﻿<!DOCTYPE html>
<html>
  <head>
    <title>绑定手机</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/绑定手机/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/绑定手机/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u2707" class="ax_default box_1">
        <div id="u2707_div" class=""></div>
        <div id="u2707_text" class="text ">
          <p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2708" class="ax_default _二级标题">
        <div id="u2708_div" class=""></div>
        <div id="u2708_text" class="text ">
          <p><span>16:18</span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u2709" class="ax_default icon">
        <img id="u2709_img" class="img " src="images/海融宝签约_个人__f501_f502_/u3.svg"/>
        <div id="u2709_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u2710" class="ax_default icon">
        <img id="u2710_img" class="img " src="images/海融宝签约_个人__f501_f502_/u4.svg"/>
        <div id="u2710_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2711" class="ax_default _文本段落">
        <div id="u2711_div" class=""></div>
        <div id="u2711_text" class="text ">
          <p><span>登陆</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2712" class="ax_default _文本段落">
        <div id="u2712_div" class=""></div>
        <div id="u2712_text" class="text ">
          <p><span>hrypt.com</span></p>
        </div>
      </div>

      <!-- Unnamed (项目logo) -->

      <!-- Unnamed (图片 ) -->
      <div id="u2714" class="ax_default _图片_">
        <img id="u2714_img" class="img " src="images/登陆主界面/u2620.svg"/>
        <div id="u2714_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2715" class="ax_default _文本段落">
        <div id="u2715_div" class=""></div>
        <div id="u2715_text" class="text ">
          <p><span>海融宝清算平台</span></p>
        </div>
      </div>
      <div id="u2713" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (矩形) -->
      <div id="u2716" class="ax_default _一级标题">
        <div id="u2716_div" class=""></div>
        <div id="u2716_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2717" class="ax_default box_1">
        <div id="u2717_div" class=""></div>
        <div id="u2717_text" class="text ">
          <p><span>微信一键登录</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2718" class="ax_default _文本段落">
        <div id="u2718_div" class=""></div>
        <div id="u2718_text" class="text ">
          <p><span style="color:#999999;">首次登录会自动进行注册，注册即为同意</span><span style="color:#33CC00;">《用户协议》《隐私政策》</span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u2719" class="ax_default icon">
        <img id="u2719_img" class="img " src="images/登陆主界面/u2637.svg"/>
        <div id="u2719_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u2720" class="ax_default icon">
        <img id="u2720_img" class="img " src="images/登陆主界面/u2624.svg"/>
        <div id="u2720_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2721" class="ax_default box_1">
        <div id="u2721_div" class=""></div>
        <div id="u2721_text" class="text ">
          <p><span>&nbsp; 请输入手机号</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2722" class="ax_default box_1">
        <div id="u2722_div" class=""></div>
        <div id="u2722_text" class="text ">
          <p><span>&nbsp; 请输入密码</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2723" class="ax_default _文本段落">
        <div id="u2723_div" class=""></div>
        <div id="u2723_text" class="text ">
          <p><span>忘记密码&gt;</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2724" class="ax_default _文本段落">
        <div id="u2724_div" class=""></div>
        <div id="u2724_text" class="text ">
          <p><span>注册登记&gt;</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2725" class="ax_default primary_button">
        <div id="u2725_div" class=""></div>
        <div id="u2725_text" class="text ">
          <p><span>登录</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2726" class="ax_default primary_button">
        <div id="u2726_div" class=""></div>
        <div id="u2726_text" class="text ">
          <p><span>快捷登录</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u2727" class="ax_default" data-left="0" data-top="0" data-width="510" data-height="1035">

        <!-- Unnamed (矩形) -->
        <div id="u2728" class="ax_default box_1">
          <div id="u2728_div" class=""></div>
          <div id="u2728_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u2729" class="ax_default" data-left="0" data-top="465" data-width="510" data-height="570">

          <!-- Unnamed (矩形) -->
          <div id="u2730" class="ax_default _形状">
            <div id="u2730_div" class=""></div>
            <div id="u2730_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (图片 ) -->
          <div id="u2731" class="ax_default _图片_">
            <img id="u2731_img" class="img " src="images/充值方式/u1461.png"/>
            <div id="u2731_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (形状) -->
          <div id="u2732" class="ax_default _文本段落">
            <img id="u2732_img" class="img " src="images/充值方式/u1462.svg"/>
            <div id="u2732_text" class="text ">
              <p><span>手机号绑定</span></p>
            </div>
          </div>

          <!-- Unnamed (线段) -->
          <div id="u2733" class="ax_default line">
            <img id="u2733_img" class="img " src="images/充值方式/u1463.svg"/>
            <div id="u2733_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2734" class="ax_default box_1">
        <div id="u2734_div" class=""></div>
        <div id="u2734_text" class="text ">
          <p><span>确认绑定</span></p>
        </div>
      </div>

      <!-- Unnamed (手机和验证码输入) -->

      <!-- Unnamed (矩形) -->
      <div id="u2736" class="ax_default _形状">
        <div id="u2736_div" class=""></div>
        <div id="u2736_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2737" class="ax_default _文本段落">
        <div id="u2737_div" class=""></div>
        <div id="u2737_text" class="text ">
          <p><span>手机号</span></p>
        </div>
      </div>

      <!-- 叫号面板按钮 (动态面板) -->
      <div id="u2738" class="ax_default" data-label="叫号面板按钮">
        <div id="u2738_state0" class="panel_state" data-label="State1" style="">
          <div id="u2738_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u2739" class="ax_default box_3">
              <div id="u2739_div" class=""></div>
              <div id="u2739_text" class="text ">
                <p><span>获取验证码</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u2738_state1" class="panel_state" data-label="State2" style="visibility: hidden;">
          <div id="u2738_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u2740" class="ax_default box_3">
              <div id="u2740_div" class=""></div>
              <div id="u2740_text" class="text ">
                <p><span>s</span></p>
              </div>
            </div>

            <!-- 叫号倒计时 (文本框) -->
            <div id="u2741" class="ax_default text_field ax_default_hidden" data-label="叫号倒计时" style="display:none; visibility: hidden">
              <div id="u2741_div" class=""></div>
              <input id="u2741_input" type="text" value="15" class="u2741_input" readonly/>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u2742" class="ax_default text_field">
        <div id="u2742_div" class=""></div>
        <input id="u2742_input" type="text" value="" class="u2742_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2743" class="ax_default _文本段落">
        <div id="u2743_div" class=""></div>
        <div id="u2743_text" class="text ">
          <p><span>134 8250 8234</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2744" class="ax_default _文本段落">
        <div id="u2744_div" class=""></div>
        <div id="u2744_text" class="text ">
          <p><span>验证码</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u2745" class="ax_default text_field">
        <div id="u2745_div" class=""></div>
        <input id="u2745_input" type="text" value="" class="u2745_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2746" class="ax_default _文本段落">
        <div id="u2746_div" class=""></div>
        <div id="u2746_text" class="text ">
          <p><span>输入短信验证码</span></p>
        </div>
      </div>
      <div id="u2735" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (矩形) -->
      <div id="u2747" class="ax_default _形状">
        <div id="u2747_div" class=""></div>
        <div id="u2747_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2748" class="ax_default box_1">
        <div id="u2748_div" class=""></div>
        <div id="u2748_text" class="text ">
          <p><span>输入新密码</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2749" class="ax_default box_1">
        <div id="u2749_div" class=""></div>
        <div id="u2749_text" class="text ">
          <p><span>再输入一次</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2750" class="ax_default box_1">
        <div id="u2750_div" class=""></div>
        <div id="u2750_text" class="text ">
          <p><span>请输入新登录密码</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2751" class="ax_default box_1">
        <div id="u2751_div" class=""></div>
        <div id="u2751_text" class="text ">
          <p><span>请再输入新登录密码</span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u2752" class="ax_default icon">
        <img id="u2752_img" class="img " src="images/登陆密码修改/u2133.svg"/>
        <div id="u2752_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u2753" class="ax_default icon">
        <img id="u2753_img" class="img " src="images/登陆密码修改/u2133.svg"/>
        <div id="u2753_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
