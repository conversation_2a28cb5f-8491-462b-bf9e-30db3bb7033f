﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:884px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u1603_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:900px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:50px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1603 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:900px;
  display:flex;
  opacity:0.49;
}
#u1603 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1603_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1604_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1604 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:20px;
  width:51px;
  height:40px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1604 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1604_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1605_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:18px;
}
#u1605 {
  border-width:0px;
  position:absolute;
  left:425px;
  top:19px;
  width:23px;
  height:18px;
  display:flex;
}
#u1605 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1605_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1606_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:16px;
}
#u1606 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:21px;
  width:26px;
  height:16px;
  display:flex;
}
#u1606 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1606_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1607_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u1607 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:50px;
  width:300px;
  height:25px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u1607 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1607_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1608 {
  position:fixed;
  left:50%;
  margin-left:-75px;
  top:50%;
  margin-top:-25px;
  visibility:hidden;
}
#u1608_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1608_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1609_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u1609 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u1609 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u1609_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1608_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u1608_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1610_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u1610 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u1610 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u1610_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1611_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u1611 {
  border-width:0px;
  position:absolute;
  left:60px;
  top:16px;
  width:80px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u1611 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1611_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1612_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u1612 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:10px;
  width:30px;
  height:30px;
  display:flex;
}
#u1612 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1612_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1613_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u1613 {
  border-width:0px;
  position:absolute;
  left:463px;
  top:50px;
  width:25px;
  height:25px;
  display:flex;
}
#u1613 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1613_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1614_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:25px;
}
#u1614 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:50px;
  width:15px;
  height:25px;
  display:flex;
}
#u1614 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1614_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1615_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:228px;
  height:11px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  text-align:center;
}
#u1615 {
  border-width:0px;
  position:absolute;
  left:136px;
  top:71px;
  width:228px;
  height:11px;
  display:flex;
  font-size:10px;
  text-align:center;
}
#u1615 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1615_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1616 {
  border-width:0px;
  position:absolute;
  left:11px;
  top:180px;
  width:488px;
  height:672px;
}
#u1616_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:488px;
  height:672px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1616_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1617 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:488px;
  height:659px;
}
#u1617_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:488px;
  height:659px;
  overflow:auto;
  -webkit-overflow-scrolling:touch;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1617_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1620_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u1620 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:106px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u1620 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1620_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1621_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u1621 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:99px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u1621 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1621_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1622_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u1622 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:127px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u1622 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1622_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1623_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:329px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1623 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:99px;
  width:329px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u1623 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1623_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1624_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u1624 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:155px;
  width:470px;
  height:2px;
  display:flex;
}
#u1624 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1624_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1625_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1625 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:131px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1625 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1625_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1626_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1626 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:127px;
  width:201px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u1626 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1626_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1628_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u1628 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:516px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u1628 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1628_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1629_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u1629 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:509px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u1629 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1629_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1630_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u1630 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:537px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u1630 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1630_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1631_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:329px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1631 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:509px;
  width:329px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u1631 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1631_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1632_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u1632 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:565px;
  width:470px;
  height:2px;
  display:flex;
}
#u1632 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1632_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1633_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1633 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:541px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1633 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1633_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1634_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1634 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:537px;
  width:201px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u1634 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1634_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1636_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u1636 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:188px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u1636 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1636_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1637_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u1637 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:181px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u1637 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1637_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1638_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u1638 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:209px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u1638 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1638_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1639_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:328px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1639 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:181px;
  width:328px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u1639 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1639_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1640_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u1640 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:237px;
  width:470px;
  height:2px;
  display:flex;
}
#u1640 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1640_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1641_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1641 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:211px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1641 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1641_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1642_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1642 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:209px;
  width:200px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u1642 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1642_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1644_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u1644 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:434px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u1644 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1644_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1645_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u1645 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:427px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u1645 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1645_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1646_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u1646 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:455px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u1646 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1646_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1647_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:328px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1647 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:427px;
  width:328px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u1647 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1647_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1648_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u1648 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:483px;
  width:470px;
  height:2px;
  display:flex;
}
#u1648 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1648_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1649_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1649 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:457px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1649 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1649_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1650_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1650 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:455px;
  width:200px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u1650 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1650_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1652_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u1652 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:352px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u1652 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1652_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1653_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u1653 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:345px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u1653 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1653_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1654_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u1654 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:373px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u1654 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1654_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1655_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:328px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1655 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:345px;
  width:328px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u1655 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1655_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1656_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u1656 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:401px;
  width:470px;
  height:2px;
  display:flex;
}
#u1656 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1656_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1657_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1657 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:375px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1657 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1657_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1658_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1658 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:373px;
  width:200px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u1658 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1658_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1660_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u1660 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:270px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u1660 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1660_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1661_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u1661 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:263px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u1661 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1661_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1662_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u1662 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:291px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u1662 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1662_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1663_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:329px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1663 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:263px;
  width:329px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u1663 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1663_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1664_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u1664 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:319px;
  width:470px;
  height:2px;
  display:flex;
}
#u1664 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1664_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1665_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1665 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:295px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1665 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1665_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1666_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1666 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:291px;
  width:201px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u1666 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1666_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1668 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1669_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:479px;
  height:80px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1669 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:479px;
  height:80px;
  display:flex;
}
#u1669 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1669_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1670_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1670 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:48px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1670 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1670_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1671_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u1671 {
  border-width:0px;
  position:absolute;
  left:92px;
  top:47px;
  width:19px;
  height:25px;
  display:flex;
}
#u1671 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1671_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1672_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1672 {
  border-width:0px;
  position:absolute;
  left:257px;
  top:48px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1672 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1672_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1673_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u1673 {
  border-width:0px;
  position:absolute;
  left:308px;
  top:47px;
  width:19px;
  height:25px;
  display:flex;
}
#u1673 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1673_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1674_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1674 {
  border-width:0px;
  position:absolute;
  left:152px;
  top:48px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1674 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1674_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1675_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u1675 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:47px;
  width:19px;
  height:25px;
  display:flex;
}
#u1675 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1675_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1676 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1677_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:425px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(201, 201, 201, 1);
  border-radius:75px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1677 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:7px;
  width:425px;
  height:34px;
  display:flex;
}
#u1677 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1677_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1678_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:14px;
}
#u1678 {
  border-width:0px;
  position:absolute;
  left:39px;
  top:17px;
  width:16px;
  height:14px;
  display:flex;
}
#u1678 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1678_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1679_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:372px;
  height:29px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u1679 {
  border-width:0px;
  position:absolute;
  left:62px;
  top:10px;
  width:372px;
  height:29px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u1679 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1679_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1680_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:17px;
}
#u1680 {
  border-width:0px;
  position:absolute;
  left:424px;
  top:16px;
  width:19px;
  height:17px;
  display:flex;
}
#u1680 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1680_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1681_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1681 {
  border-width:0px;
  position:absolute;
  left:364px;
  top:49px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1681 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1681_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1682_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u1682 {
  border-width:0px;
  position:absolute;
  left:413px;
  top:48px;
  width:19px;
  height:25px;
  display:flex;
}
#u1682 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1682_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1684_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u1684 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:680px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u1684 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1684_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1685_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u1685 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:673px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u1685 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1685_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1686_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u1686 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:701px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u1686 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1686_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1687_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:329px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1687 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:673px;
  width:329px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u1687 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1687_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1688_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u1688 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:729px;
  width:470px;
  height:2px;
  display:flex;
}
#u1688 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1688_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1689_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1689 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:705px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1689 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1689_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1690_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1690 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:701px;
  width:201px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u1690 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1690_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1692_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u1692 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:598px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u1692 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1692_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1693_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u1693 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:591px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u1693 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1693_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1694_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u1694 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:619px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u1694 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1694_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1695_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:328px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1695 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:591px;
  width:328px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u1695 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1695_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1696_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u1696 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:647px;
  width:470px;
  height:2px;
  display:flex;
}
#u1696 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1696_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1697_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1697 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:621px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1697 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1697_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1698_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1698 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:619px;
  width:200px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u1698 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1698_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1700_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u1700 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:844px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u1700 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1700_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1701_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u1701 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:837px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u1701 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1701_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1702_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u1702 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:865px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u1702 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1702_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1703_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:329px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1703 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:837px;
  width:329px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u1703 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1703_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1704_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u1704 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:893px;
  width:470px;
  height:2px;
  display:flex;
}
#u1704 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1704_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1705_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1705 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:869px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1705 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1705_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1706_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1706 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:865px;
  width:201px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u1706 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1706_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1708_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u1708 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:762px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u1708 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1708_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1709_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u1709 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:755px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u1709 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1709_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1710_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u1710 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:783px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u1710 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1710_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1711_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:328px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1711 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:755px;
  width:328px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u1711 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1711_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1712_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u1712 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:811px;
  width:470px;
  height:2px;
  display:flex;
}
#u1712 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1712_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1713_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1713 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:785px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1713 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1713_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1714_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1714 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:783px;
  width:200px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u1714 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1714_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1616_state1 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:488px;
  height:672px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u1616_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1716_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u1716 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:106px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u1716 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1716_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1717_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u1717 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:99px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u1717 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1717_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1718_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u1718 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:127px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u1718 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1718_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1719_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:329px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1719 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:99px;
  width:329px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u1719 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1719_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1720_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u1720 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:155px;
  width:470px;
  height:2px;
  display:flex;
}
#u1720 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1720_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1721_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1721 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:131px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1721 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1721_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1722_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1722 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:127px;
  width:201px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u1722 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1722_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1724 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1725_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:479px;
  height:80px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1725 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:479px;
  height:80px;
  display:flex;
}
#u1725 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1725_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1726_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1726 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:48px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1726 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1726_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1727_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u1727 {
  border-width:0px;
  position:absolute;
  left:92px;
  top:47px;
  width:19px;
  height:25px;
  display:flex;
}
#u1727 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1727_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1728_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1728 {
  border-width:0px;
  position:absolute;
  left:257px;
  top:48px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1728 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1728_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1729_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u1729 {
  border-width:0px;
  position:absolute;
  left:308px;
  top:47px;
  width:19px;
  height:25px;
  display:flex;
}
#u1729 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1729_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1730_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1730 {
  border-width:0px;
  position:absolute;
  left:152px;
  top:48px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1730 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1730_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1731_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u1731 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:47px;
  width:19px;
  height:25px;
  display:flex;
}
#u1731 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1731_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1732 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1733_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:425px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(201, 201, 201, 1);
  border-radius:75px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1733 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:7px;
  width:425px;
  height:34px;
  display:flex;
}
#u1733 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1733_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1734_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:14px;
}
#u1734 {
  border-width:0px;
  position:absolute;
  left:39px;
  top:17px;
  width:16px;
  height:14px;
  display:flex;
}
#u1734 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1734_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1735_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:372px;
  height:29px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u1735 {
  border-width:0px;
  position:absolute;
  left:62px;
  top:10px;
  width:372px;
  height:29px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u1735 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1735_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1736_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:17px;
}
#u1736 {
  border-width:0px;
  position:absolute;
  left:424px;
  top:16px;
  width:19px;
  height:17px;
  display:flex;
}
#u1736 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1736_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1737_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1737 {
  border-width:0px;
  position:absolute;
  left:364px;
  top:49px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1737 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1737_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1738_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u1738 {
  border-width:0px;
  position:absolute;
  left:413px;
  top:48px;
  width:19px;
  height:25px;
  display:flex;
}
#u1738 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1738_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1616_state2 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:488px;
  height:672px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u1616_state2_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1740 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1741_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:479px;
  height:80px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1741 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:479px;
  height:80px;
  display:flex;
}
#u1741 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1741_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1742_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1742 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:48px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1742 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1742_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1743_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u1743 {
  border-width:0px;
  position:absolute;
  left:92px;
  top:47px;
  width:19px;
  height:25px;
  display:flex;
}
#u1743 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1743_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1744_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1744 {
  border-width:0px;
  position:absolute;
  left:257px;
  top:48px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1744 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1744_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1745_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u1745 {
  border-width:0px;
  position:absolute;
  left:308px;
  top:47px;
  width:19px;
  height:25px;
  display:flex;
}
#u1745 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1745_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1746_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1746 {
  border-width:0px;
  position:absolute;
  left:152px;
  top:48px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1746 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1746_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1747_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u1747 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:47px;
  width:19px;
  height:25px;
  display:flex;
}
#u1747 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1747_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1748 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1749_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:425px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(201, 201, 201, 1);
  border-radius:75px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1749 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:7px;
  width:425px;
  height:34px;
  display:flex;
}
#u1749 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1749_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1750_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:14px;
}
#u1750 {
  border-width:0px;
  position:absolute;
  left:39px;
  top:17px;
  width:16px;
  height:14px;
  display:flex;
}
#u1750 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1750_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1751_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:372px;
  height:29px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u1751 {
  border-width:0px;
  position:absolute;
  left:62px;
  top:10px;
  width:372px;
  height:29px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u1751 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1751_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1752_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:17px;
}
#u1752 {
  border-width:0px;
  position:absolute;
  left:424px;
  top:16px;
  width:19px;
  height:17px;
  display:flex;
}
#u1752 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1752_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1753_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1753 {
  border-width:0px;
  position:absolute;
  left:364px;
  top:49px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1753 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1753_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1754_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u1754 {
  border-width:0px;
  position:absolute;
  left:413px;
  top:48px;
  width:19px;
  height:25px;
  display:flex;
}
#u1754 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1754_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1756_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u1756 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:111px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u1756 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1756_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1757_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u1757 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:104px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u1757 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1757_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1758_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u1758 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:132px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u1758 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1758_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1759_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:328px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1759 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:104px;
  width:328px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u1759 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1759_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1760_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u1760 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:160px;
  width:470px;
  height:2px;
  display:flex;
}
#u1760 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1760_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1761_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1761 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:134px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1761 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1761_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1762_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1762 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:132px;
  width:200px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u1762 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1762_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1616_state3 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:488px;
  height:672px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u1616_state3_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1764 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1765_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:479px;
  height:80px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1765 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:479px;
  height:80px;
  display:flex;
}
#u1765 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1765_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1766_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1766 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:48px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1766 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1766_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1767_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u1767 {
  border-width:0px;
  position:absolute;
  left:92px;
  top:47px;
  width:19px;
  height:25px;
  display:flex;
}
#u1767 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1767_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1768_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1768 {
  border-width:0px;
  position:absolute;
  left:257px;
  top:48px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1768 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1768_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1769_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u1769 {
  border-width:0px;
  position:absolute;
  left:308px;
  top:47px;
  width:19px;
  height:25px;
  display:flex;
}
#u1769 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1769_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1770_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1770 {
  border-width:0px;
  position:absolute;
  left:152px;
  top:48px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1770 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1770_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1771_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u1771 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:47px;
  width:19px;
  height:25px;
  display:flex;
}
#u1771 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1771_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1772 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1773_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:425px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(201, 201, 201, 1);
  border-radius:75px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1773 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:7px;
  width:425px;
  height:34px;
  display:flex;
}
#u1773 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1773_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1774_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:14px;
}
#u1774 {
  border-width:0px;
  position:absolute;
  left:39px;
  top:17px;
  width:16px;
  height:14px;
  display:flex;
}
#u1774 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1774_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1775_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:372px;
  height:29px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u1775 {
  border-width:0px;
  position:absolute;
  left:62px;
  top:10px;
  width:372px;
  height:29px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u1775 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1775_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1776_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:17px;
}
#u1776 {
  border-width:0px;
  position:absolute;
  left:424px;
  top:16px;
  width:19px;
  height:17px;
  display:flex;
}
#u1776 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1776_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1777_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1777 {
  border-width:0px;
  position:absolute;
  left:364px;
  top:49px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1777 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1777_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1778_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u1778 {
  border-width:0px;
  position:absolute;
  left:413px;
  top:48px;
  width:19px;
  height:25px;
  display:flex;
}
#u1778 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1778_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1780_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u1780 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:193px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u1780 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1780_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1781_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u1781 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:186px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u1781 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1781_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1782_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u1782 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:214px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u1782 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1782_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1783_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:329px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1783 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:186px;
  width:329px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u1783 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1783_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1784_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u1784 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:242px;
  width:470px;
  height:2px;
  display:flex;
}
#u1784 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1784_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1785_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1785 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:218px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1785 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1785_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1786_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1786 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:214px;
  width:201px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u1786 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1786_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1788_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u1788 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:111px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u1788 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1788_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1789_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u1789 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:104px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u1789 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1789_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1790_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u1790 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:132px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u1790 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1790_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1791_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:329px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1791 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:104px;
  width:329px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u1791 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1791_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1792_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u1792 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:160px;
  width:470px;
  height:2px;
  display:flex;
}
#u1792 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1792_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1793_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1793 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:136px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1793 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1793_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1794_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1794 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:132px;
  width:201px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u1794 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1794_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1796_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u1796 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:275px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u1796 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1796_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1797_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u1797 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:268px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u1797 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1797_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1798_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u1798 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:296px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u1798 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1798_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1799_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:329px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1799 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:268px;
  width:329px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u1799 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1799_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1800_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u1800 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:324px;
  width:470px;
  height:2px;
  display:flex;
}
#u1800 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1800_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1801_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1801 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:300px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1801 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1801_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1802_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1802 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:296px;
  width:201px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u1802 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1802_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1804_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u1804 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:357px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u1804 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1804_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1805_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u1805 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:350px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u1805 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1805_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1806_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u1806 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:378px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u1806 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1806_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1807_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:329px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1807 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:350px;
  width:329px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u1807 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1807_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1808_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u1808 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:406px;
  width:470px;
  height:2px;
  display:flex;
}
#u1808 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1808_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1809_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1809 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:382px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1809 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1809_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1810_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1810 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:378px;
  width:201px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u1810 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1810_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1616_state4 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:488px;
  height:672px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u1616_state4_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1812 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1813_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:479px;
  height:80px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1813 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:479px;
  height:80px;
  display:flex;
}
#u1813 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1813_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1814_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1814 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:48px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1814 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1814_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1815_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u1815 {
  border-width:0px;
  position:absolute;
  left:92px;
  top:47px;
  width:19px;
  height:25px;
  display:flex;
}
#u1815 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1815_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1816_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1816 {
  border-width:0px;
  position:absolute;
  left:257px;
  top:48px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1816 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1816_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1817_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u1817 {
  border-width:0px;
  position:absolute;
  left:308px;
  top:47px;
  width:19px;
  height:25px;
  display:flex;
}
#u1817 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1817_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1818_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1818 {
  border-width:0px;
  position:absolute;
  left:152px;
  top:48px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1818 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1818_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1819_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u1819 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:47px;
  width:19px;
  height:25px;
  display:flex;
}
#u1819 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1819_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1820 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1821_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:425px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(201, 201, 201, 1);
  border-radius:75px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1821 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:7px;
  width:425px;
  height:34px;
  display:flex;
}
#u1821 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1821_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1822_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:14px;
}
#u1822 {
  border-width:0px;
  position:absolute;
  left:39px;
  top:17px;
  width:16px;
  height:14px;
  display:flex;
}
#u1822 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1822_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1823_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:372px;
  height:29px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u1823 {
  border-width:0px;
  position:absolute;
  left:62px;
  top:10px;
  width:372px;
  height:29px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u1823 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1823_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1824_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:17px;
}
#u1824 {
  border-width:0px;
  position:absolute;
  left:424px;
  top:16px;
  width:19px;
  height:17px;
  display:flex;
}
#u1824 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1824_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1825_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1825 {
  border-width:0px;
  position:absolute;
  left:364px;
  top:49px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u1825 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1825_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1826_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u1826 {
  border-width:0px;
  position:absolute;
  left:413px;
  top:48px;
  width:19px;
  height:25px;
  display:flex;
}
#u1826 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1826_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1828_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u1828 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:193px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u1828 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1828_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1829_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u1829 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:186px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u1829 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1829_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1830_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u1830 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:214px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u1830 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1830_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1831_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:328px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1831 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:186px;
  width:328px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u1831 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1831_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1832_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u1832 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:242px;
  width:470px;
  height:2px;
  display:flex;
}
#u1832 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1832_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1833_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1833 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:216px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1833 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1833_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1834_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1834 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:214px;
  width:200px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u1834 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1834_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1836_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u1836 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:111px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u1836 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1836_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1837_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u1837 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:104px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u1837 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1837_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1838_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u1838 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:132px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u1838 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1838_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1839_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:328px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1839 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:104px;
  width:328px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u1839 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1839_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1840_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u1840 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:160px;
  width:470px;
  height:2px;
  display:flex;
}
#u1840 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1840_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1841_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1841 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:134px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1841 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1841_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1842_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1842 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:132px;
  width:200px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u1842 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1842_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1844_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u1844 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:275px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u1844 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1844_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1845_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u1845 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:268px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u1845 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1845_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1846_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u1846 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:296px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u1846 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1846_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1847_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:328px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1847 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:268px;
  width:328px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u1847 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1847_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1848_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u1848 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:324px;
  width:470px;
  height:2px;
  display:flex;
}
#u1848 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1848_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1849_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1849 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:298px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1849 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1849_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1850_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1850 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:296px;
  width:200px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u1850 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1850_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1852_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u1852 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:357px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u1852 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1852_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1853_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u1853 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:350px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u1853 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1853_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1854_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u1854 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:378px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u1854 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1854_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1855_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:328px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1855 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:350px;
  width:328px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u1855 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1855_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1856_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u1856 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:406px;
  width:470px;
  height:2px;
  display:flex;
}
#u1856 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1856_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1857_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1857 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:380px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u1857 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1857_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1858_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1858 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:378px;
  width:200px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u1858 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1858_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1859 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1860_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
}
#u1860 {
  border-width:0px;
  position:absolute;
  left:109px;
  top:130px;
  width:98px;
  height:40px;
  display:flex;
  font-size:18px;
  color:#555555;
}
#u1860 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1860_img.selected {
}
#u1860.selected {
}
#u1860_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1861 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1862_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
}
#u1862 {
  border-width:0px;
  position:absolute;
  left:206px;
  top:130px;
  width:98px;
  height:40px;
  display:flex;
  font-size:18px;
  color:#555555;
}
#u1862 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1862_img.selected {
}
#u1862.selected {
}
#u1862_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1863 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1864_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
}
#u1864 {
  border-width:0px;
  position:absolute;
  left:11px;
  top:130px;
  width:98px;
  height:40px;
  display:flex;
  font-size:18px;
  color:#555555;
}
#u1864 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1864_img.selected {
}
#u1864.selected {
}
#u1864_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1865 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1866_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
}
#u1866 {
  border-width:0px;
  position:absolute;
  left:304px;
  top:130px;
  width:98px;
  height:40px;
  display:flex;
  font-size:18px;
  color:#555555;
}
#u1866 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1866_img.selected {
}
#u1866.selected {
}
#u1866_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1867 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1868_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
}
#u1868 {
  border-width:0px;
  position:absolute;
  left:401px;
  top:130px;
  width:98px;
  height:40px;
  display:flex;
  font-size:18px;
  color:#555555;
}
#u1868 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1868_img.selected {
}
#u1868.selected {
}
#u1868_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1869_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:191px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1869 {
  border-width:0px;
  position:absolute;
  left:556px;
  top:66px;
  width:191px;
  height:30px;
  display:flex;
}
#u1869 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1869_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1870_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:328px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1870 {
  border-width:0px;
  position:absolute;
  left:556px;
  top:110px;
  width:328px;
  height:40px;
  display:flex;
}
#u1870 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1870_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
