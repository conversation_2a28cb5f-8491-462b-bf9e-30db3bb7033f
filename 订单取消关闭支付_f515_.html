﻿<!DOCTYPE html>
<html>
  <head>
    <title>订单取消关闭支付(F515)</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/订单取消关闭支付_f515_/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/订单取消关闭支付_f515_/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (基础app框架(H5)) -->

      <!-- Unnamed (矩形) -->
      <div id="u970" class="ax_default box_1">
        <div id="u970_div" class=""></div>
        <div id="u970_text" class="text ">
          <p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u971" class="ax_default _二级标题">
        <div id="u971_div" class=""></div>
        <div id="u971_text" class="text ">
          <p><span>16:18</span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u972" class="ax_default icon">
        <img id="u972_img" class="img " src="images/海融宝签约_个人__f501_f502_/u3.svg"/>
        <div id="u972_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u973" class="ax_default icon">
        <img id="u973_img" class="img " src="images/海融宝签约_个人__f501_f502_/u4.svg"/>
        <div id="u973_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u974" class="ax_default _文本段落">
        <div id="u974_div" class=""></div>
        <div id="u974_text" class="text ">
          <p><span>关闭支付订单</span></p>
        </div>
      </div>

      <!-- 操作状态 (动态面板) -->
      <div id="u975" class="ax_default ax_default_hidden" data-label="操作状态" style="display:none; visibility: hidden">
        <div id="u975_state0" class="panel_state" data-label="操作成功" style="">
          <div id="u975_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u976" class="ax_default box_3">
              <div id="u976_div" class=""></div>
              <div id="u976_text" class="text ">
                <p><span>操作成功！</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u975_state1" class="panel_state" data-label="操作失败" style="visibility: hidden;">
          <div id="u975_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u977" class="ax_default box_3">
              <div id="u977_div" class=""></div>
              <div id="u977_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u978" class="ax_default _文本段落">
              <div id="u978_div" class=""></div>
              <div id="u978_text" class="text ">
                <p><span>操作失败！</span></p>
              </div>
            </div>

            <!-- Unnamed (图片 ) -->
            <div id="u979" class="ax_default _图片_">
              <img id="u979_img" class="img " src="images/海融宝签约_个人__f501_f502_/u10.png"/>
              <div id="u979_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u980" class="ax_default _图片_">
        <img id="u980_img" class="img " src="images/海融宝签约_个人__f501_f502_/u11.png"/>
        <div id="u980_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u981" class="ax_default icon">
        <img id="u981_img" class="img " src="images/海融宝签约_个人__f501_f502_/u12.svg"/>
        <div id="u981_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u982" class="ax_default _文本段落">
        <div id="u982_div" class=""></div>
        <div id="u982_text" class="text ">
          <p><span>hrypt.com</span></p>
        </div>
      </div>
      <div id="u969" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (矩形) -->
      <div id="u983" class="ax_default primary_button">
        <div id="u983_div" class=""></div>
        <div id="u983_text" class="text ">
          <p><span>关闭支付</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u984" class="ax_default _文本段落">
        <div id="u984_div" class=""></div>
        <div id="u984_text" class="text ">
          <p style="font-size:20px;"><span>F515 商户订单关单</span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span>节点属性&nbsp; &nbsp;&nbsp; 中文描述&nbsp; &nbsp;&nbsp; 数据类型&nbsp; &nbsp;&nbsp; 必输&nbsp; &nbsp;&nbsp; 说明</span></p><p style="font-size:13px;"><span>txType&nbsp; &nbsp;&nbsp; 交易类型&nbsp; &nbsp;&nbsp; char(2)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 01-入金，02-结算，03-出金</span></p><p style="font-size:13px;"><span>custId&nbsp; &nbsp;&nbsp; 客户ID&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 01和03只有本方客户ID；02时需要输入付方客户ID</span></p><p style="font-size:13px;"><span>platOrderNo&nbsp; &nbsp;&nbsp; 平台订单号&nbsp; &nbsp;&nbsp; char(100)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 平台冻结、解冻、出入金、结算请求生成的唯一标识</span></p><p style="font-size:13px;"><span>platSubOrderNo&nbsp; &nbsp;&nbsp; 平台结算子订单号&nbsp; &nbsp;&nbsp; char(100)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; 交易类型为02-结算时拆单场景必输</span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span>节点属性&nbsp; &nbsp;&nbsp; 中文描述&nbsp; &nbsp;&nbsp; 数据类型&nbsp; &nbsp;&nbsp; 必输&nbsp; &nbsp;&nbsp; 说明</span></p><p style="font-size:13px;"><span>bdlStaCd&nbsp; &nbsp;&nbsp; 业务处理状态代码&nbsp; &nbsp;&nbsp; char(4)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; PR00-成功，PR01-失败，PR02-处理中</span></p><p style="font-size:13px;"><span>failReason&nbsp; &nbsp;&nbsp; 失败原因&nbsp; &nbsp;&nbsp; char(500)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; 当业务处理状态代码为PR01-失败时返回</span></p><p style="font-size:13px;"><span>txDealTime&nbsp; &nbsp;&nbsp; 业务处理时间&nbsp; &nbsp;&nbsp; char(14)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; yyyyMMddHHmmss</span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span><br></span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u985" class="ax_default _形状">
        <div id="u985_div" class=""></div>
        <div id="u985_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u986" class="ax_default" data-left="24" data-top="191" data-width="440" data-height="30">

        <!-- Unnamed (矩形) -->
        <div id="u987" class="ax_default _文本段落">
          <div id="u987_div" class=""></div>
          <div id="u987_text" class="text ">
            <p><span>付款单号</span></p>
          </div>
        </div>

        <!-- Unnamed (文本框) -->
        <div id="u988" class="ax_default text_field">
          <div id="u988_div" class=""></div>
          <input id="u988_input" type="text" value="8869825504200002674202505182" class="u988_input"/>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u989" class="ax_default _文本段落">
        <div id="u989_div" class=""></div>
        <div id="u989_text" class="text ">
          <p><span>关闭支付订单（简）</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u990" class="ax_default" data-left="24" data-top="151" data-width="440" data-height="30">

        <!-- Unnamed (矩形) -->
        <div id="u991" class="ax_default _文本段落">
          <div id="u991_div" class=""></div>
          <div id="u991_text" class="text ">
            <p><span>订单单号</span></p>
          </div>
        </div>

        <!-- Unnamed (文本框) -->
        <div id="u992" class="ax_default text_field">
          <div id="u992_div" class=""></div>
          <input id="u992_input" type="text" value="4200002674202505182886982550" class="u992_input"/>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u993" class="ax_default" data-left="24" data-top="235" data-width="440" data-height="30">

        <!-- Unnamed (矩形) -->
        <div id="u994" class="ax_default _文本段落">
          <div id="u994_div" class=""></div>
          <div id="u994_text" class="text ">
            <p><span>付款类型</span></p>
          </div>
        </div>

        <!-- Unnamed (文本框) -->
        <div id="u995" class="ax_default text_field">
          <div id="u995_div" class=""></div>
          <input id="u995_input" type="text" value="充值/提现/交易" class="u995_input"/>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u996" class="ax_default _文本段落">
        <div id="u996_div" class=""></div>
        <div id="u996_text" class="text ">
          <p><span>说明：本功能是对充值、提现、交易等未支付完成的订单进行关闭。基于业务操作的订单所对应的付款单号进行关闭。</span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u997" class="ax_default icon">
        <img id="u997_img" class="img " src="images/子钱包交易付款_f511_/u879.svg"/>
        <div id="u997_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u998" class="ax_default _文本段落">
        <div id="u998_div" class=""></div>
        <div id="u998_text" class="text ">
          <p><span style="color:#000000;">充值：01-入金</span></p><p><span style="color:#000000;">提现：</span><span>03-出金</span></p><p><span style="color:#000000;">交易：</span><span>02-结算</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u999" class="ax_default" data-left="45" data-top="281" data-width="426" data-height="18">

        <!-- Unnamed (矩形) -->
        <div id="u1000" class="ax_default _文本段落">
          <div id="u1000_div" class=""></div>
          <div id="u1000_text" class="text ">
            <p><span>交易渠道</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1001" class="ax_default _文本段落">
          <div id="u1001_div" class=""></div>
          <div id="u1001_text" class="text ">
            <p><span>数优联</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u1002" class="ax_default" data-left="521" data-top="10" data-width="426" data-height="18">

        <!-- Unnamed (矩形) -->
        <div id="u1003" class="ax_default _文本段落">
          <div id="u1003_div" class=""></div>
          <div id="u1003_text" class="text ">
            <p><span>交易通道</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u1004" class="ax_default _文本段落">
          <div id="u1004_div" class=""></div>
          <div id="u1004_text" class="text ">
            <p><span>数优联/数市联/数蛋联/数药联/数牛联/数菜联</span></p>
          </div>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
