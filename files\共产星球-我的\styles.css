﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:510px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u6460_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:896px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:50px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6460 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:896px;
  display:flex;
  opacity:0.49;
}
#u6460 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6460_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6461_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:448px;
}
#u6461 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:32px;
  width:510px;
  height:448px;
  display:flex;
}
#u6461 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6461_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6462_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:664px;
}
#u6462 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:567px;
  width:510px;
  height:664px;
  display:flex;
}
#u6462 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6462_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6463 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u6464 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u6465_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u6465 {
  border-width:0px;
  position:absolute;
  left:60px;
  top:480px;
  width:150px;
  height:18px;
  display:flex;
  font-size:16px;
}
#u6465 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u6465_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6466_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u6466 {
  border-width:0px;
  position:absolute;
  left:195px;
  top:487px;
  width:48px;
  height:14px;
  display:flex;
  font-size:12px;
}
#u6466 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u6466_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6467_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:13px;
  height:21px;
}
#u6467 {
  border-width:0px;
  position:absolute;
  left:478px;
  top:479px;
  width:13px;
  height:21px;
  display:flex;
}
#u6467 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6467_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6468_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u6468 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:474px;
  width:30px;
  height:30px;
  display:flex;
}
#u6468 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6468_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6469 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u6470 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u6471_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u6471 {
  border-width:0px;
  position:absolute;
  left:60px;
  top:532px;
  width:150px;
  height:18px;
  display:flex;
  font-size:16px;
}
#u6471 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u6471_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6472_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u6472 {
  border-width:0px;
  position:absolute;
  left:195px;
  top:539px;
  width:48px;
  height:14px;
  display:flex;
  font-size:12px;
}
#u6472 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u6472_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6473_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:13px;
  height:21px;
}
#u6473 {
  border-width:0px;
  position:absolute;
  left:479px;
  top:531px;
  width:13px;
  height:21px;
  display:flex;
}
#u6473 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6473_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6474_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u6474 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:526px;
  width:30px;
  height:30px;
  display:flex;
}
#u6474 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6474_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
