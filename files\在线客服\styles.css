﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:510px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u5038_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:900px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:50px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u5038 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:900px;
  display:flex;
  opacity:0.49;
}
#u5038 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5038_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5039 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u5040 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u5041_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u5041 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:834px;
  width:26px;
  height:25px;
  display:flex;
}
#u5041 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5041_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5042_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u5042 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:860px;
  width:26px;
  height:14px;
  display:flex;
  text-align:center;
}
#u5042 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5042_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5043 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u5044_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u5044 {
  border-width:0px;
  position:absolute;
  left:130px;
  top:834px;
  width:26px;
  height:25px;
  display:flex;
}
#u5044 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5044_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5045_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u5045 {
  border-width:0px;
  position:absolute;
  left:130px;
  top:860px;
  width:26px;
  height:14px;
  display:flex;
  text-align:center;
}
#u5045 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5045_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5046 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u5047_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u5047 {
  border-width:0px;
  position:absolute;
  left:345px;
  top:834px;
  width:26px;
  height:25px;
  display:flex;
}
#u5047 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5047_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5048_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  background-image:url('../../images/平台首页/u2795.png');
  background-repeat:no-repeat;
  background-size:200px 200px;
  background-position: left top;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u5048 {
  border-width:0px;
  position:absolute;
  left:345px;
  top:860px;
  width:26px;
  height:14px;
  display:flex;
  text-align:center;
}
#u5048 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5048_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5049 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u5050_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u5050 {
  border-width:0px;
  position:absolute;
  left:452px;
  top:834px;
  width:26px;
  height:25px;
  display:flex;
}
#u5050 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5050_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5051_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u5051 {
  border-width:0px;
  position:absolute;
  left:452px;
  top:860px;
  width:26px;
  height:14px;
  display:flex;
  text-align:center;
}
#u5051 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5051_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5052 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u5053_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u5053 {
  border-width:0px;
  position:absolute;
  left:237px;
  top:834px;
  width:26px;
  height:25px;
  display:flex;
}
#u5053 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5053_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5054_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:44px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u5054 {
  border-width:0px;
  position:absolute;
  left:228px;
  top:860px;
  width:44px;
  height:14px;
  display:flex;
  text-align:center;
}
#u5054 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5054_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5055_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 0, 0, 1);
  box-sizing:border-box;
  border-width:2px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:75px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u5055 {
  border-width:0px;
  position:absolute;
  left:363px;
  top:834px;
  width:21px;
  height:15px;
  display:flex;
  color:#FFFFFF;
}
#u5055 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5055_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5056_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u5056 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:20px;
  width:51px;
  height:40px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u5056 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5056_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5057_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:18px;
}
#u5057 {
  border-width:0px;
  position:absolute;
  left:425px;
  top:19px;
  width:23px;
  height:18px;
  display:flex;
}
#u5057 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5057_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5058_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:16px;
}
#u5058 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:21px;
  width:26px;
  height:16px;
  display:flex;
}
#u5058 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5058_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5059_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:25px;
}
#u5059 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:50px;
  width:24px;
  height:25px;
  display:flex;
}
#u5059 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5059_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5060_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u5060 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:49px;
  width:300px;
  height:25px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u5060 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5060_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5061 {
  position:fixed;
  left:50%;
  margin-left:-75px;
  top:50%;
  margin-top:-25px;
  visibility:hidden;
}
#u5061_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u5061_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u5062_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u5062 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u5062 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u5062_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5061_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u5061_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u5063_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u5063 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u5063 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u5063_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5064_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u5064 {
  border-width:0px;
  position:absolute;
  left:60px;
  top:16px;
  width:80px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u5064 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5064_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u5065_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u5065 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:10px;
  width:30px;
  height:30px;
  display:flex;
}
#u5065 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5065_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5066_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:228px;
  height:11px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  text-align:center;
}
#u5066 {
  border-width:0px;
  position:absolute;
  left:136px;
  top:71px;
  width:228px;
  height:11px;
  display:flex;
  font-size:10px;
  text-align:center;
}
#u5066 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5066_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5067 {
  border-width:0px;
  position:absolute;
  left:4px;
  top:215px;
  width:492px;
  height:603px;
}
#u5067_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:492px;
  height:603px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u5067_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u5068_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:23px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u5068 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:123px;
  width:131px;
  height:23px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u5068 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5068_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5069_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:453px;
  height:284px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
}
#u5069 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:216px;
  width:453px;
  height:284px;
  display:flex;
  font-size:20px;
}
#u5069 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5069_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5070_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:446px;
  height:23px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#999999;
}
#u5070 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:226px;
  width:446px;
  height:23px;
  display:flex;
  font-size:16px;
  color:#999999;
}
#u5070 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5070_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5071_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#999999;
}
#u5071 {
  border-width:0px;
  position:absolute;
  left:426px;
  top:474px;
  width:45px;
  height:15px;
  display:flex;
  color:#999999;
}
#u5071 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5071_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5072_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:453px;
  height:72px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u5072 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:22px;
  width:453px;
  height:72px;
  display:flex;
  font-size:16px;
}
#u5072 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5072_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5074 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u5075_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:45px;
}
#u5075 {
  border-width:0px;
  position:absolute;
  left:43px;
  top:369px;
  width:45px;
  height:45px;
  display:flex;
  color:#7F7F7F;
}
#u5075 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5075_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5076_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:151px;
  height:42px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#7F7F7F;
  text-align:left;
}
#u5076 {
  border-width:0px;
  position:absolute;
  left:97px;
  top:371px;
  width:151px;
  height:42px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#7F7F7F;
  text-align:left;
}
#u5076 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5076_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5077 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:369px;
  width:210px;
  height:120px;
  visibility:hidden;
}
#u5077_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:210px;
  height:120px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u5077_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u5078_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:120px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u5078 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:120px;
  display:flex;
  font-size:18px;
}
#u5078 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5078_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5079_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u5079 {
  border-width:0px;
  position:absolute;
  left:179px;
  top:2px;
  width:23px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u5079 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5079_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5080_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:2px;
}
#u5080 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:200px;
  height:1px;
  display:flex;
  font-size:18px;
}
#u5080 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5080_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5081_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:165px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  text-align:center;
}
#u5081 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:12px;
  width:165px;
  height:21px;
  display:flex;
  font-size:18px;
  text-align:center;
}
#u5081 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5081_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5082_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:2px;
}
#u5082 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:80px;
  width:200px;
  height:1px;
  display:flex;
  font-size:18px;
}
#u5082 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5082_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5083_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:173px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  text-align:center;
}
#u5083 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:50px;
  width:173px;
  height:21px;
  display:flex;
  font-size:18px;
  text-align:center;
}
#u5083 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5083_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5084_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u5084 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:85px;
  width:120px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u5084 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5084_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5085 label {
  left:0px;
  width:100%;
}
#u5085_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:2px;
  width:18px;
  height:18px;
}
#u5085 {
  border-width:0px;
  position:absolute;
  left:146px;
  top:181px;
  width:106px;
  height:21px;
  display:flex;
  font-size:18px;
}
#u5085 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u5085_img.selected {
}
#u5085.selected {
}
#u5085_img.disabled {
}
#u5085.disabled {
}
#u5085_text {
  border-width:0px;
  position:absolute;
  left:20px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u5085_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u5086 label {
  left:0px;
  width:100%;
}
#u5086_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:2px;
  width:18px;
  height:18px;
}
#u5086 {
  border-width:0px;
  position:absolute;
  left:265px;
  top:181px;
  width:99px;
  height:21px;
  display:flex;
  font-size:18px;
}
#u5086 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u5086_img.selected {
}
#u5086.selected {
}
#u5086_img.disabled {
}
#u5086.disabled {
}
#u5086_text {
  border-width:0px;
  position:absolute;
  left:20px;
  top:0px;
  width:77px;
  word-wrap:break-word;
  text-transform:none;
}
#u5086_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u5087 label {
  left:0px;
  width:100%;
}
#u5087_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:2px;
  width:18px;
  height:18px;
}
#u5087 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:181px;
  width:106px;
  height:21px;
  display:flex;
  font-size:18px;
}
#u5087 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u5087_img.selected {
}
#u5087.selected {
}
#u5087_img.disabled {
}
#u5087.disabled {
}
#u5087_text {
  border-width:0px;
  position:absolute;
  left:20px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u5087_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u5088 label {
  left:0px;
  width:100%;
}
#u5088_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:2px;
  width:18px;
  height:18px;
}
#u5088 {
  border-width:0px;
  position:absolute;
  left:376px;
  top:181px;
  width:99px;
  height:21px;
  display:flex;
  font-size:18px;
}
#u5088 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u5088_img.selected {
}
#u5088.selected {
}
#u5088_img.disabled {
}
#u5088.disabled {
}
#u5088_text {
  border-width:0px;
  position:absolute;
  left:20px;
  top:0px;
  width:77px;
  word-wrap:break-word;
  text-transform:none;
}
#u5088_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u5089_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:166px;
  height:40px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
}
#u5089 {
  border-width:0px;
  position:absolute;
  left:175px;
  top:515px;
  width:166px;
  height:40px;
  display:flex;
  font-size:20px;
}
#u5089 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5089_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5067_state1 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:492px;
  height:603px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u5067_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u5090_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:297px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:28px;
  color:#000000;
  text-align:left;
}
#u5090 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:45px;
  width:297px;
  height:40px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:28px;
  color:#000000;
  text-align:left;
}
#u5090 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5090_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5091_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u5091 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:24px;
  width:90px;
  height:21px;
  display:flex;
  font-size:18px;
}
#u5091 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5091_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u5092_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:40px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u5092 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:92px;
  width:103px;
  height:40px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u5092 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5092_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5093_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:175px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:2px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:left;
}
#u5093 {
  border-width:0px;
  position:absolute;
  left:46px;
  top:92px;
  width:175px;
  height:40px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:left;
}
#u5093 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5093_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5094_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:267px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u5094 {
  border-width:0px;
  position:absolute;
  left:60px;
  top:55px;
  width:267px;
  height:18px;
  display:flex;
  font-size:16px;
}
#u5094 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5094_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5095_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:297px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:28px;
  color:#000000;
  text-align:left;
}
#u5095 {
  border-width:0px;
  position:absolute;
  left:46px;
  top:194px;
  width:297px;
  height:40px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:28px;
  color:#000000;
  text-align:left;
}
#u5095 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5095_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5096_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u5096 {
  border-width:0px;
  position:absolute;
  left:46px;
  top:173px;
  width:90px;
  height:21px;
  display:flex;
  font-size:18px;
}
#u5096 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5096_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u5097_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:40px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u5097 {
  border-width:0px;
  position:absolute;
  left:241px;
  top:241px;
  width:103px;
  height:40px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u5097 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5097_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5098_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:175px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:2px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:left;
}
#u5098 {
  border-width:0px;
  position:absolute;
  left:47px;
  top:241px;
  width:175px;
  height:40px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:left;
}
#u5098 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5098_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5099_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:267px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u5099 {
  border-width:0px;
  position:absolute;
  left:61px;
  top:204px;
  width:267px;
  height:18px;
  display:flex;
  font-size:16px;
}
#u5099 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5099_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5100_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:166px;
  height:40px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
}
#u5100 {
  border-width:0px;
  position:absolute;
  left:151px;
  top:327px;
  width:166px;
  height:40px;
  display:flex;
  font-size:20px;
}
#u5100 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5100_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5067_state2 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:492px;
  height:603px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u5067_state2_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u5101_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:472px;
  height:90px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u5101 {
  border-width:0px;
  position:absolute;
  left:10px;
  top:13px;
  width:472px;
  height:90px;
  display:flex;
  font-size:16px;
}
#u5101 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5101_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5102_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:297px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:28px;
  color:#000000;
  text-align:left;
}
#u5102 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:146px;
  width:297px;
  height:40px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:28px;
  color:#000000;
  text-align:left;
}
#u5102 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5102_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5103_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:144px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u5103 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:123px;
  width:144px;
  height:18px;
  display:flex;
  font-size:16px;
}
#u5103 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5103_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u5104_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:103px;
  height:40px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u5104 {
  border-width:0px;
  position:absolute;
  left:223px;
  top:193px;
  width:103px;
  height:40px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u5104 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5104_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5105_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:175px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:2px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:left;
}
#u5105 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:193px;
  width:175px;
  height:40px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:left;
}
#u5105 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5105_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5106_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:20px;
}
#u5106 {
  border-width:0px;
  position:absolute;
  left:287px;
  top:154px;
  width:30px;
  height:20px;
  display:flex;
}
#u5106 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5106_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5107_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:429px;
  height:82px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:left;
}
#u5107 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:440px;
  width:429px;
  height:82px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:left;
}
#u5107 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5107_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5108_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u5108 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:391px;
  width:112px;
  height:18px;
  display:flex;
  font-size:16px;
}
#u5108 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5108_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u5109 label {
  left:0px;
  width:100%;
}
#u5109_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:18px;
}
#u5109 {
  border-width:0px;
  position:absolute;
  left:54px;
  top:414px;
  width:175px;
  height:18px;
  display:flex;
  font-size:16px;
}
#u5109 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u5109_img.selected {
}
#u5109.selected {
}
#u5109_img.disabled {
}
#u5109.disabled {
}
#u5109_text {
  border-width:0px;
  position:absolute;
  left:20px;
  top:0px;
  width:153px;
  word-wrap:break-word;
  text-transform:none;
}
#u5109_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u5110 label {
  left:0px;
  width:100%;
}
#u5110_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:18px;
}
#u5110 {
  border-width:0px;
  position:absolute;
  left:246px;
  top:414px;
  width:197px;
  height:18px;
  display:flex;
  font-size:16px;
}
#u5110 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u5110_img.selected {
}
#u5110.selected {
}
#u5110_img.disabled {
}
#u5110.disabled {
}
#u5110_text {
  border-width:0px;
  position:absolute;
  left:20px;
  top:0px;
  width:175px;
  word-wrap:break-word;
  text-transform:none;
}
#u5110_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u5111 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u5112_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:298px;
  height:137px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#AEAEAE;
  text-align:left;
}
#u5112 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:242px;
  width:298px;
  height:137px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#AEAEAE;
  text-align:left;
}
#u5112 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5112_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5114 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u5115_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:45px;
}
#u5115 {
  border-width:0px;
  position:absolute;
  left:43px;
  top:250px;
  width:45px;
  height:45px;
  display:flex;
  color:#7F7F7F;
}
#u5115 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5115_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5116_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:151px;
  height:42px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#7F7F7F;
  text-align:left;
}
#u5116 {
  border-width:0px;
  position:absolute;
  left:97px;
  top:252px;
  width:151px;
  height:42px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#7F7F7F;
  text-align:left;
}
#u5116 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5116_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5117 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:250px;
  width:210px;
  height:120px;
  visibility:hidden;
}
#u5117_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:210px;
  height:120px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u5117_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u5118_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:120px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u5118 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:120px;
  display:flex;
  font-size:18px;
}
#u5118 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5118_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5119_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u5119 {
  border-width:0px;
  position:absolute;
  left:179px;
  top:2px;
  width:23px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u5119 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5119_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5120_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:2px;
}
#u5120 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:200px;
  height:1px;
  display:flex;
  font-size:18px;
}
#u5120 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5120_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5121_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:165px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  text-align:center;
}
#u5121 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:12px;
  width:165px;
  height:21px;
  display:flex;
  font-size:18px;
  text-align:center;
}
#u5121 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5121_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5122_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:2px;
}
#u5122 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:80px;
  width:200px;
  height:1px;
  display:flex;
  font-size:18px;
}
#u5122 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5122_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5123_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:173px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  text-align:center;
}
#u5123 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:50px;
  width:173px;
  height:21px;
  display:flex;
  font-size:18px;
  text-align:center;
}
#u5123 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5123_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5124_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u5124 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:85px;
  width:120px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u5124 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5124_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5125_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:395px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u5125 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:447px;
  width:395px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
}
#u5125 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5125_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5126_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:166px;
  height:40px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
}
#u5126 {
  border-width:0px;
  position:absolute;
  left:168px;
  top:538px;
  width:166px;
  height:40px;
  display:flex;
  font-size:20px;
}
#u5126 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5126_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5067_state3 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:492px;
  height:603px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u5067_state3_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u5127_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:448px;
  height:72px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u5127 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:9px;
  width:448px;
  height:72px;
  display:flex;
  font-size:16px;
}
#u5127 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5127_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5128 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u5129_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:230px;
  height:138px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#AEAEAE;
  text-align:left;
}
#u5129 {
  border-width:0px;
  position:absolute;
  left:246px;
  top:299px;
  width:230px;
  height:138px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#AEAEAE;
  text-align:left;
}
#u5129 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5129_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5131 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u5132_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:45px;
}
#u5132 {
  border-width:0px;
  position:absolute;
  left:263px;
  top:308px;
  width:45px;
  height:45px;
  display:flex;
  color:#7F7F7F;
}
#u5132 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5132_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5133_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:151px;
  height:42px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#7F7F7F;
  text-align:left;
}
#u5133 {
  border-width:0px;
  position:absolute;
  left:317px;
  top:310px;
  width:151px;
  height:42px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#7F7F7F;
  text-align:left;
}
#u5133 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5133_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5134 {
  border-width:0px;
  position:absolute;
  left:248px;
  top:308px;
  width:210px;
  height:120px;
  visibility:hidden;
}
#u5134_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:210px;
  height:120px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u5134_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u5135_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:120px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u5135 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:120px;
  display:flex;
  font-size:18px;
}
#u5135 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5135_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5136_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u5136 {
  border-width:0px;
  position:absolute;
  left:179px;
  top:2px;
  width:23px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u5136 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5136_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5137_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:2px;
}
#u5137 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:200px;
  height:1px;
  display:flex;
  font-size:18px;
}
#u5137 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5137_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5138_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:165px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  text-align:center;
}
#u5138 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:12px;
  width:165px;
  height:21px;
  display:flex;
  font-size:18px;
  text-align:center;
}
#u5138 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5138_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5139_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:2px;
}
#u5139 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:80px;
  width:200px;
  height:1px;
  display:flex;
  font-size:18px;
}
#u5139 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5139_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5140_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:173px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  text-align:center;
}
#u5140 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:50px;
  width:173px;
  height:21px;
  display:flex;
  font-size:18px;
  text-align:center;
}
#u5140 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5140_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5141_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u5141 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:85px;
  width:120px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u5141 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5141_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5142 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u5143_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:229px;
  height:142px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#AEAEAE;
  text-align:left;
}
#u5143 {
  border-width:0px;
  position:absolute;
  left:9px;
  top:299px;
  width:229px;
  height:142px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#AEAEAE;
  text-align:left;
}
#u5143 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5143_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5145 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u5146_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:45px;
}
#u5146 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:308px;
  width:45px;
  height:45px;
  display:flex;
  color:#7F7F7F;
}
#u5146 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5146_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5147_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:151px;
  height:42px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#7F7F7F;
  text-align:left;
}
#u5147 {
  border-width:0px;
  position:absolute;
  left:80px;
  top:310px;
  width:151px;
  height:42px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#7F7F7F;
  text-align:left;
}
#u5147 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5147_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5148 {
  border-width:0px;
  position:absolute;
  left:11px;
  top:308px;
  width:210px;
  height:120px;
  visibility:hidden;
}
#u5148_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:210px;
  height:120px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u5148_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u5149_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:120px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u5149 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:120px;
  display:flex;
  font-size:18px;
}
#u5149 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5149_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5150_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u5150 {
  border-width:0px;
  position:absolute;
  left:179px;
  top:2px;
  width:23px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u5150 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5150_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5151_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:2px;
}
#u5151 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:200px;
  height:1px;
  display:flex;
  font-size:18px;
}
#u5151 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5151_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5152_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:165px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  text-align:center;
}
#u5152 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:12px;
  width:165px;
  height:21px;
  display:flex;
  font-size:18px;
  text-align:center;
}
#u5152 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5152_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5153_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:2px;
}
#u5153 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:80px;
  width:200px;
  height:1px;
  display:flex;
  font-size:18px;
}
#u5153 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5153_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5154_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:173px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  text-align:center;
}
#u5154 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:50px;
  width:173px;
  height:21px;
  display:flex;
  font-size:18px;
  text-align:center;
}
#u5154 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5154_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5155_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u5155 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:85px;
  width:120px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u5155 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5155_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5156 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u5157_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:230px;
  height:138px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#AEAEAE;
  text-align:left;
}
#u5157 {
  border-width:0px;
  position:absolute;
  left:243px;
  top:161px;
  width:230px;
  height:138px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#AEAEAE;
  text-align:left;
}
#u5157 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5157_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5159 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u5160_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:45px;
}
#u5160 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:171px;
  width:45px;
  height:45px;
  display:flex;
  color:#7F7F7F;
}
#u5160 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5160_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5161_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:151px;
  height:42px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#7F7F7F;
  text-align:left;
}
#u5161 {
  border-width:0px;
  position:absolute;
  left:314px;
  top:173px;
  width:151px;
  height:42px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#7F7F7F;
  text-align:left;
}
#u5161 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5161_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5162 {
  border-width:0px;
  position:absolute;
  left:245px;
  top:171px;
  width:210px;
  height:120px;
  visibility:hidden;
}
#u5162_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:210px;
  height:120px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u5162_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u5163_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:120px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u5163 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:120px;
  display:flex;
  font-size:18px;
}
#u5163 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5163_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5164_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u5164 {
  border-width:0px;
  position:absolute;
  left:179px;
  top:2px;
  width:23px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u5164 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5164_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5165_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:2px;
}
#u5165 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:200px;
  height:1px;
  display:flex;
  font-size:18px;
}
#u5165 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5165_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5166_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:165px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  text-align:center;
}
#u5166 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:12px;
  width:165px;
  height:21px;
  display:flex;
  font-size:18px;
  text-align:center;
}
#u5166 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5166_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5167_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:2px;
}
#u5167 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:80px;
  width:200px;
  height:1px;
  display:flex;
  font-size:18px;
}
#u5167 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5167_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5168_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:173px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  text-align:center;
}
#u5168 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:50px;
  width:173px;
  height:21px;
  display:flex;
  font-size:18px;
  text-align:center;
}
#u5168 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5168_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5169_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u5169 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:85px;
  width:120px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u5169 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5169_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5170 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u5171_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:229px;
  height:138px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#AEAEAE;
  text-align:left;
}
#u5171 {
  border-width:0px;
  position:absolute;
  left:7px;
  top:161px;
  width:229px;
  height:138px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#AEAEAE;
  text-align:left;
}
#u5171 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5171_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5173 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u5174_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:45px;
}
#u5174 {
  border-width:0px;
  position:absolute;
  left:24px;
  top:170px;
  width:45px;
  height:45px;
  display:flex;
  color:#7F7F7F;
}
#u5174 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5174_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5175_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:151px;
  height:42px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#7F7F7F;
  text-align:left;
}
#u5175 {
  border-width:0px;
  position:absolute;
  left:78px;
  top:172px;
  width:151px;
  height:42px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#7F7F7F;
  text-align:left;
}
#u5175 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5175_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5176 {
  border-width:0px;
  position:absolute;
  left:9px;
  top:170px;
  width:210px;
  height:120px;
  visibility:hidden;
}
#u5176_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:210px;
  height:120px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u5176_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u5177_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:120px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u5177 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:120px;
  display:flex;
  font-size:18px;
}
#u5177 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5177_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5178_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u5178 {
  border-width:0px;
  position:absolute;
  left:179px;
  top:2px;
  width:23px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u5178 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5178_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5179_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:2px;
}
#u5179 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:200px;
  height:1px;
  display:flex;
  font-size:18px;
}
#u5179 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5179_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5180_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:165px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  text-align:center;
}
#u5180 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:12px;
  width:165px;
  height:21px;
  display:flex;
  font-size:18px;
  text-align:center;
}
#u5180 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5180_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5181_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:2px;
}
#u5181 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:80px;
  width:200px;
  height:1px;
  display:flex;
  font-size:18px;
}
#u5181 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5181_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5182_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:173px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  text-align:center;
}
#u5182 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:50px;
  width:173px;
  height:21px;
  display:flex;
  font-size:18px;
  text-align:center;
}
#u5182 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5182_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5183_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u5183 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:85px;
  width:120px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u5183 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5183_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5184 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u5185_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:227px;
  height:138px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#AEAEAE;
  text-align:left;
}
#u5185 {
  border-width:0px;
  position:absolute;
  left:10px;
  top:441px;
  width:227px;
  height:138px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#AEAEAE;
  text-align:left;
}
#u5185 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5185_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5187 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u5188_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:45px;
}
#u5188 {
  border-width:0px;
  position:absolute;
  left:27px;
  top:450px;
  width:45px;
  height:45px;
  display:flex;
  color:#7F7F7F;
}
#u5188 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5188_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5189_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:151px;
  height:42px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#7F7F7F;
  text-align:left;
}
#u5189 {
  border-width:0px;
  position:absolute;
  left:81px;
  top:452px;
  width:151px;
  height:42px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#7F7F7F;
  text-align:left;
}
#u5189 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5189_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5190 {
  border-width:0px;
  position:absolute;
  left:12px;
  top:450px;
  width:210px;
  height:120px;
  visibility:hidden;
}
#u5190_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:210px;
  height:120px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u5190_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u5191_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:120px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u5191 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:120px;
  display:flex;
  font-size:18px;
}
#u5191 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5191_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5192_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u5192 {
  border-width:0px;
  position:absolute;
  left:179px;
  top:2px;
  width:23px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u5192 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5192_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5193_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:2px;
}
#u5193 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:200px;
  height:1px;
  display:flex;
  font-size:18px;
}
#u5193 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5193_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5194_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:165px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  text-align:center;
}
#u5194 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:12px;
  width:165px;
  height:21px;
  display:flex;
  font-size:18px;
  text-align:center;
}
#u5194 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5194_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5195_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:2px;
}
#u5195 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:80px;
  width:200px;
  height:1px;
  display:flex;
  font-size:18px;
}
#u5195 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5195_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5196_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:173px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  text-align:center;
}
#u5196 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:50px;
  width:173px;
  height:21px;
  display:flex;
  font-size:18px;
  text-align:center;
}
#u5196 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5196_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5197_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u5197 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:85px;
  width:120px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u5197 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5197_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5198_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:299px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u5198 {
  border-width:0px;
  position:absolute;
  left:110px;
  top:579px;
  width:299px;
  height:15px;
  display:flex;
}
#u5198 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5198_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u5199_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:124px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#555555;
  text-align:left;
}
#u5199 {
  border-width:0px;
  position:absolute;
  left:9px;
  top:91px;
  width:124px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#555555;
  text-align:left;
}
#u5199 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5199_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5200_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:343px;
  height:30px;
}
#u5200 {
  border-width:0px;
  position:absolute;
  left:133px;
  top:91px;
  width:343px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#7F7F7F;
}
#u5200 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5200_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5201_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:123px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#555555;
  text-align:left;
}
#u5201 {
  border-width:0px;
  position:absolute;
  left:10px;
  top:123px;
  width:123px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#555555;
  text-align:left;
}
#u5201 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5201_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5202_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:344px;
  height:30px;
}
#u5202 {
  border-width:0px;
  position:absolute;
  left:133px;
  top:123px;
  width:344px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#7F7F7F;
}
#u5202 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5202_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5203_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:166px;
  height:40px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
}
#u5203 {
  border-width:0px;
  position:absolute;
  left:275px;
  top:490px;
  width:166px;
  height:40px;
  display:flex;
  font-size:20px;
}
#u5203 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5203_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5204 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u5205_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:100px;
  background:inherit;
  background-color:rgba(244, 250, 255, 1);
  border:none;
  border-radius:18px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u5205 {
  border-width:0px;
  position:absolute;
  left:172px;
  top:88px;
  width:100px;
  height:100px;
  display:flex;
}
#u5205 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5205_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5206_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:center;
}
#u5206 {
  border-width:0px;
  position:absolute;
  left:155px;
  top:155px;
  width:117px;
  height:21px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:center;
}
#u5206 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5206_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5207_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
}
#u5207 {
  border-width:0px;
  position:absolute;
  left:207px;
  top:111px;
  width:35px;
  height:35px;
  display:flex;
}
#u5207 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5207_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5208 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u5209_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:100px;
  background:inherit;
  background-color:rgba(244, 250, 255, 1);
  border:none;
  border-radius:18px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u5209 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:93px;
  width:100px;
  height:100px;
  display:flex;
}
#u5209 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5209_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5210_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:29px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  text-align:center;
}
#u5210 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:151px;
  width:100px;
  height:29px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  text-align:center;
}
#u5210 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5210_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5211_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
}
#u5211 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:116px;
  width:35px;
  height:35px;
  display:flex;
}
#u5211 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5211_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5212 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u5213_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:100px;
  background:inherit;
  background-color:rgba(244, 250, 255, 1);
  border:none;
  border-radius:18px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u5213 {
  border-width:0px;
  position:absolute;
  left:354px;
  top:88px;
  width:100px;
  height:100px;
  display:flex;
}
#u5213 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5213_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5214_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:67px;
  height:33px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:center;
}
#u5214 {
  border-width:0px;
  position:absolute;
  left:371px;
  top:150px;
  width:67px;
  height:33px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:center;
}
#u5214 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5214_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5215_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
}
#u5215 {
  border-width:0px;
  position:absolute;
  left:389px;
  top:111px;
  width:35px;
  height:35px;
  display:flex;
}
#u5215 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5215_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
