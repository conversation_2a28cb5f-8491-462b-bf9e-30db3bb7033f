﻿<!DOCTYPE html>
<html>
  <head>
    <title>示意图-邮储确认提现(邮储页面)</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/示意图-邮储确认提现_邮储页面_/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/示意图-邮储确认提现_邮储页面_/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (基础app框架(H5)) -->

      <!-- Unnamed (矩形) -->
      <div id="u721" class="ax_default box_1">
        <div id="u721_div" class=""></div>
        <div id="u721_text" class="text ">
          <p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u722" class="ax_default _二级标题">
        <div id="u722_div" class=""></div>
        <div id="u722_text" class="text ">
          <p><span>16:18</span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u723" class="ax_default icon">
        <img id="u723_img" class="img " src="images/海融宝签约_个人__f501_f502_/u3.svg"/>
        <div id="u723_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u724" class="ax_default icon">
        <img id="u724_img" class="img " src="images/海融宝签约_个人__f501_f502_/u4.svg"/>
        <div id="u724_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u725" class="ax_default _文本段落">
        <div id="u725_div" class=""></div>
        <div id="u725_text" class="text ">
          <p><span>确认提现</span></p>
        </div>
      </div>

      <!-- 操作状态 (动态面板) -->
      <div id="u726" class="ax_default ax_default_hidden" data-label="操作状态" style="display:none; visibility: hidden">
        <div id="u726_state0" class="panel_state" data-label="操作成功" style="">
          <div id="u726_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u727" class="ax_default box_3">
              <div id="u727_div" class=""></div>
              <div id="u727_text" class="text ">
                <p><span>操作成功！</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u726_state1" class="panel_state" data-label="操作失败" style="visibility: hidden;">
          <div id="u726_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u728" class="ax_default box_3">
              <div id="u728_div" class=""></div>
              <div id="u728_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u729" class="ax_default _文本段落">
              <div id="u729_div" class=""></div>
              <div id="u729_text" class="text ">
                <p><span>操作失败！</span></p>
              </div>
            </div>

            <!-- Unnamed (图片 ) -->
            <div id="u730" class="ax_default _图片_">
              <img id="u730_img" class="img " src="images/海融宝签约_个人__f501_f502_/u10.png"/>
              <div id="u730_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u731" class="ax_default _图片_">
        <img id="u731_img" class="img " src="images/海融宝签约_个人__f501_f502_/u11.png"/>
        <div id="u731_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u732" class="ax_default icon">
        <img id="u732_img" class="img " src="images/海融宝签约_个人__f501_f502_/u12.svg"/>
        <div id="u732_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u733" class="ax_default _文本段落">
        <div id="u733_div" class=""></div>
        <div id="u733_text" class="text ">
          <p><span>hrypt.com</span></p>
        </div>
      </div>
      <div id="u720" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (矩形) -->
      <div id="u734" class="ax_default primary_button">
        <div id="u734_div" class=""></div>
        <div id="u734_text" class="text ">
          <p><span>确认支付</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u735" class="ax_default _形状">
        <div id="u735_div" class=""></div>
        <div id="u735_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u736" class="ax_default _文本段落">
        <div id="u736_div" class=""></div>
        <div id="u736_text" class="text ">
          <p><span>￥21,165.00</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u737" class="ax_default _文本段落">
        <div id="u737_div" class=""></div>
        <div id="u737_text" class="text ">
          <p><span>收款账号:</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u738" class="ax_default" data-left="133" data-top="226" data-width="284" data-height="25">

        <!-- Unnamed (矩形) -->
        <div id="u739" class="ax_default _文本段落">
          <div id="u739_div" class=""></div>
          <div id="u739_text" class="text ">
            <p><span>中国邮政储蓄银行 (0416)</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u740" class="ax_default" data-left="22" data-top="383" data-width="460" data-height="128">

        <!-- Unnamed (矩形) -->
        <div id="u741" class="ax_default _形状">
          <div id="u741_div" class=""></div>
          <div id="u741_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u742" class="ax_default _文本段落">
          <div id="u742_div" class=""></div>
          <div id="u742_text" class="text ">
            <p><span>支付密码</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u743" class="ax_default" data-left="92" data-top="440" data-width="343" data-height="35">

          <!-- Unnamed (文本框) -->
          <div id="u744" class="ax_default text_field">
            <div id="u744_div" class=""></div>
            <input id="u744_input" type="text" value="" class="u744_input"/>
          </div>

          <!-- Unnamed (文本框) -->
          <div id="u745" class="ax_default text_field">
            <div id="u745_div" class=""></div>
            <input id="u745_input" type="text" value="" class="u745_input"/>
          </div>

          <!-- Unnamed (文本框) -->
          <div id="u746" class="ax_default text_field">
            <div id="u746_div" class=""></div>
            <input id="u746_input" type="text" value="" class="u746_input"/>
          </div>

          <!-- Unnamed (文本框) -->
          <div id="u747" class="ax_default text_field">
            <div id="u747_div" class=""></div>
            <input id="u747_input" type="text" value="" class="u747_input"/>
          </div>

          <!-- Unnamed (文本框) -->
          <div id="u748" class="ax_default text_field">
            <div id="u748_div" class=""></div>
            <input id="u748_input" type="text" value="" class="u748_input"/>
          </div>

          <!-- Unnamed (文本框) -->
          <div id="u749" class="ax_default text_field">
            <div id="u749_div" class=""></div>
            <input id="u749_input" type="text" value="" class="u749_input"/>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u750" class="ax_default _形状">
        <div id="u750_div" class=""></div>
        <div id="u750_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u751" class="ax_default _文本段落">
        <div id="u751_div" class=""></div>
        <div id="u751_text" class="text ">
          <p><span>温馨提示</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u752" class="ax_default _文本段落">
        <div id="u752_div" class=""></div>
        <div id="u752_text" class="text ">
          <p><span>1.交易确认后，数字⼈⺠币钱包划转邮储银行母钱包。</span></p><p><span><br></span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u753" class="ax_default _文本段落">
        <div id="u753_div" class=""></div>
        <div id="u753_text" class="text ">
          <p><span>钱包ID：3100 **** **** 8888</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u754" class="ax_default" data-left="30" data-top="275" data-width="442" data-height="25">

        <!-- Unnamed (矩形) -->
        <div id="u755" class="ax_default _文本段落">
          <div id="u755_div" class=""></div>
          <div id="u755_text" class="text ">
            <p><span>收款数币钱包</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u756" class="ax_default" data-left="167" data-top="275" data-width="305" data-height="25">

          <!-- Unnamed (矩形) -->
          <div id="u757" class="ax_default _文本段落">
            <div id="u757_div" class=""></div>
            <div id="u757_text" class="text ">
              <p><span>中国邮政储蓄银行 (0416)</span></p>
            </div>
          </div>

          <!-- Unnamed (形状) -->
          <div id="u758" class="ax_default icon">
            <img id="u758_img" class="img " src="images/示意图-邮储充值确认_邮储页面_/u652.svg"/>
            <div id="u758_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u759" class="ax_default _形状">
        <div id="u759_div" class=""></div>
        <div id="u759_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u760" class="ax_default _文本段落">
        <div id="u760_div" class=""></div>
        <div id="u760_text" class="text ">
          <p><span>交易时间</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u761" class="ax_default _文本段落">
        <div id="u761_div" class=""></div>
        <div id="u761_text" class="text ">
          <p><span>子账户全称</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u762" class="ax_default _文本段落">
        <div id="u762_div" class=""></div>
        <div id="u762_text" class="text ">
          <p><span>收单机构</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u763" class="ax_default _文本段落">
        <div id="u763_div" class=""></div>
        <div id="u763_text" class="text ">
          <p><span>用户钱包ID</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u764" class="ax_default" data-left="572" data-top="183" data-width="426" data-height="18">

        <!-- Unnamed (矩形) -->
        <div id="u765" class="ax_default _文本段落">
          <div id="u765_div" class=""></div>
          <div id="u765_text" class="text ">
            <p><span>订单单号</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u766" class="ax_default _文本段落">
          <div id="u766_div" class=""></div>
          <div id="u766_text" class="text ">
            <p><span>4200002682886982550742025051</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u767" class="ax_default _文本段落">
        <div id="u767_div" class=""></div>
        <div id="u767_text" class="text ">
          <p><span>2025年5月18日 14:21:31</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u768" class="ax_default _文本段落">
        <div id="u768_div" class=""></div>
        <div id="u768_text" class="text ">
          <p><span>海创未来(福建)科技有限公司</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u769" class="ax_default _文本段落">
        <div id="u769_div" class=""></div>
        <div id="u769_text" class="text ">
          <p><span>中国邮政储蓄银行福建省分行</span></p><p><span><br></span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u770" class="ax_default _文本段落">
        <div id="u770_div" class=""></div>
        <div id="u770_text" class="text ">
          <p><span>邮储银行 数字人民币钱包id（8888）</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u771" class="ax_default" data-left="572" data-top="150" data-width="426" data-height="18">

        <!-- Unnamed (矩形) -->
        <div id="u772" class="ax_default _文本段落">
          <div id="u772_div" class=""></div>
          <div id="u772_text" class="text ">
            <p><span>交易金额</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u773" class="ax_default _文本段落">
          <div id="u773_div" class=""></div>
          <div id="u773_text" class="text ">
            <p><span>21,165.00</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u774" class="ax_default _文本段落">
        <div id="u774_div" class=""></div>
        <div id="u774_text" class="text ">
          <p><span>提现信息（应用平台接口数据）</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u775" class="ax_default" data-left="572" data-top="415" data-width="426" data-height="18">

        <!-- Unnamed (矩形) -->
        <div id="u776" class="ax_default _文本段落">
          <div id="u776_div" class=""></div>
          <div id="u776_text" class="text ">
            <p><span>操作附言</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u777" class="ax_default _文本段落">
          <div id="u777_div" class=""></div>
          <div id="u777_text" class="text ">
            <p><span>无</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u778" class="ax_default" data-left="572" data-top="382" data-width="426" data-height="18">

        <!-- Unnamed (矩形) -->
        <div id="u779" class="ax_default _文本段落">
          <div id="u779_div" class=""></div>
          <div id="u779_text" class="text ">
            <p><span>交易通道</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u780" class="ax_default _文本段落">
          <div id="u780_div" class=""></div>
          <div id="u780_text" class="text ">
            <p><span>数优联/数市联/数蛋联/数药联/数牛联/数菜联</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u781" class="ax_default _文本段落">
        <div id="u781_div" class=""></div>
        <div id="u781_text" class="text ">
          <p><span>说明：以上多余字段保留，实际传输以邮储为准。</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u782" class="ax_default" data-left="572" data-top="246" data-width="426" data-height="18">

        <!-- Unnamed (矩形) -->
        <div id="u783" class="ax_default _文本段落">
          <div id="u783_div" class=""></div>
          <div id="u783_text" class="text ">
            <p><span>付款单号</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u784" class="ax_default _文本段落">
          <div id="u784_div" class=""></div>
          <div id="u784_text" class="text ">
            <p><span>8869825504200002674202505182</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u785" class="ax_default _文本段落">
        <div id="u785_div" class=""></div>
        <div id="u785_text" class="text ">
          <p style="font-size:20px;"><span style="color:#000000;">F512出金创单</span></p><p style="font-size:20px;"><span style="color:#000000;"><br></span></p><p style="font-size:13px;"><span>节点属性&nbsp; &nbsp;&nbsp; 中文描述&nbsp; &nbsp;&nbsp; 数据类型&nbsp; &nbsp;&nbsp; 必输&nbsp; &nbsp;&nbsp; 说明</span></p><p style="font-size:13px;"><span>platOrderNo&nbsp; &nbsp;&nbsp; 平台出金订单号&nbsp; &nbsp;&nbsp; char(100)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 平台生成的唯一标识,银企客户号+yyyyMMdd+序号</span></p><p style="font-size:13px;"><span>signAgrNo&nbsp; &nbsp;&nbsp; 签约协议号&nbsp; &nbsp;&nbsp; char(64)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 个人会员签约时生成</span></p><p style="font-size:13px;"><span>custId&nbsp; &nbsp;&nbsp; 客户Id&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 个人会员签约时生成</span></p><p style="font-size:13px;"><span>txAmt&nbsp; &nbsp;&nbsp; 交易金额&nbsp; &nbsp;&nbsp; char(22)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 保留两位小数</span></p><p style="font-size:13px;"><span>txCurr&nbsp; &nbsp;&nbsp; 交易币种&nbsp; &nbsp;&nbsp; char(3)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 人民币CNY</span></p><p style="font-size:13px;"><span>vrfCdSendFlag&nbsp; &nbsp;&nbsp; 是否发送验证码&nbsp; &nbsp;&nbsp; char(1)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 0-不发送，1-发送</span></p><p style="font-size:13px;"><span>txnRmrk&nbsp; &nbsp;&nbsp; 交易备注&nbsp; &nbsp;&nbsp; char(300)&nbsp; &nbsp;&nbsp; N&nbsp; </span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span>&nbsp;&nbsp; </span></p><p style="font-size:13px;"><span>节点属性&nbsp; &nbsp;&nbsp; 中文描述&nbsp; &nbsp;&nbsp; 数据类型&nbsp; &nbsp;&nbsp; 必输&nbsp; &nbsp;&nbsp; 说明</span></p><p style="font-size:13px;"><span>txOrderNo&nbsp; &nbsp;&nbsp; 交易订单号&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; </span></p><p style="font-size:13px;"><span>platOrderNo&nbsp; &nbsp;&nbsp; 平台订单号&nbsp; &nbsp;&nbsp; char(100)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 可用于查询交易详情</span></p><p style="font-size:13px;"><span>custId&nbsp; &nbsp;&nbsp; 客户Id&nbsp; &nbsp;&nbsp; char(100)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; </span></p><p style="font-size:13px;"><span>platfNo&nbsp; &nbsp;&nbsp; 平台编号&nbsp; &nbsp;&nbsp; char(48)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; </span></p><p style="font-size:13px;"><span>txAmt&nbsp; &nbsp;&nbsp; 交易金额&nbsp; &nbsp;&nbsp; char(22)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 保留两位小数</span></p><p style="font-size:13px;"><span>txCurr&nbsp; &nbsp;&nbsp; 交易币种&nbsp; &nbsp;&nbsp; char(3)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; CNY</span></p><p style="font-size:13px;"><span>mbno&nbsp; &nbsp;&nbsp; 手机号&nbsp; &nbsp;&nbsp; char(11)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; &quot;是否发送验证码&quot;为1时返回</span></p><p style="font-size:13px;"><span>txCrtTime&nbsp; &nbsp;&nbsp; 交易创建时间&nbsp; &nbsp;&nbsp; char(14)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; yyyyMMddHHmmss</span></p><p style="font-size:13px;"><span>sessNo&nbsp; &nbsp;&nbsp; 会话编号&nbsp; &nbsp;&nbsp; char(64)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; &quot;是否发送验证码&quot;为1时返回。</span></p><p style="font-size:13px;"><span>和验证码一一对应，F513出金结算时需要上送</span></p><p style="font-size:13px;"><span>bdlStaCd&nbsp; &nbsp;&nbsp; 业务处理状态代码&nbsp; &nbsp;&nbsp; char(4)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; PR00-成功，PR01-失败，PR02-处理中</span></p><p style="font-size:13px;"><span>failReason&nbsp; &nbsp;&nbsp; 失败原因&nbsp; &nbsp;&nbsp; char(500)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; 当业务处理状态代码为PR01-失败时返回</span></p><p style="font-size:20px;"><span style="color:#000000;"><br></span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u786" class="ax_default _文本段落">
        <div id="u786_div" class=""></div>
        <div id="u786_text" class="text ">
          <p style="font-size:20px;"><span style="color:#000000;">F513出金结算创单</span></p><p style="font-size:20px;"><span style="color:#000000;"><br></span></p><p style="font-size:13px;"><span>节点属性&nbsp; &nbsp;&nbsp; 中文描述&nbsp; &nbsp;&nbsp; 数据类型&nbsp; &nbsp;&nbsp; 必输&nbsp; &nbsp;&nbsp; 说明</span></p><p style="font-size:13px;"><span>txOrderNo&nbsp; &nbsp;&nbsp; 交易订单号&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 使用出金创单响应的交易订单号</span></p><p style="font-size:13px;"><span>platOrderNo&nbsp; &nbsp;&nbsp; 平台出金订单号&nbsp; &nbsp;&nbsp; char(100)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 平台生成的唯一标识,银企客户号+yyyyMMdd+序号.</span></p><p style="font-size:13px;"><span>需要和出金创单的平台出金订单号一致</span></p><p style="font-size:13px;"><span>custId&nbsp; &nbsp;&nbsp; 客户Id&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 个人签约时生成</span></p><p style="font-size:13px;"><span>sessNo&nbsp; &nbsp;&nbsp; 会话编号&nbsp; &nbsp;&nbsp; char(64)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 出金创单返回</span></p><p style="font-size:13px;"><span>verifyCode&nbsp; &nbsp;&nbsp; 短信验证码&nbsp; &nbsp;&nbsp; char(6)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 调用出金创单时会发送短信到客户手机</span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span>&nbsp;&nbsp; </span></p><p style="font-size:13px;"><span>节点属性&nbsp; &nbsp;&nbsp; 中文描述&nbsp; &nbsp;&nbsp; 数据类型&nbsp; &nbsp;&nbsp; 必输&nbsp; &nbsp;&nbsp; 说明</span></p><p style="font-size:13px;"><span>txOrderNo&nbsp; &nbsp;&nbsp; 交易订单号&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; </span></p><p style="font-size:13px;"><span>platOrderNo&nbsp; &nbsp;&nbsp; 平台订单号&nbsp; &nbsp;&nbsp; char(100)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; </span></p><p style="font-size:13px;"><span>txAmt&nbsp; &nbsp;&nbsp; 交易金额&nbsp; &nbsp;&nbsp; char(22)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 保留两位小数</span></p><p style="font-size:13px;"><span>txCurr&nbsp; &nbsp;&nbsp; 交易币种&nbsp; &nbsp;&nbsp; char(3)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 人民币CNY</span></p><p style="font-size:13px;"><span>txDealTime&nbsp; &nbsp;&nbsp; 交易处理时间&nbsp; &nbsp;&nbsp; char(14)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; yyyyMMddHHmmss</span></p><p style="font-size:13px;"><span>bdlStaCd&nbsp; &nbsp;&nbsp; 业务处理状态代码&nbsp; &nbsp;&nbsp; char(4)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; PR00-成功，PR01-失败，PR02-处理中</span></p><p style="font-size:13px;"><span>failReason&nbsp; &nbsp;&nbsp; 失败原因&nbsp; &nbsp;&nbsp; char(500)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; 当业务处理状态代码为PR01-失败时返回</span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:20px;"><span style="color:#000000;"><br></span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
