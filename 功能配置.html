﻿<!DOCTYPE html>
<html>
  <head>
    <title>功能配置</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/功能配置/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/功能配置/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (基础APP框架) -->

      <!-- Unnamed (矩形) -->
      <div id="u4921" class="ax_default box_1">
        <div id="u4921_div" class=""></div>
        <div id="u4921_text" class="text ">
          <p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u4922" class="ax_default" data-left="22" data-top="834" data-width="456" data-height="41">

        <!-- Unnamed (组合) -->
        <div id="u4923" class="ax_default" data-left="22" data-top="834" data-width="26" data-height="41">

          <!-- Unnamed (图片 ) -->
          <div id="u4924" class="ax_default _图片">
            <img id="u4924_img" class="img " src="images/平台首页/u2789.png"/>
            <div id="u4924_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u4925" class="ax_default _文本段落">
            <div id="u4925_div" class=""></div>
            <div id="u4925_text" class="text ">
              <p><span>首页</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u4926" class="ax_default" data-left="130" data-top="834" data-width="26" data-height="41">

          <!-- Unnamed (图片 ) -->
          <div id="u4927" class="ax_default _图片">
            <img id="u4927_img" class="img " src="images/平台首页/u2792.png"/>
            <div id="u4927_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u4928" class="ax_default _文本段落">
            <div id="u4928_div" class=""></div>
            <div id="u4928_text" class="text ">
              <p><span>融资</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u4929" class="ax_default" data-left="345" data-top="834" data-width="26" data-height="41">

          <!-- Unnamed (图片 ) -->
          <div id="u4930" class="ax_default _图片">
            <img id="u4930_img" class="img " src="images/平台首页/u2795.png"/>
            <div id="u4930_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u4931" class="ax_default _文本段落">
            <div id="u4931_div" class=""></div>
            <div id="u4931_text" class="text ">
              <p><span>消息</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u4932" class="ax_default" data-left="452" data-top="834" data-width="26" data-height="41">

          <!-- Unnamed (图片 ) -->
          <div id="u4933" class="ax_default _图片">
            <img id="u4933_img" class="img " src="images/平台首页/u2798.png"/>
            <div id="u4933_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u4934" class="ax_default _文本段落">
            <div id="u4934_div" class=""></div>
            <div id="u4934_text" class="text ">
              <p><span>我的</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u4935" class="ax_default" data-left="228" data-top="834" data-width="44" data-height="41">

          <!-- Unnamed (图片 ) -->
          <div id="u4936" class="ax_default _图片">
            <img id="u4936_img" class="img " src="images/平台首页/u2801.png"/>
            <div id="u4936_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u4937" class="ax_default _文本段落">
            <div id="u4937_div" class=""></div>
            <div id="u4937_text" class="text ">
              <p><span>发现</span></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4938" class="ax_default box_1">
        <div id="u4938_div" class=""></div>
        <div id="u4938_text" class="text ">
          <p><span>3</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4939" class="ax_default _二级标题">
        <div id="u4939_div" class=""></div>
        <div id="u4939_text" class="text ">
          <p><span>16:18</span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u4940" class="ax_default icon">
        <img id="u4940_img" class="img " src="images/海融宝签约_个人__f501_f502_/u3.svg"/>
        <div id="u4940_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u4941" class="ax_default icon">
        <img id="u4941_img" class="img " src="images/海融宝签约_个人__f501_f502_/u4.svg"/>
        <div id="u4941_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u4942" class="ax_default _图片">
        <img id="u4942_img" class="img " src="images/个人开结算账户（申请）/u2269.png"/>
        <div id="u4942_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4943" class="ax_default _文本段落">
        <div id="u4943_div" class=""></div>
        <div id="u4943_text" class="text ">
          <p><span>功能配置</span></p>
        </div>
      </div>

      <!-- 操作状态 (动态面板) -->
      <div id="u4944" class="ax_default ax_default_hidden" data-label="操作状态" style="display:none; visibility: hidden">
        <div id="u4944_state0" class="panel_state" data-label="操作成功" style="">
          <div id="u4944_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u4945" class="ax_default box_3">
              <div id="u4945_div" class=""></div>
              <div id="u4945_text" class="text ">
                <p><span>操作成功！</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u4944_state1" class="panel_state" data-label="操作失败" style="visibility: hidden;">
          <div id="u4944_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u4946" class="ax_default box_3">
              <div id="u4946_div" class=""></div>
              <div id="u4946_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u4947" class="ax_default _文本段落">
              <div id="u4947_div" class=""></div>
              <div id="u4947_text" class="text ">
                <p><span>操作失败！</span></p>
              </div>
            </div>

            <!-- Unnamed (图片 ) -->
            <div id="u4948" class="ax_default _图片_">
              <img id="u4948_img" class="img " src="images/海融宝签约_个人__f501_f502_/u10.png"/>
              <div id="u4948_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4949" class="ax_default _文本段落">
        <div id="u4949_div" class=""></div>
        <div id="u4949_text" class="text ">
          <p><span>hrypt.com</span></p>
        </div>
      </div>
      <div id="u4920" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (矩形) -->
      <div id="u4950" class="ax_default box_1">
        <div id="u4950_div" class=""></div>
        <div id="u4950_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (横排菜单式单条链接导航) -->

      <!-- Unnamed (组合) -->
      <div id="u4952" class="ax_default" data-left="15" data-top="715" data-width="479" data-height="30">

        <!-- Unnamed (矩形) -->
        <div id="u4953" class="ax_default _一级标题">
          <div id="u4953_div" class=""></div>
          <div id="u4953_text" class="text ">
            <p><span>清理缓存</span></p>
          </div>
        </div>

        <!-- Unnamed (形状) -->
        <div id="u4954" class="ax_default icon">
          <img id="u4954_img" class="img " src="images/安全管理/u2066.svg"/>
          <div id="u4954_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (图片 ) -->
        <div id="u4955" class="ax_default _图片">
          <img id="u4955_img" class="img " src="images/功能配置/u4955.svg"/>
          <div id="u4955_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u4956" class="ax_default _一级标题">
          <div id="u4956_div" class=""></div>
          <div id="u4956_text" class="text ">
            <p><span>&nbsp;</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u4957" class="ax_default line">
        <img id="u4957_img" class="img " src="images/安全管理/u2069.svg"/>
        <div id="u4957_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u4958" class="ax_default line">
        <img id="u4958_img" class="img " src="images/安全管理/u2069.svg"/>
        <div id="u4958_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>
      <div id="u4951" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (横排菜单式单条链接导航) -->

      <!-- Unnamed (组合) -->
      <div id="u4960" class="ax_default" data-left="15" data-top="665" data-width="479" data-height="30">

        <!-- Unnamed (矩形) -->
        <div id="u4961" class="ax_default _一级标题">
          <div id="u4961_div" class=""></div>
          <div id="u4961_text" class="text ">
            <p><span>在线客服</span></p>
          </div>
        </div>

        <!-- Unnamed (形状) -->
        <div id="u4962" class="ax_default icon">
          <img id="u4962_img" class="img " src="images/安全管理/u2066.svg"/>
          <div id="u4962_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (图片 ) -->
        <div id="u4963" class="ax_default _图片">
          <img id="u4963_img" class="img " src="images/功能配置/u4963.svg"/>
          <div id="u4963_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u4964" class="ax_default _一级标题">
          <div id="u4964_div" class=""></div>
          <div id="u4964_text" class="text ">
            <p><span>&nbsp;</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u4965" class="ax_default line">
        <img id="u4965_img" class="img " src="images/安全管理/u2069.svg"/>
        <div id="u4965_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u4966" class="ax_default line">
        <img id="u4966_img" class="img " src="images/安全管理/u2069.svg"/>
        <div id="u4966_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>
      <div id="u4959" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (横排菜单式单条链接导航) -->

      <!-- Unnamed (组合) -->
      <div id="u4968" class="ax_default" data-left="15" data-top="615" data-width="479" data-height="30">

        <!-- Unnamed (矩形) -->
        <div id="u4969" class="ax_default _一级标题">
          <div id="u4969_div" class=""></div>
          <div id="u4969_text" class="text ">
            <p><span>隐私政策</span></p>
          </div>
        </div>

        <!-- Unnamed (形状) -->
        <div id="u4970" class="ax_default icon">
          <img id="u4970_img" class="img " src="images/安全管理/u2066.svg"/>
          <div id="u4970_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (图片 ) -->
        <div id="u4971" class="ax_default _图片">
          <img id="u4971_img" class="img " src="images/功能配置/u4971.svg"/>
          <div id="u4971_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u4972" class="ax_default _一级标题">
          <div id="u4972_div" class=""></div>
          <div id="u4972_text" class="text ">
            <p><span>&nbsp;</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u4973" class="ax_default line">
        <img id="u4973_img" class="img " src="images/安全管理/u2069.svg"/>
        <div id="u4973_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u4974" class="ax_default line">
        <img id="u4974_img" class="img " src="images/安全管理/u2069.svg"/>
        <div id="u4974_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>
      <div id="u4967" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (横排菜单式单条链接导航) -->

      <!-- Unnamed (组合) -->
      <div id="u4976" class="ax_default" data-left="15" data-top="565" data-width="479" data-height="30">

        <!-- Unnamed (矩形) -->
        <div id="u4977" class="ax_default _一级标题">
          <div id="u4977_div" class=""></div>
          <div id="u4977_text" class="text ">
            <p><span>常见问题</span></p>
          </div>
        </div>

        <!-- Unnamed (形状) -->
        <div id="u4978" class="ax_default icon">
          <img id="u4978_img" class="img " src="images/安全管理/u2066.svg"/>
          <div id="u4978_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (图片 ) -->
        <div id="u4979" class="ax_default _图片">
          <img id="u4979_img" class="img " src="images/功能配置/u4979.svg"/>
          <div id="u4979_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u4980" class="ax_default _一级标题">
          <div id="u4980_div" class=""></div>
          <div id="u4980_text" class="text ">
            <p><span>&nbsp;</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u4981" class="ax_default line">
        <img id="u4981_img" class="img " src="images/安全管理/u2069.svg"/>
        <div id="u4981_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u4982" class="ax_default line">
        <img id="u4982_img" class="img " src="images/安全管理/u2069.svg"/>
        <div id="u4982_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>
      <div id="u4975" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (横排菜单式单条链接导航) -->

      <!-- Unnamed (组合) -->
      <div id="u4984" class="ax_default" data-left="15" data-top="515" data-width="479" data-height="30">

        <!-- Unnamed (矩形) -->
        <div id="u4985" class="ax_default _一级标题">
          <div id="u4985_div" class=""></div>
          <div id="u4985_text" class="text ">
            <p><span>发票信息</span></p>
          </div>
        </div>

        <!-- Unnamed (形状) -->
        <div id="u4986" class="ax_default icon">
          <img id="u4986_img" class="img " src="images/安全管理/u2066.svg"/>
          <div id="u4986_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (图片 ) -->
        <div id="u4987" class="ax_default _图片">
          <img id="u4987_img" class="img " src="images/功能配置/u4987.svg"/>
          <div id="u4987_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u4988" class="ax_default _一级标题">
          <div id="u4988_div" class=""></div>
          <div id="u4988_text" class="text ">
            <p><span>&nbsp;</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u4989" class="ax_default line">
        <img id="u4989_img" class="img " src="images/安全管理/u2069.svg"/>
        <div id="u4989_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u4990" class="ax_default line">
        <img id="u4990_img" class="img " src="images/安全管理/u2069.svg"/>
        <div id="u4990_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>
      <div id="u4983" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (横排菜单式单条链接导航) -->

      <!-- Unnamed (组合) -->
      <div id="u4992" class="ax_default" data-left="15" data-top="765" data-width="479" data-height="30">

        <!-- Unnamed (矩形) -->
        <div id="u4993" class="ax_default _一级标题">
          <div id="u4993_div" class=""></div>
          <div id="u4993_text" class="text ">
            <p><span>关于我们</span></p>
          </div>
        </div>

        <!-- Unnamed (形状) -->
        <div id="u4994" class="ax_default icon">
          <img id="u4994_img" class="img " src="images/安全管理/u2066.svg"/>
          <div id="u4994_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (图片 ) -->
        <div id="u4995" class="ax_default _图片">
          <img id="u4995_img" class="img " src="images/我的/u3050.svg"/>
          <div id="u4995_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u4996" class="ax_default _一级标题">
          <div id="u4996_div" class=""></div>
          <div id="u4996_text" class="text ">
            <p><span>版本号 1.0.1</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u4997" class="ax_default line">
        <img id="u4997_img" class="img " src="images/安全管理/u2069.svg"/>
        <div id="u4997_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u4998" class="ax_default line">
        <img id="u4998_img" class="img " src="images/安全管理/u2069.svg"/>
        <div id="u4998_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>
      <div id="u4991" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (热区) -->
      <div id="u4999" class="ax_default">
      </div>

      <!-- Unnamed (热区) -->
      <div id="u5000" class="ax_default">
      </div>

      <!-- Unnamed (热区) -->
      <div id="u5001" class="ax_default">
      </div>

      <!-- Unnamed (热区) -->
      <div id="u5002" class="ax_default">
      </div>

      <!-- Unnamed (热区) -->
      <div id="u5003" class="ax_default">
      </div>

      <!-- Unnamed (热区) -->
      <div id="u5004" class="ax_default">
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
