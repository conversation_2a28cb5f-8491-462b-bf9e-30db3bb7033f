﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:510px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u2674_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:896px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:50px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2674 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:896px;
  display:flex;
  opacity:0.49;
}
#u2674 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2674_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2675_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u2675 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:20px;
  width:51px;
  height:40px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u2675 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2675_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2676_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:18px;
}
#u2676 {
  border-width:0px;
  position:absolute;
  left:425px;
  top:19px;
  width:23px;
  height:18px;
  display:flex;
}
#u2676 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2676_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2677_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:16px;
}
#u2677 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:21px;
  width:26px;
  height:16px;
  display:flex;
}
#u2677 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2677_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2678_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u2678 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:49px;
  width:300px;
  height:25px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u2678 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2678_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2679_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:228px;
  height:11px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  text-align:center;
}
#u2679 {
  border-width:0px;
  position:absolute;
  left:136px;
  top:71px;
  width:228px;
  height:11px;
  display:flex;
  font-size:10px;
  text-align:center;
}
#u2679 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2679_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2681_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:400px;
  height:200px;
}
#u2681 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:110px;
  width:400px;
  height:200px;
  display:flex;
}
#u2681 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2681_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2682_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:400px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:28px;
  color:#0000FF;
  text-align:center;
}
#u2682 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:116px;
  width:400px;
  height:32px;
  display:flex;
  font-size:28px;
  color:#0000FF;
  text-align:center;
}
#u2682 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2682_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2683_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:400px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  color:#1296DB;
  text-align:center;
}
#u2683 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:310px;
  width:400px;
  height:50px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  color:#1296DB;
  text-align:center;
}
#u2683 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2683_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2684_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:333px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:150px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  color:#000000;
}
#u2684 {
  border-width:0px;
  position:absolute;
  left:80px;
  top:635px;
  width:333px;
  height:40px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  color:#000000;
}
#u2684 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2684_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2685_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:380px;
  height:67px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u2685 {
  border-width:0px;
  position:absolute;
  left:80px;
  top:713px;
  width:380px;
  height:67px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u2685 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2685_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2686_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u2686 {
  border-width:0px;
  position:absolute;
  left:136px;
  top:645px;
  width:25px;
  height:25px;
  display:flex;
}
#u2686 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2686_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2687_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2687 {
  border-width:0px;
  position:absolute;
  left:51px;
  top:713px;
  width:20px;
  height:20px;
  display:flex;
}
#u2687 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2687_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2688_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:375px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  color:#999999;
  text-align:left;
}
#u2688 {
  border-width:0px;
  position:absolute;
  left:62px;
  top:387px;
  width:375px;
  height:40px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  color:#999999;
  text-align:left;
}
#u2688 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2688_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2689_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:375px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  color:#999999;
  text-align:left;
}
#u2689 {
  border-width:0px;
  position:absolute;
  left:62px;
  top:449px;
  width:375px;
  height:40px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  color:#999999;
  text-align:left;
}
#u2689 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2689_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2690_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#8400FF;
  text-align:right;
}
#u2690 {
  border-width:0px;
  position:absolute;
  left:337px;
  top:500px;
  width:100px;
  height:25px;
  display:flex;
  font-size:16px;
  color:#8400FF;
  text-align:right;
}
#u2690 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2690_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2691_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:121px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#8400FF;
}
#u2691 {
  border-width:0px;
  position:absolute;
  left:62px;
  top:500px;
  width:121px;
  height:25px;
  display:flex;
  font-size:16px;
  color:#8400FF;
}
#u2691 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2691_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2692_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:333px;
  height:40px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:40px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
}
#u2692 {
  border-width:0px;
  position:absolute;
  left:80px;
  top:582px;
  width:333px;
  height:40px;
  display:flex;
  font-size:20px;
}
#u2692 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2692_div.mouseDown {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:333px;
  height:40px;
  background:inherit;
  background-color:rgba(204, 204, 204, 1);
  border:none;
  border-radius:40px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
}
#u2692.mouseDown {
}
#u2692_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2693_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:67px;
  height:24px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2693 {
  border-width:0px;
  position:absolute;
  left:370px;
  top:455px;
  width:67px;
  height:24px;
  display:flex;
}
#u2693 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2693_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2694 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2695_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:895px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.298039215686275);
  border:none;
  border-radius:50px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2695 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:895px;
  display:flex;
}
#u2695 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2695_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2696 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2697_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:562px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2697 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:459px;
  width:510px;
  height:562px;
  display:flex;
}
#u2697 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2697_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2698_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u2698 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:477px;
  width:25px;
  height:25px;
  display:flex;
}
#u2698 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2698_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2699_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:354px;
  height:25px;
}
#u2699 {
  border-width:0px;
  position:absolute;
  left:71px;
  top:477px;
  width:354px;
  height:25px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u2699 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2699_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2700_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:511px;
  height:2px;
}
#u2700 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:519px;
  width:510px;
  height:1px;
  display:flex;
  font-size:16px;
}
#u2700 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2700_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2701_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:212px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#33CC00;
  text-align:center;
}
#u2701 {
  border-width:0px;
  position:absolute;
  left:154px;
  top:919px;
  width:212px;
  height:32px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#33CC00;
  text-align:center;
}
#u2701 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2701_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2702_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:399px;
  height:46px;
  background:inherit;
  background-color:rgba(18, 150, 219, 1);
  border:none;
  border-radius:282px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u2702 {
  border-width:0px;
  position:absolute;
  left:61px;
  top:799px;
  width:399px;
  height:46px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u2702 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2702_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2703_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:399px;
  height:46px;
  background:inherit;
  background-color:rgba(0, 255, 255, 1);
  border:none;
  border-radius:282px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u2703 {
  border-width:0px;
  position:absolute;
  left:61px;
  top:862px;
  width:399px;
  height:46px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u2703 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2703_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2704_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:246px;
  height:71px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2704 {
  border-width:0px;
  position:absolute;
  left:118px;
  top:705px;
  width:246px;
  height:71px;
  display:flex;
}
#u2704 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2704_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2705_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:80px;
}
#u2705 {
  border-width:0px;
  position:absolute;
  left:210px;
  top:548px;
  width:80px;
  height:80px;
  display:flex;
}
#u2705 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2705_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2706_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:111px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:center;
}
#u2706 {
  border-width:0px;
  position:absolute;
  left:191px;
  top:628px;
  width:111px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:center;
}
#u2706 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2706_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
