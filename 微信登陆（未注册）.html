﻿<!DOCTYPE html>
<html>
  <head>
    <title>微信登陆（未注册）</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/微信登陆（未注册）/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/微信登陆（未注册）/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u2674" class="ax_default box_1">
        <div id="u2674_div" class=""></div>
        <div id="u2674_text" class="text ">
          <p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2675" class="ax_default _二级标题">
        <div id="u2675_div" class=""></div>
        <div id="u2675_text" class="text ">
          <p><span>16:18</span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u2676" class="ax_default icon">
        <img id="u2676_img" class="img " src="images/海融宝签约_个人__f501_f502_/u3.svg"/>
        <div id="u2676_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u2677" class="ax_default icon">
        <img id="u2677_img" class="img " src="images/海融宝签约_个人__f501_f502_/u4.svg"/>
        <div id="u2677_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2678" class="ax_default _文本段落">
        <div id="u2678_div" class=""></div>
        <div id="u2678_text" class="text ">
          <p><span>登陆</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2679" class="ax_default _文本段落">
        <div id="u2679_div" class=""></div>
        <div id="u2679_text" class="text ">
          <p><span>hrypt.com</span></p>
        </div>
      </div>

      <!-- Unnamed (项目logo) -->

      <!-- Unnamed (图片 ) -->
      <div id="u2681" class="ax_default _图片_">
        <img id="u2681_img" class="img " src="images/登陆主界面/u2620.svg"/>
        <div id="u2681_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2682" class="ax_default _文本段落">
        <div id="u2682_div" class=""></div>
        <div id="u2682_text" class="text ">
          <p><span>海融宝清算平台</span></p>
        </div>
      </div>
      <div id="u2680" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (矩形) -->
      <div id="u2683" class="ax_default _一级标题">
        <div id="u2683_div" class=""></div>
        <div id="u2683_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2684" class="ax_default box_1">
        <div id="u2684_div" class=""></div>
        <div id="u2684_text" class="text ">
          <p><span>微信一键登录</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2685" class="ax_default _文本段落">
        <div id="u2685_div" class=""></div>
        <div id="u2685_text" class="text ">
          <p><span style="color:#999999;">首次登录会自动进行注册，注册即为同意</span><span style="color:#33CC00;">《用户协议》《隐私政策》</span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u2686" class="ax_default icon">
        <img id="u2686_img" class="img " src="images/登陆主界面/u2637.svg"/>
        <div id="u2686_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u2687" class="ax_default icon">
        <img id="u2687_img" class="img " src="images/登陆主界面/u2624.svg"/>
        <div id="u2687_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2688" class="ax_default box_1">
        <div id="u2688_div" class=""></div>
        <div id="u2688_text" class="text ">
          <p><span>&nbsp; 请输入手机号</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2689" class="ax_default box_1">
        <div id="u2689_div" class=""></div>
        <div id="u2689_text" class="text ">
          <p><span>&nbsp; 请输入密码</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2690" class="ax_default _文本段落">
        <div id="u2690_div" class=""></div>
        <div id="u2690_text" class="text ">
          <p><span>忘记密码&gt;</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2691" class="ax_default _文本段落">
        <div id="u2691_div" class=""></div>
        <div id="u2691_text" class="text ">
          <p><span>注册登记&gt;</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2692" class="ax_default primary_button">
        <div id="u2692_div" class=""></div>
        <div id="u2692_text" class="text ">
          <p><span>登录</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2693" class="ax_default primary_button">
        <div id="u2693_div" class=""></div>
        <div id="u2693_text" class="text ">
          <p><span>快捷登录</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u2694" class="ax_default" data-left="0" data-top="0" data-width="510" data-height="1021">

        <!-- Unnamed (矩形) -->
        <div id="u2695" class="ax_default box_1">
          <div id="u2695_div" class=""></div>
          <div id="u2695_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u2696" class="ax_default" data-left="0" data-top="459" data-width="510" data-height="562">

          <!-- Unnamed (矩形) -->
          <div id="u2697" class="ax_default _形状">
            <div id="u2697_div" class=""></div>
            <div id="u2697_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (图片 ) -->
          <div id="u2698" class="ax_default _图片_">
            <img id="u2698_img" class="img " src="images/充值方式/u1461.png"/>
            <div id="u2698_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (形状) -->
          <div id="u2699" class="ax_default _文本段落">
            <img id="u2699_img" class="img " src="images/充值方式/u1462.svg"/>
            <div id="u2699_text" class="text ">
              <p><span>微信授权登陆</span></p>
            </div>
          </div>

          <!-- Unnamed (线段) -->
          <div id="u2700" class="ax_default line">
            <img id="u2700_img" class="img " src="images/充值方式/u1463.svg"/>
            <div id="u2700_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2701" class="ax_default _文本段落">
        <div id="u2701_div" class=""></div>
        <div id="u2701_text" class="text ">
          <p><span>《用户协议》《隐私政策》</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2702" class="ax_default box_1">
        <div id="u2702_div" class=""></div>
        <div id="u2702_text" class="text ">
          <p><span>授权登录</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2703" class="ax_default box_1">
        <div id="u2703_div" class=""></div>
        <div id="u2703_text" class="text ">
          <p><span>返回登陆页</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2704" class="ax_default _文本段落">
        <div id="u2704_div" class=""></div>
        <div id="u2704_text" class="text ">
          <p style="font-size:16px;"><span style="font-family:'Arial Negreta', 'Arial Normal', 'Arial';font-weight:700;">登录后开发者将获取一下权限</span></p><ul><li style="font-size:12px;"><span style="font-family:'Arial Normal', 'Arial';font-weight:400;">获取你的公开信息（昵称、头像、地区及性别等）</span></li><li style="font-size:12px;"><span style="font-family:'Arial Normal', 'Arial';font-weight:400;">寻找与你共同使用该应用的好友</span></li></ul>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u2705" class="ax_default _图片_">
        <img id="u2705_img" class="img " src="images/微信登陆（未注册）/u2705.png"/>
        <div id="u2705_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2706" class="ax_default _一级标题">
        <div id="u2706_div" class=""></div>
        <div id="u2706_text" class="text ">
          <p><span>党军强</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
