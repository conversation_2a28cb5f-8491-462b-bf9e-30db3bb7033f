﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:508px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u3534_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:900px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:50px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3534 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:900px;
  display:flex;
  opacity:0.49;
}
#u3534 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3534_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3535 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3536 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3537_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u3537 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:834px;
  width:26px;
  height:25px;
  display:flex;
}
#u3537 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3537_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3538_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u3538 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:860px;
  width:26px;
  height:14px;
  display:flex;
  text-align:center;
}
#u3538 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3538_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3539 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3540_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u3540 {
  border-width:0px;
  position:absolute;
  left:130px;
  top:834px;
  width:26px;
  height:25px;
  display:flex;
}
#u3540 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3540_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3541_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u3541 {
  border-width:0px;
  position:absolute;
  left:130px;
  top:860px;
  width:26px;
  height:14px;
  display:flex;
  text-align:center;
}
#u3541 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3541_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3542 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3543_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u3543 {
  border-width:0px;
  position:absolute;
  left:345px;
  top:834px;
  width:26px;
  height:25px;
  display:flex;
}
#u3543 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3543_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3544_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  background-image:url('../../images/平台首页/u2795.png');
  background-repeat:no-repeat;
  background-size:200px 200px;
  background-position: left top;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u3544 {
  border-width:0px;
  position:absolute;
  left:345px;
  top:860px;
  width:26px;
  height:14px;
  display:flex;
  text-align:center;
}
#u3544 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3544_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3545 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3546_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u3546 {
  border-width:0px;
  position:absolute;
  left:452px;
  top:834px;
  width:26px;
  height:25px;
  display:flex;
}
#u3546 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3546_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3547_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u3547 {
  border-width:0px;
  position:absolute;
  left:452px;
  top:860px;
  width:26px;
  height:14px;
  display:flex;
  text-align:center;
}
#u3547 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3547_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3548 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3549_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u3549 {
  border-width:0px;
  position:absolute;
  left:237px;
  top:834px;
  width:26px;
  height:25px;
  display:flex;
}
#u3549 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3549_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3550_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:44px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u3550 {
  border-width:0px;
  position:absolute;
  left:228px;
  top:860px;
  width:44px;
  height:14px;
  display:flex;
  text-align:center;
}
#u3550 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3550_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3551_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 0, 0, 1);
  box-sizing:border-box;
  border-width:2px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:75px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u3551 {
  border-width:0px;
  position:absolute;
  left:363px;
  top:834px;
  width:21px;
  height:15px;
  display:flex;
  color:#FFFFFF;
}
#u3551 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3551_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3552_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u3552 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:20px;
  width:51px;
  height:40px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u3552 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3552_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3553_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:18px;
}
#u3553 {
  border-width:0px;
  position:absolute;
  left:425px;
  top:19px;
  width:23px;
  height:18px;
  display:flex;
}
#u3553 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3553_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3554_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:16px;
}
#u3554 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:21px;
  width:26px;
  height:16px;
  display:flex;
}
#u3554 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3554_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3555_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:25px;
}
#u3555 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:50px;
  width:24px;
  height:25px;
  display:flex;
}
#u3555 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3555_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3556_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u3556 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:49px;
  width:300px;
  height:25px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u3556 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3556_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3557 {
  position:fixed;
  left:50%;
  margin-left:-75px;
  top:50%;
  margin-top:-25px;
  visibility:hidden;
}
#u3557_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3557_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u3558_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u3558 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u3558 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u3558_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3557_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u3557_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u3559_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u3559 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u3559 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u3559_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3560_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u3560 {
  border-width:0px;
  position:absolute;
  left:60px;
  top:16px;
  width:80px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u3560 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3560_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3561_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u3561 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:10px;
  width:30px;
  height:30px;
  display:flex;
}
#u3561 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3561_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3562_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:228px;
  height:11px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  text-align:center;
}
#u3562 {
  border-width:0px;
  position:absolute;
  left:136px;
  top:71px;
  width:228px;
  height:11px;
  display:flex;
  font-size:10px;
  text-align:center;
}
#u3562 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3562_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3563_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:80px;
}
#u3563 {
  border-width:0px;
  position:absolute;
  left:12px;
  top:46px;
  width:80px;
  height:80px;
  display:flex;
}
#u3563 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3563_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3564 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3565 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3566_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:152px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3566 {
  border-width:0px;
  position:absolute;
  left:102px;
  top:88px;
  width:152px;
  height:40px;
  display:flex;
}
#u3566 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3566_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3567_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u3567 {
  border-width:0px;
  position:absolute;
  left:109px;
  top:95px;
  width:139px;
  height:26px;
  display:flex;
  font-size:18px;
}
#u3567 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3567_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3568 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3569_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:179px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(235, 223, 245, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u3569 {
  border-width:0px;
  position:absolute;
  left:287px;
  top:88px;
  width:179px;
  height:40px;
  display:flex;
  font-size:18px;
}
#u3569 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3569_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3570_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:170px;
  height:23px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#8400FF;
}
#u3570 {
  border-width:0px;
  position:absolute;
  left:296px;
  top:97px;
  width:170px;
  height:23px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#8400FF;
}
#u3570 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3570_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3571_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:22px;
  background:inherit;
  background-color:rgba(204, 204, 204, 1);
  border:none;
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:center;
}
#u3571 {
  border-width:0px;
  position:absolute;
  left:23px;
  top:104px;
  width:60px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:center;
}
#u3571 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3571_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3572_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:496px;
  height:130px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3572 {
  border-width:0px;
  position:absolute;
  left:7px;
  top:146px;
  width:496px;
  height:130px;
  display:flex;
}
#u3572 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3572_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3573 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3574_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:154px;
  height:96px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u3574 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:174px;
  width:154px;
  height:96px;
  display:flex;
  font-size:18px;
}
#u3574 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3574_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3575_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:154px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:center;
}
#u3575 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:208px;
  width:154px;
  height:28px;
  display:flex;
  font-size:16px;
  text-align:center;
}
#u3575 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3575_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3576_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:28px;
}
#u3576 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:180px;
  width:27px;
  height:28px;
  display:flex;
  font-size:18px;
}
#u3576 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3576_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3577_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u3577 {
  border-width:0px;
  position:absolute;
  left:51px;
  top:182px;
  width:116px;
  height:21px;
  display:flex;
  font-size:18px;
}
#u3577 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3577_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3578_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:19px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u3578 {
  border-width:0px;
  position:absolute;
  left:96px;
  top:244px;
  width:65px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u3578 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3578_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3579_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:19px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u3579 {
  border-width:0px;
  position:absolute;
  left:21px;
  top:244px;
  width:65px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u3579 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3579_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3580 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3581_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:154px;
  height:96px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u3581 {
  border-width:0px;
  position:absolute;
  left:176px;
  top:174px;
  width:154px;
  height:96px;
  display:flex;
  font-size:18px;
}
#u3581 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3581_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3582_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:154px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:center;
}
#u3582 {
  border-width:0px;
  position:absolute;
  left:176px;
  top:208px;
  width:154px;
  height:28px;
  display:flex;
  font-size:16px;
  text-align:center;
}
#u3582 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3582_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3583_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:28px;
}
#u3583 {
  border-width:0px;
  position:absolute;
  left:183px;
  top:180px;
  width:27px;
  height:28px;
  display:flex;
  font-size:18px;
}
#u3583 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3583_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3584_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:43px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u3584 {
  border-width:0px;
  position:absolute;
  left:214px;
  top:182px;
  width:116px;
  height:43px;
  display:flex;
  font-size:18px;
}
#u3584 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3584_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3585_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:19px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u3585 {
  border-width:0px;
  position:absolute;
  left:258px;
  top:244px;
  width:65px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u3585 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3585_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3586_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:19px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u3586 {
  border-width:0px;
  position:absolute;
  left:183px;
  top:244px;
  width:65px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u3586 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3586_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3587 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3588_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:154px;
  height:96px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u3588 {
  border-width:0px;
  position:absolute;
  left:339px;
  top:174px;
  width:154px;
  height:96px;
  display:flex;
  font-size:18px;
}
#u3588 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3588_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3589_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:154px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:center;
}
#u3589 {
  border-width:0px;
  position:absolute;
  left:339px;
  top:208px;
  width:154px;
  height:28px;
  display:flex;
  font-size:16px;
  text-align:center;
}
#u3589 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3589_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3590_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:28px;
}
#u3590 {
  border-width:0px;
  position:absolute;
  left:346px;
  top:180px;
  width:27px;
  height:28px;
  display:flex;
  font-size:18px;
}
#u3590 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3590_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3591_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:43px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u3591 {
  border-width:0px;
  position:absolute;
  left:377px;
  top:182px;
  width:116px;
  height:43px;
  display:flex;
  font-size:18px;
}
#u3591 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3591_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3592_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:19px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u3592 {
  border-width:0px;
  position:absolute;
  left:419px;
  top:244px;
  width:65px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u3592 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3592_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3593_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:19px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u3593 {
  border-width:0px;
  position:absolute;
  left:344px;
  top:244px;
  width:65px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u3593 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3593_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3594_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
  color:#8400FF;
}
#u3594 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:151px;
  width:112px;
  height:18px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
  color:#8400FF;
}
#u3594 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3594_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3595_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:494px;
  height:330px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3595 {
  border-width:0px;
  position:absolute;
  left:7px;
  top:283px;
  width:494px;
  height:330px;
  display:flex;
}
#u3595 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3595_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3596 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3597_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:155px;
  height:90px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
}
#u3597 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:411px;
  width:155px;
  height:90px;
  display:flex;
  font-size:20px;
}
#u3597 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3597_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3598_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:155px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u3598 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:461px;
  width:155px;
  height:40px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u3598 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3598_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3599_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:50px;
}
#u3599 {
  border-width:0px;
  position:absolute;
  left:66px;
  top:416px;
  width:48px;
  height:50px;
  display:flex;
}
#u3599 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3599_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3600 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3601_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:155px;
  height:90px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
}
#u3601 {
  border-width:0px;
  position:absolute;
  left:176px;
  top:505px;
  width:155px;
  height:90px;
  display:flex;
  font-size:20px;
}
#u3601 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3601_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3602_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:155px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u3602 {
  border-width:0px;
  position:absolute;
  left:176px;
  top:556px;
  width:155px;
  height:40px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u3602 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3602_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3603_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:50px;
}
#u3603 {
  border-width:0px;
  position:absolute;
  left:229px;
  top:510px;
  width:48px;
  height:50px;
  display:flex;
}
#u3603 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3603_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3604 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3605_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:155px;
  height:90px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
}
#u3605 {
  border-width:0px;
  position:absolute;
  left:176px;
  top:411px;
  width:155px;
  height:90px;
  display:flex;
  font-size:20px;
}
#u3605 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3605_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3606_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:155px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u3606 {
  border-width:0px;
  position:absolute;
  left:176px;
  top:462px;
  width:155px;
  height:40px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u3606 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3606_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3607_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:50px;
}
#u3607 {
  border-width:0px;
  position:absolute;
  left:229px;
  top:416px;
  width:48px;
  height:50px;
  display:flex;
}
#u3607 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3607_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3608 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3609_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:155px;
  height:90px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
}
#u3609 {
  border-width:0px;
  position:absolute;
  left:338px;
  top:311px;
  width:155px;
  height:90px;
  display:flex;
  font-size:20px;
}
#u3609 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3609_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3610_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:155px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u3610 {
  border-width:0px;
  position:absolute;
  left:338px;
  top:361px;
  width:155px;
  height:40px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u3610 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3610_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3611_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:50px;
}
#u3611 {
  border-width:0px;
  position:absolute;
  left:391px;
  top:316px;
  width:48px;
  height:50px;
  display:flex;
}
#u3611 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3611_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3612 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3613_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:155px;
  height:90px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
}
#u3613 {
  border-width:0px;
  position:absolute;
  left:338px;
  top:410px;
  width:155px;
  height:90px;
  display:flex;
  font-size:20px;
}
#u3613 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3613_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3614_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:155px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u3614 {
  border-width:0px;
  position:absolute;
  left:338px;
  top:461px;
  width:155px;
  height:40px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u3614 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3614_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3615_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:50px;
}
#u3615 {
  border-width:0px;
  position:absolute;
  left:391px;
  top:415px;
  width:48px;
  height:50px;
  display:flex;
}
#u3615 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3615_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3616_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
  color:#8400FF;
}
#u3616 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:288px;
  width:96px;
  height:18px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
  color:#8400FF;
}
#u3616 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3616_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3617 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3618_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:152px;
  height:90px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
}
#u3618 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:311px;
  width:152px;
  height:90px;
  display:flex;
  font-size:20px;
}
#u3618 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3618_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3619_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:152px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u3619 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:361px;
  width:152px;
  height:40px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u3619 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3619_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3620_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:50px;
}
#u3620 {
  border-width:0px;
  position:absolute;
  left:229px;
  top:316px;
  width:48px;
  height:50px;
  display:flex;
}
#u3620 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3620_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3621 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3622_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:155px;
  height:90px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
}
#u3622 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:311px;
  width:155px;
  height:90px;
  display:flex;
  font-size:20px;
}
#u3622 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3622_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3623_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:155px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u3623 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:361px;
  width:155px;
  height:40px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u3623 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3623_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3624_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:50px;
}
#u3624 {
  border-width:0px;
  position:absolute;
  left:66px;
  top:316px;
  width:48px;
  height:50px;
  display:flex;
}
#u3624 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3624_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3625 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3626_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:155px;
  height:90px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
}
#u3626 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:505px;
  width:155px;
  height:90px;
  display:flex;
  font-size:20px;
}
#u3626 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3626_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3627_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:155px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u3627 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:556px;
  width:155px;
  height:40px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u3627 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3627_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3628_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:50px;
}
#u3628 {
  border-width:0px;
  position:absolute;
  left:66px;
  top:510px;
  width:48px;
  height:50px;
  display:flex;
}
#u3628 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3628_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3629 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3630 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3631_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:367px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3631 {
  border-width:0px;
  position:absolute;
  left:103px;
  top:43px;
  width:367px;
  height:40px;
  display:flex;
}
#u3631 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3631_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3632_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:349px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u3632 {
  border-width:0px;
  position:absolute;
  left:112px;
  top:50px;
  width:349px;
  height:26px;
  display:flex;
  font-size:18px;
}
#u3632 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3632_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3633_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:322px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AEAEAE;
  text-align:left;
}
#u3633 {
  border-width:0px;
  position:absolute;
  left:3px;
  top:620px;
  width:500px;
  height:322px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AEAEAE;
  text-align:left;
}
#u3633 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3633_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3634 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3636 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3637_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u3637 {
  border-width:0px;
  position:absolute;
  left:48px;
  top:840px;
  width:260px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u3637 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3637_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3638_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:30px;
}
#u3638 {
  border-width:0px;
  position:absolute;
  left:477px;
  top:840px;
  width:15px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u3638 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3638_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3639_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u3639 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:840px;
  width:30px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u3639 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3639_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3640_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:159px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u3640 {
  border-width:0px;
  position:absolute;
  left:308px;
  top:840px;
  width:159px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u3640 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3640_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3641_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u3641 {
  border-width:0px;
  position:absolute;
  left:3px;
  top:880px;
  width:500px;
  height:1px;
  display:flex;
}
#u3641 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3641_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3642_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u3642 {
  border-width:0px;
  position:absolute;
  left:3px;
  top:831px;
  width:500px;
  height:1px;
  display:flex;
}
#u3642 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3642_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3644 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3645_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u3645 {
  border-width:0px;
  position:absolute;
  left:48px;
  top:790px;
  width:260px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u3645 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3645_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3646_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:30px;
}
#u3646 {
  border-width:0px;
  position:absolute;
  left:477px;
  top:790px;
  width:15px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u3646 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3646_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3647_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u3647 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:790px;
  width:30px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u3647 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3647_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3648_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:159px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u3648 {
  border-width:0px;
  position:absolute;
  left:308px;
  top:790px;
  width:159px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u3648 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3648_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3649_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u3649 {
  border-width:0px;
  position:absolute;
  left:3px;
  top:830px;
  width:500px;
  height:1px;
  display:flex;
}
#u3649 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3649_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3650_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u3650 {
  border-width:0px;
  position:absolute;
  left:3px;
  top:781px;
  width:500px;
  height:1px;
  display:flex;
}
#u3650 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3650_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3652 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3653_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u3653 {
  border-width:0px;
  position:absolute;
  left:48px;
  top:740px;
  width:260px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u3653 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3653_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3654_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:30px;
}
#u3654 {
  border-width:0px;
  position:absolute;
  left:477px;
  top:740px;
  width:15px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u3654 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3654_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3655_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u3655 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:740px;
  width:30px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u3655 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3655_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3656_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:159px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u3656 {
  border-width:0px;
  position:absolute;
  left:308px;
  top:740px;
  width:159px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u3656 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3656_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3657_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u3657 {
  border-width:0px;
  position:absolute;
  left:3px;
  top:780px;
  width:500px;
  height:1px;
  display:flex;
}
#u3657 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3657_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3658_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u3658 {
  border-width:0px;
  position:absolute;
  left:3px;
  top:731px;
  width:500px;
  height:1px;
  display:flex;
}
#u3658 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3658_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3660 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3661_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u3661 {
  border-width:0px;
  position:absolute;
  left:48px;
  top:690px;
  width:260px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u3661 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3661_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3662_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:30px;
}
#u3662 {
  border-width:0px;
  position:absolute;
  left:477px;
  top:690px;
  width:15px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u3662 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3662_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3663_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u3663 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:690px;
  width:30px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u3663 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3663_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3664_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:159px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u3664 {
  border-width:0px;
  position:absolute;
  left:308px;
  top:690px;
  width:159px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u3664 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3664_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3665_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u3665 {
  border-width:0px;
  position:absolute;
  left:3px;
  top:730px;
  width:500px;
  height:1px;
  display:flex;
}
#u3665 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3665_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3666_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u3666 {
  border-width:0px;
  position:absolute;
  left:3px;
  top:681px;
  width:500px;
  height:1px;
  display:flex;
}
#u3666 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3666_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3668 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3669_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u3669 {
  border-width:0px;
  position:absolute;
  left:48px;
  top:640px;
  width:260px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u3669 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3669_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3670_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:30px;
}
#u3670 {
  border-width:0px;
  position:absolute;
  left:477px;
  top:640px;
  width:15px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u3670 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3670_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3671_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u3671 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:640px;
  width:30px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u3671 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3671_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3672_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:159px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u3672 {
  border-width:0px;
  position:absolute;
  left:308px;
  top:640px;
  width:159px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u3672 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3672_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3673_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u3673 {
  border-width:0px;
  position:absolute;
  left:3px;
  top:680px;
  width:500px;
  height:1px;
  display:flex;
}
#u3673 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3673_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3674_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u3674 {
  border-width:0px;
  position:absolute;
  left:3px;
  top:631px;
  width:500px;
  height:1px;
  display:flex;
}
#u3674 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3674_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3676 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3677_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u3677 {
  border-width:0px;
  position:absolute;
  left:48px;
  top:891px;
  width:260px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u3677 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3677_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3678_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:30px;
}
#u3678 {
  border-width:0px;
  position:absolute;
  left:477px;
  top:891px;
  width:15px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u3678 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3678_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3679_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u3679 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:891px;
  width:30px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u3679 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3679_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3680_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:159px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u3680 {
  border-width:0px;
  position:absolute;
  left:308px;
  top:891px;
  width:159px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u3680 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3680_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3681_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u3681 {
  border-width:0px;
  position:absolute;
  left:3px;
  top:931px;
  width:500px;
  height:1px;
  display:flex;
}
#u3681 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3681_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3682_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u3682 {
  border-width:0px;
  position:absolute;
  left:3px;
  top:882px;
  width:500px;
  height:1px;
  display:flex;
}
#u3682 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3682_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3683 {
  border-width:0px;
  position:absolute;
  left:11px;
  top:641px;
  width:480px;
  height:30px;
  overflow:hidden;
  background-image:url('../../resources/images/transparent.gif');
}
#u3684 {
  border-width:0px;
  position:absolute;
  left:11px;
  top:691px;
  width:480px;
  height:30px;
  overflow:hidden;
  background-image:url('../../resources/images/transparent.gif');
}
#u3685 {
  border-width:0px;
  position:absolute;
  left:11px;
  top:741px;
  width:480px;
  height:30px;
  overflow:hidden;
  background-image:url('../../resources/images/transparent.gif');
}
#u3686 {
  border-width:0px;
  position:absolute;
  left:11px;
  top:791px;
  width:480px;
  height:30px;
  overflow:hidden;
  background-image:url('../../resources/images/transparent.gif');
}
#u3687 {
  border-width:0px;
  position:absolute;
  left:11px;
  top:841px;
  width:480px;
  height:30px;
  overflow:hidden;
  background-image:url('../../resources/images/transparent.gif');
}
#u3688 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:892px;
  width:480px;
  height:30px;
  overflow:hidden;
  background-image:url('../../resources/images/transparent.gif');
}
