﻿$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,_(g,h,i,_(j,k,l,k)),m,[],n,_(h,o),p,[q],r,_(s,t,u,v,g,w,x,_(),y,[],z,_(A,B,C,D,E,_(F,G,H,I),J,null,K,L,L,M,N,O,null,P,Q,R,S,T,U,V,Q,W,X,_(F,G,H,Y),Z,Q,ba,bb,_(bc,bd,be,bf,bg,bf,bh,bf,H,_(bi,bj,bk,bj,bl,bj,bm,bn)),i,_(j,k,l,k)),bo,_(),bp,_(),bq,_(br,[_(bs,bt,bu,h,bv,bw,u,bx,by,bx,bz,bA,z,_(i,_(j,bB,l,bC)),bo,_(),bD,_(),bE,bF),_(bs,bG,bu,h,bv,bH,u,bI,by,bI,bz,bA,z,_(i,_(j,bJ,l,bK),A,bL,bM,_(bN,bO,bP,bQ),Z,bR,bS,bT),bo,_(),bD,_(),bp,_(bU,_(bV,bW,bX,bY,bZ,[_(bX,h,ca,h,cb,bd,cc,cd,ce,[_(cf,cg,bX,ch,ci,cj,ck,_(cl,_(h,ch)),cm,_(cn,r,b,co,cp,bA),cq,cr)])])),cs,bA,ct,bd),_(bs,cu,bu,h,bv,bH,u,bI,by,bI,bz,bA,z,_(A,cv,i,_(j,cw,l,cx),bM,_(bN,cy,bP,cz)),bo,_(),bD,_(),ct,bd),_(bs,cA,bu,h,bv,bH,u,bI,by,bI,bz,bA,z,_(A,cB,i,_(j,cC,l,cD),Z,cE,bM,_(bN,cF,bP,cG),bS,cH),bo,_(),bD,_(),ct,bd),_(bs,cI,bu,h,bv,cJ,u,cK,by,cK,bz,bA,z,_(),bo,_(),bD,_(),cL,[_(bs,cM,bu,h,bv,cN,u,cO,by,cO,bz,bA,z,_(i,_(j,cP,l,cQ),cR,_(cS,_(A,cT),cU,_(A,cV)),A,cW,bM,_(bN,cX,bP,cY)),cZ,bd,bo,_(),bD,_(),da,h),_(bs,db,bu,h,bv,bH,u,bI,by,bI,bz,bA,z,_(dc,_(F,G,H,dd,de,df),A,cv,i,_(j,dg,l,dh),bS,cH,bM,_(bN,di,bP,dj),dk,dl),bo,_(),bD,_(),ct,bd)],dm,bd),_(bs,dn,bu,h,bv,bH,u,bI,by,bI,bz,bA,z,_(dc,_(F,G,H,dp,de,df),A,cv,i,_(j,dq,l,dh),bS,dr,bM,_(bN,ds,bP,dt),dk,dl),bo,_(),bD,_(),ct,bd),_(bs,du,bu,h,bv,bH,u,bI,by,bI,bz,bA,z,_(A,cv,i,_(j,cQ,l,dv),bM,_(bN,dw,bP,dx),bS,dy),bo,_(),bD,_(),ct,bd),_(bs,dz,bu,h,bv,bH,u,bI,by,bI,bz,bA,z,_(dc,_(F,G,H,dA,de,df),A,cv,i,_(j,dB,l,bO),bM,_(bN,dC,bP,dD),bS,dE,dk,dl,dF,dG),bo,_(),bD,_(),bp,_(bU,_(bV,bW,bX,bY,bZ,[_(bX,h,ca,h,cb,bd,cc,cd,ce,[_(cf,cg,bX,dH,ci,cj,ck,_(dI,_(h,dH)),cm,_(cn,r,b,dJ,cp,bA),cq,cr)])])),cs,bA,ct,bd),_(bs,dK,bu,h,bv,bH,u,bI,by,bI,bz,bA,z,_(A,cv,i,_(j,df,l,dL),bM,_(bN,dM,bP,dN)),bo,_(),bD,_(),ct,bd),_(bs,dO,bu,h,bv,bH,u,bI,by,bI,bz,bA,z,_(T,dP,dc,_(F,G,H,dQ,de,df),A,cv,i,_(j,dR,l,bO),bS,dS,bM,_(bN,dw,bP,dT)),bo,_(),bD,_(),ct,bd),_(bs,dU,bu,h,bv,bH,u,bI,by,bI,bz,bA,z,_(A,cv,i,_(j,dV,l,dW),bM,_(bN,dX,bP,dY)),bo,_(),bD,_(),ct,bd),_(bs,dZ,bu,h,bv,bH,u,bI,by,bI,bz,bA,z,_(i,_(j,ea,l,eb),A,bL,bM,_(bN,ec,bP,ed)),bo,_(),bD,_(),bp,_(bU,_(bV,bW,bX,bY,bZ,[_(bX,h,ca,h,cb,bd,cc,cd,ce,[_(cf,cg,bX,ch,ci,cj,ck,_(cl,_(h,ch)),cm,_(cn,r,b,co,cp,bA),cq,cr)])])),cs,bA,ct,bd),_(bs,ee,bu,h,bv,bH,u,bI,by,bI,bz,bA,z,_(i,_(j,ea,l,eb),A,bL,bM,_(bN,ec,bP,ef)),bo,_(),bD,_(),bp,_(bU,_(bV,bW,bX,bY,bZ,[_(bX,h,ca,h,cb,bd,cc,cd,ce,[_(cf,cg,bX,eg,ci,cj,ck,_(eh,_(h,eg)),cm,_(cn,r,b,ei,cp,bA),cq,cr)])])),cs,bA,ct,bd),_(bs,ej,bu,h,bv,bH,u,bI,by,bI,bz,bA,z,_(A,cv,i,_(j,ek,l,el),bM,_(bN,dX,bP,em)),bo,_(),bD,_(),ct,bd)])),en,_(eo,_(s,eo,u,ep,g,bw,x,_(),y,[],z,_(A,B,C,D,E,_(F,G,H,I),J,null,K,L,L,M,N,O,null,P,Q,R,S,T,U,V,Q,W,X,_(F,G,H,Y),Z,Q,ba,bb,_(bc,bd,be,bf,bg,bf,bh,bf,H,_(bi,bj,bk,bj,bl,bj,bm,bn)),i,_(j,k,l,k)),m,[],bp,_(),bq,_(br,[_(bs,eq,bu,h,bv,bH,u,bI,by,bI,bz,bA,z,_(i,_(j,bB,l,er),A,es,Z,et,de,eu),bo,_(),bD,_(),ct,bd),_(bs,ev,bu,h,bv,bH,u,bI,by,bI,bz,bA,z,_(ew,ex,i,_(j,ey,l,ez),A,eA,bM,_(bN,eB,bP,eC),bS,cH),bo,_(),bD,_(),ct,bd),_(bs,eD,bu,h,bv,eE,u,bI,by,bI,bz,bA,z,_(A,eF,i,_(j,eG,l,eH),bM,_(bN,eI,bP,eJ)),bo,_(),bD,_(),eK,_(eL,eM),ct,bd),_(bs,eN,bu,h,bv,eE,u,bI,by,bI,bz,bA,z,_(A,eF,i,_(j,eO,l,eP),bM,_(bN,eQ,bP,eR)),bo,_(),bD,_(),eK,_(eS,eT),ct,bd),_(bs,eU,bu,h,bv,bH,u,bI,by,bI,bz,bA,z,_(A,cv,i,_(j,eV,l,eW),bM,_(bN,eX,bP,bK),bS,dr,dk,dl,dF,D),bo,_(),bD,_(),ct,bd),_(bs,eY,bu,eZ,bv,fa,u,fb,by,fb,bz,bd,z,_(i,_(j,fc,l,bK),bM,_(bN,k,bP,er),bz,bd),bo,_(),bD,_(),fd,D,fe,k,ff,dl,fg,k,fh,bA,fi,fj,fk,bA,dm,bd,fl,[_(bs,fm,bu,fn,u,fo,br,[_(bs,fp,bu,h,bv,bH,fq,eY,fr,bj,u,bI,by,bI,bz,bA,z,_(dc,_(F,G,H,I,de,df),i,_(j,fc,l,bK),A,fs,bS,cH,E,_(F,G,H,ft),fu,fv,Z,cE),bo,_(),bD,_(),ct,bd)],z,_(E,_(F,G,H,fw),J,null,K,L,L,M,N,V,Q,W,X,_(F,G,H,Y),Z,Q,ba,bb,_(bc,bd,be,bf,bg,bf,bh,bf,H,_(bi,bj,bk,bj,bl,bj,bm,bn))),bo,_()),_(bs,fx,bu,fy,u,fo,br,[_(bs,fz,bu,h,bv,bH,fq,eY,fr,fA,u,bI,by,bI,bz,bA,z,_(dc,_(F,G,H,I,de,df),i,_(j,fc,l,bK),A,fs,bS,cH,E,_(F,G,H,fB),fu,fv,Z,cE),bo,_(),bD,_(),ct,bd),_(bs,fC,bu,h,bv,bH,fq,eY,fr,fA,u,bI,by,bI,bz,bA,z,_(dc,_(F,G,H,fD,de,df),A,cv,i,_(j,fE,l,eH),bS,cH,dF,D,bM,_(bN,fF,bP,eP)),bo,_(),bD,_(),ct,bd),_(bs,fG,bu,h,bv,fH,fq,eY,fr,fA,u,fI,by,fI,bz,bA,z,_(A,fJ,i,_(j,eb,l,eb),bM,_(bN,fK,bP,fL),J,null),bo,_(),bD,_(),eK,_(fM,fN))],z,_(E,_(F,G,H,fw),J,null,K,L,L,M,N,V,Q,W,X,_(F,G,H,Y),Z,Q,ba,bb,_(bc,bd,be,bf,bg,bf,bh,bf,H,_(bi,bj,bk,bj,bl,bj,bm,bn))),bo,_())]),_(bs,fO,bu,h,bv,fH,u,fI,by,fI,bz,bA,z,_(A,fJ,i,_(j,eW,l,eW),bM,_(bN,fP,bP,bK),J,null),bo,_(),bD,_(),eK,_(fQ,fR)),_(bs,fS,bu,h,bv,eE,u,bI,by,bI,bz,bA,z,_(A,eF,V,Q,i,_(j,dL,l,eW),E,_(F,G,H,dp),X,_(F,G,H,fw),bb,_(bc,bd,be,k,bg,k,bh,fL,H,_(bi,bj,bk,bj,bl,bj,bm,fT)),fU,_(bc,bd,be,k,bg,k,bh,fL,H,_(bi,bj,bk,bj,bl,bj,bm,fT)),bM,_(bN,eB,bP,bK)),bo,_(),bD,_(),bp,_(bU,_(bV,bW,bX,bY,bZ,[_(bX,h,ca,h,cb,bd,cc,cd,ce,[_(cf,fV,bX,fW,ci,fX)])])),cs,bA,eK,_(fY,fZ),ct,bd),_(bs,ga,bu,h,bv,bH,u,bI,by,bI,bz,bA,z,_(A,cv,i,_(j,gb,l,cF),bM,_(bN,gc,bP,gd),bS,ge,dF,D),bo,_(),bD,_(),ct,bd)]))),gf,_(gg,_(gh,gi,gj,_(gh,gk),gl,_(gh,gm),gn,_(gh,go),gp,_(gh,gq),gr,_(gh,gs),gt,_(gh,gu),gv,_(gh,gw),gx,_(gh,gy),gz,_(gh,gA),gB,_(gh,gC),gD,_(gh,gE),gF,_(gh,gG),gH,_(gh,gI)),gJ,_(gh,gK),gL,_(gh,gM),gN,_(gh,gO),gP,_(gh,gQ),gR,_(gh,gS),gT,_(gh,gU),gV,_(gh,gW),gX,_(gh,gY),gZ,_(gh,ha),hb,_(gh,hc),hd,_(gh,he),hf,_(gh,hg),hh,_(gh,hi),hj,_(gh,hk),hl,_(gh,hm)));}; 
var b="url",c="子钱包充值_f510_f507_.html",d="generationDate",e=new Date(1752898671878.54),f="defaultAdaptiveView",g="name",h="",i="size",j="width",k=0,l="height",m="adaptiveViews",n="sketchKeys",o="s0",p="variables",q="OnLoadVariable",r="page",s="packageId",t="9623c93ff792488cabee3bb6bbf196e7",u="type",v="Axure:Page",w="子钱包充值(F510\\F507)",x="notes",y="annotations",z="style",A="baseStyle",B="627587b6038d43cca051c114ac41ad32",C="pageAlignment",D="center",E="fill",F="fillType",G="solid",H="color",I=0xFFFFFFFF,J="image",K="imageAlignment",L="near",M="imageRepeat",N="auto",O="favicon",P="sketchFactor",Q="0",R="colorStyle",S="appliedColor",T="fontName",U="Applied Font",V="borderWidth",W="borderVisibility",X="borderFill",Y=0xFF797979,Z="cornerRadius",ba="cornerVisibility",bb="outerShadow",bc="on",bd=false,be="offsetX",bf=5,bg="offsetY",bh="blurRadius",bi="r",bj=0,bk="g",bl="b",bm="a",bn=0.349019607843137,bo="adaptiveStyles",bp="interactionMap",bq="diagram",br="objects",bs="id",bt="df70793c521d496d89807d30054ae919",bu="label",bv="friendlyType",bw="基础app框架(H5)",bx="referenceDiagramObject",by="styleType",bz="visible",bA=true,bB=510,bC=950,bD="imageOverrides",bE="masterId",bF="2ba4949fd6a542ffa65996f1d39439b0",bG="9c70613713014db786db3851520ff718",bH="矩形",bI="vectorShape",bJ=439,bK=50,bL="588c65e91e28430e948dc660c2e7df8d",bM="location",bN="x",bO=32,bP="y",bQ=786,bR="15",bS="fontSize",bT="18px",bU="onClick",bV="eventType",bW="Click时",bX="description",bY="Click or Tap",bZ="cases",ca="conditionString",cb="isNewIfGroup",cc="caseColorHex",cd="9D33FA",ce="actions",cf="action",cg="linkWindow",ch="打开 示意图-邮储充值确认(邮储页面) 在 当前窗口",ci="displayName",cj="打开链接",ck="actionInfoDescriptions",cl="示意图-邮储充值确认(邮储页面)",cm="target",cn="targetType",co="示意图-邮储充值确认_邮储页面_.html",cp="includeVariables",cq="linkType",cr="current",cs="tabbable",ct="generateCompound",cu="12428b5d17884aef9a86db455d71f4d5",cv="4988d43d80b44008a4a415096f1632af",cw=296,cx=167,cy=531,cz=198,cA="701817c6f0fa4f7e8c5a227e58fc5b8e",cB="40519e9ec4264601bfb12c514e4f4867",cC=460,cD=169,cE="5",cF=11,cG=119,cH="16px",cI="f695b390c432460b91a557299ff493f4",cJ="组合",cK="layer",cL="objs",cM="7ca3564acc654da8b0a666768d848547",cN="文本框",cO="textBox",cP=317,cQ=36,cR="stateStyles",cS="hint",cT="4f2de20c43134cd2a4563ef9ee22a985",cU="disabled",cV="7a92d57016ac4846ae3c8801278c2634",cW="9997b85eaede43e1880476dc96cdaf30",cX=85,cY=180,cZ="HideHintOnFocused",da="placeholderText",db="1ca650d642764cd3a88de18762766130",dc="foreGroundFill",dd=0xFFAAAAAA,de="opacity",df=1,dg=138,dh=33,di=96,dj=182,dk="verticalAlignment",dl="middle",dm="propagate",dn="729af87f119f44eb905197e4109095d1",dp=0xFF000000,dq=225,dr="20px",ds=34,dt=131,du="ea9c87d528b14901af2fac48d4da2ea0",dv=42,dw=49,dx=179,dy="36px",dz="54ba44ae39634de1a1b82caa2f5e779e",dA=0xFF8400FF,dB=353,dC=113,dD=123,dE="14px",dF="horizontalAlignment",dG="right",dH="打开 海融宝数字人民币子钱包(F502\\F503)明细(F506\\F507\\F508\\F509) 在 当前窗口",dI="海融宝数字人民币子钱包(F502\\F503)明细(F506\\F507\\F508\\F509)",dJ="____________f502_f503____f506_f507_f508_f509_.html",dK="1cedca320fd84e21a3caf05d2520f98f",dL=15,dM=542,dN=271,dO="8f57716975714beea456c50f889ae9af",dP="'Nunito Sans'",dQ=0xFFD9001B,dR=389,dS="12px",dT=233,dU="57329ec5c6d64db6afc825feb35dc091",dV=673,dW=252,dX=550,dY=365,dZ="2350f6445a8d43c5a8e874d804da52c3",ea=251,eb=30,ec=220,ed=576,ee="9598e29b6933443388bf5053b8f4273d",ef=615,eg="打开 订单取消关闭支付(F515) 在 当前窗口",eh="订单取消关闭支付(F515)",ei="订单取消关闭支付_f515_.html",ej="c1bff86c384444648dbc2be9d0b8cbb8",ek=791,el=398,em=644,en="masters",eo="2ba4949fd6a542ffa65996f1d39439b0",ep="Axure:Master",eq="dac57e0ca3ce409faa452eb0fc8eb81a",er=900,es="4b7bfc596114427989e10bb0b557d0ce",et="50",eu="0.49",ev="c8e043946b3449e498b30257492c8104",ew="fontWeight",ex="700",ey=51,ez=40,eA="b3a15c9ddde04520be40f94c8168891e",eB=22,eC=20,eD="a51144fb589b4c6eb578160cb5630ca3",eE="形状",eF="a1488a5543e94a8a99005391d65f659f",eG=23,eH=18,eI=425,eJ=19,eK="images",eL="u583~normal~",eM="images/海融宝签约_个人__f501_f502_/u3.svg",eN="598ced9993944690a9921d5171e64625",eO=26,eP=16,eQ=462,eR=21,eS="u584~normal~",eT="images/海融宝签约_个人__f501_f502_/u4.svg",eU="874683054d164363ae6d09aac8dc1980",eV=300,eW=25,eX=100,eY="874e9f226cd0488fb00d2a5054076f72",eZ="操作状态",fa="动态面板",fb="dynamicPanel",fc=150,fd="fixedHorizontal",fe="fixedMarginHorizontal",ff="fixedVertical",fg="fixedMarginVertical",fh="fixedKeepInFront",fi="scrollbars",fj="none",fk="fitToContent",fl="diagrams",fm="79e9e0b789a2492b9f935e56140dfbfc",fn="操作成功",fo="Axure:PanelDiagram",fp="0e0d7fa17c33431488e150a444a35122",fq="parentDynamicPanel",fr="panelIndex",fs="7df6f7f7668b46ba8c886da45033d3c4",ft=0x7F000000,fu="paddingLeft",fv="10",fw=0xFFFFFF,fx="9e7ab27805b94c5ba4316397b2c991d5",fy="操作失败",fz="5dce348e49cb490699e53eb8c742aff2",fA=1,fB=0x7FFFFFFF,fC="465a60dcd11743dc824157aab46488c5",fD=0xFFA30014,fE=80,fF=60,fG="124378459454442e845d09e1dad19b6e",fH="图片 ",fI="imageBox",fJ="********************************",fK=14,fL=10,fM="u590~normal~",fN="images/海融宝签约_个人__f501_f502_/u10.png",fO="ed7a6a58497940529258e39ad5a62983",fP=463,fQ="u591~normal~",fR="images/海融宝签约_个人__f501_f502_/u11.png",fS="ad6f9e7d80604be9a8c4c1c83cef58e5",fT=0.313725490196078,fU="innerShadow",fV="closeCurrent",fW="关闭当前窗口",fX="关闭窗口",fY="u592~normal~",fZ="images/海融宝签约_个人__f501_f502_/u12.svg",ga="d1f5e883bd3e44da89f3645e2b65189c",gb=228,gc=136,gd=71,ge="10px",gf="objectPaths",gg="df70793c521d496d89807d30054ae919",gh="scriptId",gi="u580",gj="dac57e0ca3ce409faa452eb0fc8eb81a",gk="u581",gl="c8e043946b3449e498b30257492c8104",gm="u582",gn="a51144fb589b4c6eb578160cb5630ca3",go="u583",gp="598ced9993944690a9921d5171e64625",gq="u584",gr="874683054d164363ae6d09aac8dc1980",gs="u585",gt="874e9f226cd0488fb00d2a5054076f72",gu="u586",gv="0e0d7fa17c33431488e150a444a35122",gw="u587",gx="5dce348e49cb490699e53eb8c742aff2",gy="u588",gz="465a60dcd11743dc824157aab46488c5",gA="u589",gB="124378459454442e845d09e1dad19b6e",gC="u590",gD="ed7a6a58497940529258e39ad5a62983",gE="u591",gF="ad6f9e7d80604be9a8c4c1c83cef58e5",gG="u592",gH="d1f5e883bd3e44da89f3645e2b65189c",gI="u593",gJ="9c70613713014db786db3851520ff718",gK="u594",gL="12428b5d17884aef9a86db455d71f4d5",gM="u595",gN="701817c6f0fa4f7e8c5a227e58fc5b8e",gO="u596",gP="f695b390c432460b91a557299ff493f4",gQ="u597",gR="7ca3564acc654da8b0a666768d848547",gS="u598",gT="1ca650d642764cd3a88de18762766130",gU="u599",gV="729af87f119f44eb905197e4109095d1",gW="u600",gX="ea9c87d528b14901af2fac48d4da2ea0",gY="u601",gZ="54ba44ae39634de1a1b82caa2f5e779e",ha="u602",hb="1cedca320fd84e21a3caf05d2520f98f",hc="u603",hd="8f57716975714beea456c50f889ae9af",he="u604",hf="57329ec5c6d64db6afc825feb35dc091",hg="u605",hh="2350f6445a8d43c5a8e874d804da52c3",hi="u606",hj="9598e29b6933443388bf5053b8f4273d",hk="u607",hl="c1bff86c384444648dbc2be9d0b8cbb8",hm="u608";
return _creator();
})());