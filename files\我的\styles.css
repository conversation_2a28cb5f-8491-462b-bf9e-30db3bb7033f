﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:745px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u2901_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:900px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:50px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2901 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:900px;
  display:flex;
  opacity:0.49;
}
#u2901 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2901_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2902 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2903 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2904_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u2904 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:834px;
  width:26px;
  height:25px;
  display:flex;
}
#u2904 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2904_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2905_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u2905 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:860px;
  width:26px;
  height:14px;
  display:flex;
  text-align:center;
}
#u2905 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2905_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2906 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2907_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u2907 {
  border-width:0px;
  position:absolute;
  left:130px;
  top:834px;
  width:26px;
  height:25px;
  display:flex;
}
#u2907 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2907_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2908_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u2908 {
  border-width:0px;
  position:absolute;
  left:130px;
  top:860px;
  width:26px;
  height:14px;
  display:flex;
  text-align:center;
}
#u2908 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2908_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2909 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2910_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u2910 {
  border-width:0px;
  position:absolute;
  left:345px;
  top:834px;
  width:26px;
  height:25px;
  display:flex;
}
#u2910 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2910_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2911_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  background-image:url('../../images/平台首页/u2795.png');
  background-repeat:no-repeat;
  background-size:200px 200px;
  background-position: left top;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u2911 {
  border-width:0px;
  position:absolute;
  left:345px;
  top:860px;
  width:26px;
  height:14px;
  display:flex;
  text-align:center;
}
#u2911 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2911_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2912 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2913_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u2913 {
  border-width:0px;
  position:absolute;
  left:452px;
  top:834px;
  width:26px;
  height:25px;
  display:flex;
}
#u2913 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2913_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2914_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u2914 {
  border-width:0px;
  position:absolute;
  left:452px;
  top:860px;
  width:26px;
  height:14px;
  display:flex;
  text-align:center;
}
#u2914 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2914_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2915 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2916_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u2916 {
  border-width:0px;
  position:absolute;
  left:237px;
  top:834px;
  width:26px;
  height:25px;
  display:flex;
}
#u2916 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2916_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2917_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:44px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u2917 {
  border-width:0px;
  position:absolute;
  left:228px;
  top:860px;
  width:44px;
  height:14px;
  display:flex;
  text-align:center;
}
#u2917 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2917_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2918_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 0, 0, 1);
  box-sizing:border-box;
  border-width:2px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:75px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u2918 {
  border-width:0px;
  position:absolute;
  left:363px;
  top:834px;
  width:21px;
  height:15px;
  display:flex;
  color:#FFFFFF;
}
#u2918 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2918_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2919_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u2919 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:20px;
  width:51px;
  height:40px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u2919 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2919_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2920_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:18px;
}
#u2920 {
  border-width:0px;
  position:absolute;
  left:425px;
  top:19px;
  width:23px;
  height:18px;
  display:flex;
}
#u2920 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2920_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2921_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:16px;
}
#u2921 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:21px;
  width:26px;
  height:16px;
  display:flex;
}
#u2921 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2921_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2922_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:25px;
}
#u2922 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:50px;
  width:24px;
  height:25px;
  display:flex;
}
#u2922 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2922_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2923_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u2923 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:49px;
  width:300px;
  height:25px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u2923 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2923_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2924 {
  position:fixed;
  left:50%;
  margin-left:-75px;
  top:50%;
  margin-top:-25px;
  visibility:hidden;
}
#u2924_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2924_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u2925_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u2925 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u2925 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2925_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2924_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u2924_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u2926_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u2926 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u2926 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2926_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2927_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u2927 {
  border-width:0px;
  position:absolute;
  left:60px;
  top:16px;
  width:80px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u2927 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2927_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2928_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u2928 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:10px;
  width:30px;
  height:30px;
  display:flex;
}
#u2928 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2928_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2929_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:228px;
  height:11px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  text-align:center;
}
#u2929 {
  border-width:0px;
  position:absolute;
  left:136px;
  top:71px;
  width:228px;
  height:11px;
  display:flex;
  font-size:10px;
  text-align:center;
}
#u2929 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2929_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2930_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:100px;
}
#u2930 {
  border-width:0px;
  position:absolute;
  left:23px;
  top:89px;
  width:100px;
  height:100px;
  display:flex;
}
#u2930 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2930_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2931 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2932 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2933 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2934_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:46px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2934 {
  border-width:0px;
  position:absolute;
  left:346px;
  top:93px;
  width:138px;
  height:46px;
  display:flex;
}
#u2934 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2934_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2935 label {
  left:0px;
  width:100%;
}
#u2935_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:2px;
  width:18px;
  height:18px;
}
#u2935 {
  border-width:0px;
  position:absolute;
  left:356px;
  top:105px;
  width:66px;
  height:21px;
  display:flex;
  font-size:18px;
}
#u2935 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u2935_img.selected {
}
#u2935.selected {
}
#u2935_img.disabled {
}
#u2935.disabled {
}
#u2935_text {
  border-width:0px;
  position:absolute;
  left:20px;
  top:0px;
  width:44px;
  word-wrap:break-word;
  text-transform:none;
}
#u2935_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u2936 label {
  left:0px;
  width:100%;
}
#u2936_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:2px;
  width:18px;
  height:18px;
}
#u2936 {
  border-width:0px;
  position:absolute;
  left:413px;
  top:105px;
  width:62px;
  height:21px;
  display:flex;
  font-size:18px;
}
#u2936 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u2936_img.selected {
}
#u2936.selected {
}
#u2936_img.disabled {
}
#u2936.disabled {
}
#u2936_text {
  border-width:0px;
  position:absolute;
  left:20px;
  top:0px;
  width:40px;
  word-wrap:break-word;
  text-transform:none;
}
#u2936_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u2937 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2938_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:46px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2938 {
  border-width:0px;
  position:absolute;
  left:145px;
  top:93px;
  width:188px;
  height:46px;
  display:flex;
}
#u2938 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2938_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2939_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2939 {
  border-width:0px;
  position:absolute;
  left:151px;
  top:101px;
  width:55px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u2939 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2939_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2940_input {
  position:absolute;
  left:0px;
  top:0px;
  width:107px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2940_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:107px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2940_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:107px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u2940 {
  border-width:0px;
  position:absolute;
  left:216px;
  top:101px;
  width:107px;
  height:30px;
  display:flex;
  font-size:16px;
}
#u2940 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2940_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:107px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u2940.disabled {
}
#u2942_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u2942 {
  border-width:0px;
  position:absolute;
  left:95px;
  top:147px;
  width:25px;
  height:25px;
  display:flex;
}
#u2942 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2942_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2943 {
  border-width:0px;
  position:absolute;
  left:95px;
  top:147px;
  width:305px;
  height:185px;
  visibility:hidden;
}
#u2943_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:305px;
  height:185px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2943_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u2944_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:180px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2944 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:180px;
  display:flex;
}
#u2944 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2944_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2945_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:28px;
}
#u2945 {
  border-width:0px;
  position:absolute;
  left:276px;
  top:5px;
  width:23px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:28px;
}
#u2945 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2945_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2946_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:301px;
  height:2px;
}
#u2946 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:60px;
  width:300px;
  height:1px;
  display:flex;
}
#u2946 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2946_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2947_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:165px;
  height:23px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u2947 {
  border-width:0px;
  position:absolute;
  left:62px;
  top:26px;
  width:165px;
  height:23px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u2947 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2947_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2948_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:301px;
  height:2px;
}
#u2948 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:120px;
  width:300px;
  height:1px;
  display:flex;
}
#u2948 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2948_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2949_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:23px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u2949 {
  border-width:0px;
  position:absolute;
  left:70px;
  top:79px;
  width:150px;
  height:23px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u2949 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2949_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2950_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:37px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2950 {
  border-width:0px;
  position:absolute;
  left:70px;
  top:132px;
  width:140px;
  height:37px;
  display:flex;
  font-size:18px;
}
#u2950 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2950_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2951 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2952_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:339px;
  height:46px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2952 {
  border-width:0px;
  position:absolute;
  left:145px;
  top:145px;
  width:339px;
  height:46px;
  display:flex;
}
#u2952 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2952_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2953_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2953 {
  border-width:0px;
  position:absolute;
  left:151px;
  top:153px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u2953 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2953_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2954_input {
  position:absolute;
  left:0px;
  top:0px;
  width:252px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2954_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:252px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2954_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:252px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#AAAAAA;
}
#u2954 {
  border-width:0px;
  position:absolute;
  left:216px;
  top:153px;
  width:252px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#AAAAAA;
}
#u2954 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2954_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:252px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#AAAAAA;
}
#u2954.disabled {
}
#u2955_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:513px;
  height:5px;
}
#u2955 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:213px;
  width:510px;
  height:2px;
  display:flex;
  font-size:18px;
}
#u2955 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2955_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2956 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2957_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:496px;
  height:130px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2957 {
  border-width:0px;
  position:absolute;
  left:8px;
  top:217px;
  width:496px;
  height:130px;
  display:flex;
}
#u2957 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2957_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2958 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2959_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:154px;
  height:96px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2959 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:245px;
  width:154px;
  height:96px;
  display:flex;
  font-size:18px;
}
#u2959 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2959_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2960_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:154px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:center;
}
#u2960 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:279px;
  width:154px;
  height:28px;
  display:flex;
  font-size:16px;
  text-align:center;
}
#u2960 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2960_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2961_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:28px;
}
#u2961 {
  border-width:0px;
  position:absolute;
  left:21px;
  top:251px;
  width:27px;
  height:28px;
  display:flex;
  font-size:18px;
}
#u2961 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2961_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2962_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2962 {
  border-width:0px;
  position:absolute;
  left:52px;
  top:253px;
  width:116px;
  height:21px;
  display:flex;
  font-size:18px;
}
#u2962 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2962_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2963_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:19px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u2963 {
  border-width:0px;
  position:absolute;
  left:97px;
  top:315px;
  width:65px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u2963 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2963_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2964_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:19px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u2964 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:315px;
  width:65px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u2964 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2964_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2965 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2966_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:154px;
  height:96px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2966 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:245px;
  width:154px;
  height:96px;
  display:flex;
  font-size:18px;
}
#u2966 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2966_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2967_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:154px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:center;
}
#u2967 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:279px;
  width:154px;
  height:28px;
  display:flex;
  font-size:16px;
  text-align:center;
}
#u2967 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2967_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2968_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:28px;
}
#u2968 {
  border-width:0px;
  position:absolute;
  left:184px;
  top:251px;
  width:27px;
  height:28px;
  display:flex;
  font-size:18px;
}
#u2968 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2968_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2969_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:43px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2969 {
  border-width:0px;
  position:absolute;
  left:215px;
  top:253px;
  width:116px;
  height:43px;
  display:flex;
  font-size:18px;
}
#u2969 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2969_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2970_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:19px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u2970 {
  border-width:0px;
  position:absolute;
  left:259px;
  top:315px;
  width:65px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u2970 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2970_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2971_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:19px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u2971 {
  border-width:0px;
  position:absolute;
  left:184px;
  top:315px;
  width:65px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u2971 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2971_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2972 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2973_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:154px;
  height:96px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2973 {
  border-width:0px;
  position:absolute;
  left:340px;
  top:245px;
  width:154px;
  height:96px;
  display:flex;
  font-size:18px;
}
#u2973 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2973_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2974_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:154px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:center;
}
#u2974 {
  border-width:0px;
  position:absolute;
  left:340px;
  top:279px;
  width:154px;
  height:28px;
  display:flex;
  font-size:16px;
  text-align:center;
}
#u2974 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2974_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2975_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:28px;
}
#u2975 {
  border-width:0px;
  position:absolute;
  left:347px;
  top:251px;
  width:27px;
  height:28px;
  display:flex;
  font-size:18px;
}
#u2975 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2975_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2976_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:116px;
  height:43px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2976 {
  border-width:0px;
  position:absolute;
  left:378px;
  top:253px;
  width:116px;
  height:43px;
  display:flex;
  font-size:18px;
}
#u2976 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2976_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2977_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:19px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u2977 {
  border-width:0px;
  position:absolute;
  left:420px;
  top:315px;
  width:65px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u2977 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2977_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2978_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:19px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u2978 {
  border-width:0px;
  position:absolute;
  left:345px;
  top:315px;
  width:65px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u2978 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2978_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2979_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
  color:#8400FF;
}
#u2979 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:222px;
  width:112px;
  height:18px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
  color:#8400FF;
}
#u2979 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2979_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2980_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:494px;
  height:233px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2980 {
  border-width:0px;
  position:absolute;
  left:8px;
  top:354px;
  width:494px;
  height:233px;
  display:flex;
}
#u2980 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2980_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2981 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2982_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:155px;
  height:90px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
}
#u2982 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:488px;
  width:155px;
  height:90px;
  display:flex;
  font-size:20px;
}
#u2982 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2982_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2983_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:155px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u2983 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:538px;
  width:155px;
  height:40px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u2983 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2983_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2984_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:50px;
}
#u2984 {
  border-width:0px;
  position:absolute;
  left:67px;
  top:493px;
  width:48px;
  height:50px;
  display:flex;
}
#u2984 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2984_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2985 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2986_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:155px;
  height:90px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
}
#u2986 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:488px;
  width:155px;
  height:90px;
  display:flex;
  font-size:20px;
}
#u2986 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2986_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2987_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:155px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u2987 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:539px;
  width:155px;
  height:40px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u2987 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2987_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2988_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:50px;
}
#u2988 {
  border-width:0px;
  position:absolute;
  left:230px;
  top:493px;
  width:48px;
  height:50px;
  display:flex;
}
#u2988 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2988_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2989 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2990_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:155px;
  height:90px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
}
#u2990 {
  border-width:0px;
  position:absolute;
  left:339px;
  top:382px;
  width:155px;
  height:90px;
  display:flex;
  font-size:20px;
}
#u2990 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2990_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2991_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:155px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u2991 {
  border-width:0px;
  position:absolute;
  left:339px;
  top:433px;
  width:155px;
  height:40px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u2991 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2991_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2992_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:50px;
}
#u2992 {
  border-width:0px;
  position:absolute;
  left:392px;
  top:387px;
  width:48px;
  height:50px;
  display:flex;
}
#u2992 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2992_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2993 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2994_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:155px;
  height:90px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
}
#u2994 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:382px;
  width:155px;
  height:90px;
  display:flex;
  font-size:20px;
}
#u2994 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2994_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2995_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:155px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u2995 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:432px;
  width:155px;
  height:40px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u2995 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2995_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2996_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:50px;
}
#u2996 {
  border-width:0px;
  position:absolute;
  left:67px;
  top:387px;
  width:48px;
  height:50px;
  display:flex;
}
#u2996 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2996_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2997 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2998_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:155px;
  height:90px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
}
#u2998 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:382px;
  width:155px;
  height:90px;
  display:flex;
  font-size:20px;
}
#u2998 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2998_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2999_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:155px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u2999 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:433px;
  width:155px;
  height:40px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u2999 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2999_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:50px;
}
#u3000 {
  border-width:0px;
  position:absolute;
  left:230px;
  top:387px;
  width:48px;
  height:50px;
  display:flex;
}
#u3000 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3000_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3001_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
  color:#8400FF;
}
#u3001 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:359px;
  width:96px;
  height:18px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
  color:#8400FF;
}
#u3001 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3001_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3002_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3002 {
  border-width:0px;
  position:absolute;
  left:626px;
  top:23px;
  width:119px;
  height:30px;
  display:flex;
}
#u3002 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3002_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3003_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:146px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#8080FF;
  text-align:right;
}
#u3003 {
  border-width:0px;
  position:absolute;
  left:338px;
  top:189px;
  width:146px;
  height:26px;
  display:flex;
  font-size:14px;
  color:#8080FF;
  text-align:right;
}
#u3003 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3003_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3004_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:322px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AEAEAE;
  text-align:left;
}
#u3004 {
  border-width:0px;
  position:absolute;
  left:7px;
  top:592px;
  width:500px;
  height:322px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AEAEAE;
  text-align:left;
}
#u3004 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3004_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3005 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3007 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3008_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u3008 {
  border-width:0px;
  position:absolute;
  left:52px;
  top:812px;
  width:260px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u3008 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3008_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3009_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:30px;
}
#u3009 {
  border-width:0px;
  position:absolute;
  left:481px;
  top:812px;
  width:15px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u3009 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3009_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3010_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u3010 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:812px;
  width:30px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u3010 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3010_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3011_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:159px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u3011 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:812px;
  width:159px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u3011 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3011_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3012_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u3012 {
  border-width:0px;
  position:absolute;
  left:7px;
  top:852px;
  width:500px;
  height:1px;
  display:flex;
}
#u3012 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3012_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3013_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u3013 {
  border-width:0px;
  position:absolute;
  left:7px;
  top:803px;
  width:500px;
  height:1px;
  display:flex;
}
#u3013 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3013_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3015 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3016_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u3016 {
  border-width:0px;
  position:absolute;
  left:52px;
  top:762px;
  width:260px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u3016 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3016_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3017_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:30px;
}
#u3017 {
  border-width:0px;
  position:absolute;
  left:481px;
  top:762px;
  width:15px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u3017 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3017_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3018_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u3018 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:762px;
  width:30px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u3018 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3018_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3019_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:159px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u3019 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:762px;
  width:159px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u3019 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3019_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3020_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u3020 {
  border-width:0px;
  position:absolute;
  left:7px;
  top:802px;
  width:500px;
  height:1px;
  display:flex;
}
#u3020 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3020_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3021_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u3021 {
  border-width:0px;
  position:absolute;
  left:7px;
  top:753px;
  width:500px;
  height:1px;
  display:flex;
}
#u3021 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3021_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3023 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3024_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u3024 {
  border-width:0px;
  position:absolute;
  left:52px;
  top:712px;
  width:260px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u3024 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3024_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3025_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:30px;
}
#u3025 {
  border-width:0px;
  position:absolute;
  left:481px;
  top:712px;
  width:15px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u3025 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3025_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3026_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u3026 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:712px;
  width:30px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u3026 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3026_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3027_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:159px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u3027 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:712px;
  width:159px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u3027 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3027_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3028_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u3028 {
  border-width:0px;
  position:absolute;
  left:7px;
  top:752px;
  width:500px;
  height:1px;
  display:flex;
}
#u3028 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3028_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3029_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u3029 {
  border-width:0px;
  position:absolute;
  left:7px;
  top:703px;
  width:500px;
  height:1px;
  display:flex;
}
#u3029 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3029_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3031 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3032_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u3032 {
  border-width:0px;
  position:absolute;
  left:52px;
  top:662px;
  width:260px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u3032 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3032_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3033_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:30px;
}
#u3033 {
  border-width:0px;
  position:absolute;
  left:481px;
  top:662px;
  width:15px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u3033 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3033_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3034_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u3034 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:662px;
  width:30px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u3034 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3034_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3035_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:159px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u3035 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:662px;
  width:159px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u3035 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3035_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3036_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u3036 {
  border-width:0px;
  position:absolute;
  left:7px;
  top:702px;
  width:500px;
  height:1px;
  display:flex;
}
#u3036 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3036_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3037_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u3037 {
  border-width:0px;
  position:absolute;
  left:7px;
  top:653px;
  width:500px;
  height:1px;
  display:flex;
}
#u3037 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3037_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3039 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3040_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u3040 {
  border-width:0px;
  position:absolute;
  left:52px;
  top:612px;
  width:260px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u3040 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3040_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3041_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:30px;
}
#u3041 {
  border-width:0px;
  position:absolute;
  left:481px;
  top:612px;
  width:15px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u3041 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3041_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3042_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u3042 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:612px;
  width:30px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u3042 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3042_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3043_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:159px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u3043 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:612px;
  width:159px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u3043 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3043_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3044_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u3044 {
  border-width:0px;
  position:absolute;
  left:7px;
  top:652px;
  width:500px;
  height:1px;
  display:flex;
}
#u3044 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3044_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3045_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u3045 {
  border-width:0px;
  position:absolute;
  left:7px;
  top:603px;
  width:500px;
  height:1px;
  display:flex;
}
#u3045 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3045_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3047 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3048_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u3048 {
  border-width:0px;
  position:absolute;
  left:52px;
  top:863px;
  width:260px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u3048 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3048_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3049_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:30px;
}
#u3049 {
  border-width:0px;
  position:absolute;
  left:481px;
  top:863px;
  width:15px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u3049 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3049_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3050_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u3050 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:863px;
  width:30px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u3050 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3050_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3051_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:159px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u3051 {
  border-width:0px;
  position:absolute;
  left:312px;
  top:863px;
  width:159px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u3051 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3051_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3052_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u3052 {
  border-width:0px;
  position:absolute;
  left:7px;
  top:903px;
  width:500px;
  height:1px;
  display:flex;
}
#u3052 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3052_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3053_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u3053 {
  border-width:0px;
  position:absolute;
  left:7px;
  top:854px;
  width:500px;
  height:1px;
  display:flex;
}
#u3053 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3053_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3054 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:613px;
  width:480px;
  height:30px;
  overflow:hidden;
  background-image:url('../../resources/images/transparent.gif');
}
#u3055 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:663px;
  width:480px;
  height:30px;
  overflow:hidden;
  background-image:url('../../resources/images/transparent.gif');
}
#u3056 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:713px;
  width:480px;
  height:30px;
  overflow:hidden;
  background-image:url('../../resources/images/transparent.gif');
}
#u3057 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:763px;
  width:480px;
  height:30px;
  overflow:hidden;
  background-image:url('../../resources/images/transparent.gif');
}
#u3058 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:813px;
  width:480px;
  height:30px;
  overflow:hidden;
  background-image:url('../../resources/images/transparent.gif');
}
#u3059 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:864px;
  width:480px;
  height:30px;
  overflow:hidden;
  background-image:url('../../resources/images/transparent.gif');
}
