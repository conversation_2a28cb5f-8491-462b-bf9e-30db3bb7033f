﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:1429px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u3690_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:1280px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:50px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3690 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:1280px;
  display:flex;
  opacity:0.49;
}
#u3690 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3690_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3691_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u3691 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:20px;
  width:51px;
  height:18px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u3691 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3691_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3692_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:18px;
}
#u3692 {
  border-width:0px;
  position:absolute;
  left:425px;
  top:19px;
  width:23px;
  height:18px;
  display:flex;
}
#u3692 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3692_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3693_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:16px;
}
#u3693 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:21px;
  width:26px;
  height:16px;
  display:flex;
}
#u3693 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3693_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3694_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:25px;
}
#u3694 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:50px;
  width:24px;
  height:25px;
  display:flex;
}
#u3694 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3694_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3695_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:22px;
}
#u3695 {
  border-width:0px;
  position:absolute;
  left:467px;
  top:56px;
  width:21px;
  height:22px;
  display:flex;
}
#u3695 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3695_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3696_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:252px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u3696 {
  border-width:0px;
  position:absolute;
  left:124px;
  top:49px;
  width:252px;
  height:25px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u3696 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3696_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3697 {
  position:fixed;
  left:50%;
  margin-left:-75px;
  top:50%;
  margin-top:-25px;
  visibility:hidden;
}
#u3697_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3697_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u3698_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u3698 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u3698 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u3698_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3697_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u3697_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u3699_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u3699 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u3699 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u3699_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3700_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u3700 {
  border-width:0px;
  position:absolute;
  left:60px;
  top:16px;
  width:80px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u3700 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3700_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3701_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u3701 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:10px;
  width:30px;
  height:30px;
  display:flex;
}
#u3701 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3701_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3702_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:228px;
  height:11px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  text-align:center;
}
#u3702 {
  border-width:0px;
  position:absolute;
  left:136px;
  top:71px;
  width:228px;
  height:11px;
  display:flex;
  font-size:10px;
  text-align:center;
}
#u3702 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3702_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3703 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3704_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:150px;
  background:inherit;
  background-color:rgba(244, 250, 255, 1);
  border:none;
  border-radius:18px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3704 {
  border-width:0px;
  position:absolute;
  left:201px;
  top:318px;
  width:150px;
  height:150px;
  display:flex;
}
#u3704 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3704_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3705_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  text-align:center;
}
#u3705 {
  border-width:0px;
  position:absolute;
  left:226px;
  top:417px;
  width:96px;
  height:32px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  text-align:center;
}
#u3705 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3705_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3706_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
}
#u3706 {
  border-width:0px;
  position:absolute;
  left:251px;
  top:350px;
  width:50px;
  height:50px;
  display:flex;
}
#u3706 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3706_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3707 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3708_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:150px;
  background:inherit;
  background-color:rgba(244, 250, 255, 1);
  border:none;
  border-radius:18px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3708 {
  border-width:0px;
  position:absolute;
  left:24px;
  top:318px;
  width:150px;
  height:150px;
  display:flex;
}
#u3708 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3708_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3709_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  text-align:center;
}
#u3709 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:416px;
  width:96px;
  height:32px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  text-align:center;
}
#u3709 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3709_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3710_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
}
#u3710 {
  border-width:0px;
  position:absolute;
  left:74px;
  top:350px;
  width:50px;
  height:50px;
  display:flex;
}
#u3710 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3710_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3711 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3712_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:150px;
  background:inherit;
  background-color:rgba(244, 250, 255, 1);
  border:none;
  border-radius:18px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3712 {
  border-width:0px;
  position:absolute;
  left:377px;
  top:318px;
  width:150px;
  height:150px;
  display:flex;
}
#u3712 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3712_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3713_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  text-align:center;
}
#u3713 {
  border-width:0px;
  position:absolute;
  left:410px;
  top:416px;
  width:96px;
  height:32px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  text-align:center;
}
#u3713 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3713_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3714_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
}
#u3714 {
  border-width:0px;
  position:absolute;
  left:427px;
  top:350px;
  width:50px;
  height:50px;
  display:flex;
}
#u3714 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3714_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3715_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:82px;
}
#u3715 {
  border-width:0px;
  position:absolute;
  left:607px;
  top:134px;
  width:81px;
  height:82px;
  display:flex;
}
#u3715 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3715_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3716_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:113px;
  height:25px;
}
#u3716 {
  border-width:0px;
  position:absolute;
  left:1118px;
  top:134px;
  width:113px;
  height:25px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
}
#u3716 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3716_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3717 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3718_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u3718 {
  border-width:0px;
  position:absolute;
  left:188px;
  top:67px;
  width:1px;
  height:18px;
  display:flex;
  font-size:16px;
}
#u3718 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3718_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u3719_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u3719 {
  border-width:0px;
  position:absolute;
  left:719px;
  top:172px;
  width:193px;
  height:21px;
  display:flex;
  font-size:18px;
}
#u3719 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3719_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3720_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:24px;
  color:#8400FF;
}
#u3720 {
  border-width:0px;
  position:absolute;
  left:713px;
  top:137px;
  width:168px;
  height:28px;
  display:flex;
  font-size:24px;
  color:#8400FF;
}
#u3720 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3720_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3721 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3722_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  color:#999999;
  text-align:left;
}
#u3722 {
  border-width:0px;
  position:absolute;
  left:933px;
  top:207px;
  width:90px;
  height:26px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  color:#999999;
  text-align:left;
}
#u3722 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3722_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3723_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:109px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  color:#999999;
  text-align:left;
}
#u3723 {
  border-width:0px;
  position:absolute;
  left:715px;
  top:207px;
  width:109px;
  height:26px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  color:#999999;
  text-align:left;
}
#u3723 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3723_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3724_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  color:#000000;
  text-align:left;
}
#u3724 {
  border-width:0px;
  position:absolute;
  left:824px;
  top:201px;
  width:84px;
  height:32px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  color:#000000;
  text-align:left;
}
#u3724 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3724_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3725_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:141px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  color:#000000;
  text-align:left;
}
#u3725 {
  border-width:0px;
  position:absolute;
  left:1020px;
  top:201px;
  width:141px;
  height:32px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  color:#000000;
  text-align:left;
}
#u3725 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3725_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3726 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3727_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:150px;
  background:inherit;
  background-color:rgba(244, 250, 255, 1);
  border:none;
  border-radius:18px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3727 {
  border-width:0px;
  position:absolute;
  left:557px;
  top:318px;
  width:150px;
  height:150px;
  display:flex;
}
#u3727 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3727_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3728_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  text-align:center;
}
#u3728 {
  border-width:0px;
  position:absolute;
  left:591px;
  top:416px;
  width:96px;
  height:32px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  text-align:center;
}
#u3728 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3728_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3729_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
}
#u3729 {
  border-width:0px;
  position:absolute;
  left:607px;
  top:350px;
  width:50px;
  height:50px;
  display:flex;
}
#u3729 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3729_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3730 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3731_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:36px;
  color:#000000;
}
#u3731 {
  border-width:0px;
  position:absolute;
  left:553px;
  top:252px;
  width:49px;
  height:48px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:36px;
  color:#000000;
}
#u3731 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3731_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3732_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:49px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:36px;
  color:#000000;
}
#u3732 {
  border-width:0px;
  position:absolute;
  left:747px;
  top:252px;
  width:49px;
  height:48px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:36px;
  color:#000000;
}
#u3732 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3732_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3733_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:69px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:36px;
  color:#000000;
}
#u3733 {
  border-width:0px;
  position:absolute;
  left:921px;
  top:252px;
  width:69px;
  height:48px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:500;
  font-style:normal;
  font-size:36px;
  color:#000000;
}
#u3733 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3733_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3734_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#999999;
}
#u3734 {
  border-width:0px;
  position:absolute;
  left:614px;
  top:264px;
  width:48px;
  height:32px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#999999;
}
#u3734 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3734_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3735_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#999999;
}
#u3735 {
  border-width:0px;
  position:absolute;
  left:808px;
  top:264px;
  width:48px;
  height:32px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#999999;
}
#u3735 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3735_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3736_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#999999;
}
#u3736 {
  border-width:0px;
  position:absolute;
  left:1005px;
  top:264px;
  width:120px;
  height:32px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#999999;
}
#u3736 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3736_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3737 {
  border-width:0px;
  position:absolute;
  left:5px;
  top:640px;
  width:711px;
  height:547px;
}
#u3737_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:711px;
  height:547px;
  overflow:auto;
  -webkit-overflow-scrolling:touch;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3737_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u3738_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:334px;
  height:247px;
  background:-webkit-linear-gradient(0deg, rgba(2, 119, 198, 1) 0%, rgba(2, 119, 198, 1) 0%, rgba(16, 62, 112, 1) 100%, rgba(16, 62, 112, 1) 100%);
  background:-moz-linear-gradient(90deg, rgba(2, 119, 198, 1) 0%, rgba(2, 119, 198, 1) 0%, rgba(16, 62, 112, 1) 100%, rgba(16, 62, 112, 1) 100%);
  background:linear-gradient(90deg, rgba(2, 119, 198, 1) 0%, rgba(2, 119, 198, 1) 0%, rgba(16, 62, 112, 1) 100%, rgba(16, 62, 112, 1) 100%);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#C280FF;
}
#u3738 {
  border-width:0px;
  position:absolute;
  left:346px;
  top:54px;
  width:334px;
  height:247px;
  display:flex;
  opacity:0.07;
  color:#C280FF;
}
#u3738 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3738_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3739_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:334px;
  height:44px;
  background:-webkit-linear-gradient(0deg, rgba(2, 119, 198, 1) 0%, rgba(2, 119, 198, 1) 0%, rgba(4, 35, 73, 1) 100%, rgba(4, 35, 73, 1) 100%);
  background:-moz-linear-gradient(90deg, rgba(2, 119, 198, 1) 0%, rgba(2, 119, 198, 1) 0%, rgba(4, 35, 73, 1) 100%, rgba(4, 35, 73, 1) 100%);
  background:linear-gradient(90deg, rgba(2, 119, 198, 1) 0%, rgba(2, 119, 198, 1) 0%, rgba(4, 35, 73, 1) 100%, rgba(4, 35, 73, 1) 100%);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3739 {
  border-width:0px;
  position:absolute;
  left:346px;
  top:54px;
  width:334px;
  height:44px;
  display:flex;
  opacity:0.8;
}
#u3739 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3739_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3740_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:148px;
  height:23px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  color:#66FFFF;
}
#u3740 {
  border-width:0px;
  position:absolute;
  left:371px;
  top:64px;
  width:148px;
  height:23px;
  display:flex;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  color:#66FFFF;
}
#u3740 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3740_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3741_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:680px;
  height:44px;
  background:-webkit-linear-gradient(0deg, rgba(2, 119, 198, 1) 0%, rgba(2, 119, 198, 1) 0%, rgba(16, 62, 112, 1) 100%, rgba(16, 62, 112, 1) 100%);
  background:-moz-linear-gradient(90deg, rgba(2, 119, 198, 1) 0%, rgba(2, 119, 198, 1) 0%, rgba(16, 62, 112, 1) 100%, rgba(16, 62, 112, 1) 100%);
  background:linear-gradient(90deg, rgba(2, 119, 198, 1) 0%, rgba(2, 119, 198, 1) 0%, rgba(16, 62, 112, 1) 100%, rgba(16, 62, 112, 1) 100%);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#555555;
}
#u3741 {
  border-width:0px;
  position:absolute;
  left:3px;
  top:307px;
  width:680px;
  height:44px;
  display:flex;
  opacity:0.8;
  color:#555555;
}
#u3741 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3741_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3742_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:680px;
  height:227px;
  background:-webkit-linear-gradient(0deg, rgba(2, 119, 198, 1) 0%, rgba(2, 119, 198, 1) 0%, rgba(2, 119, 198, 1) 100%, rgba(2, 119, 198, 1) 100%);
  background:-moz-linear-gradient(90deg, rgba(2, 119, 198, 1) 0%, rgba(2, 119, 198, 1) 0%, rgba(2, 119, 198, 1) 100%, rgba(2, 119, 198, 1) 100%);
  background:linear-gradient(90deg, rgba(2, 119, 198, 1) 0%, rgba(2, 119, 198, 1) 0%, rgba(2, 119, 198, 1) 100%, rgba(2, 119, 198, 1) 100%);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
  color:#555555;
}
#u3742 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:347px;
  width:680px;
  height:227px;
  display:flex;
  opacity:0.07;
  font-size:11px;
  color:#555555;
}
#u3742 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3742_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3743_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:23px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  color:#555555;
}
#u3743 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:317px;
  width:201px;
  height:23px;
  display:flex;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  color:#555555;
}
#u3743 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3743_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3744_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:609px;
  height:2px;
}
#u3744 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:550px;
  width:608px;
  height:1px;
  display:flex;
  opacity:0.6;
  font-size:12px;
  color:#555555;
}
#u3744 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3744_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3745_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:601px;
  height:2px;
}
#u3745 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:510px;
  width:600px;
  height:1px;
  display:flex;
  opacity:0.2;
  font-size:12px;
  color:#555555;
}
#u3745 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3745_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3746_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:601px;
  height:2px;
}
#u3746 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:471px;
  width:600px;
  height:1px;
  display:flex;
  opacity:0.2;
  font-size:12px;
  color:#555555;
}
#u3746 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3746_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3747_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:601px;
  height:2px;
}
#u3747 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:431px;
  width:600px;
  height:1px;
  display:flex;
  opacity:0.2;
  font-size:12px;
  color:#555555;
}
#u3747 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3747_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3748_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
  color:#555555;
}
#u3748 {
  border-width:0px;
  position:absolute;
  left:127px;
  top:566px;
  width:30px;
  height:12px;
  display:flex;
  font-size:11px;
  color:#555555;
}
#u3748 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3748_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3749_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
  color:#555555;
}
#u3749 {
  border-width:0px;
  position:absolute;
  left:191px;
  top:566px;
  width:30px;
  height:12px;
  display:flex;
  font-size:11px;
  color:#555555;
}
#u3749 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3749_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3750_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
  color:#555555;
}
#u3750 {
  border-width:0px;
  position:absolute;
  left:253px;
  top:566px;
  width:30px;
  height:12px;
  display:flex;
  font-size:11px;
  color:#555555;
}
#u3750 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3750_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3751_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
  color:#555555;
}
#u3751 {
  border-width:0px;
  position:absolute;
  left:317px;
  top:566px;
  width:30px;
  height:12px;
  display:flex;
  font-size:11px;
  color:#555555;
}
#u3751 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3751_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3752_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
  color:#555555;
}
#u3752 {
  border-width:0px;
  position:absolute;
  left:380px;
  top:566px;
  width:30px;
  height:12px;
  display:flex;
  font-size:11px;
  color:#555555;
}
#u3752 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3752_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3753_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
  color:#555555;
}
#u3753 {
  border-width:0px;
  position:absolute;
  left:444px;
  top:566px;
  width:30px;
  height:12px;
  display:flex;
  font-size:11px;
  color:#555555;
}
#u3753 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3753_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3754_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
  color:#555555;
}
#u3754 {
  border-width:0px;
  position:absolute;
  left:571px;
  top:566px;
  width:30px;
  height:12px;
  display:flex;
  font-size:11px;
  color:#555555;
}
#u3754 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3754_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3755_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
  color:#555555;
}
#u3755 {
  border-width:0px;
  position:absolute;
  left:506px;
  top:566px;
  width:30px;
  height:12px;
  display:flex;
  font-size:11px;
  color:#555555;
}
#u3755 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3755_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3756_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:12px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
  color:#555555;
}
#u3756 {
  border-width:0px;
  position:absolute;
  left:633px;
  top:566px;
  width:30px;
  height:12px;
  display:flex;
  font-size:11px;
  color:#555555;
}
#u3756 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3756_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3757 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3758_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:583px;
  height:118px;
}
#u3758 {
  border-width:0px;
  position:absolute;
  left:64px;
  top:432px;
  width:583px;
  height:118px;
  display:flex;
  opacity:0.7;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:12px;
  color:#555555;
}
#u3758 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3758_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3759_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:588px;
  height:70px;
}
#u3759 {
  border-width:0px;
  position:absolute;
  left:64px;
  top:432px;
  width:584px;
  height:66px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:12px;
  color:#555555;
}
#u3759 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3759_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3760_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:334px;
  height:247px;
  background:-webkit-linear-gradient(0deg, rgba(2, 119, 198, 1) 0%, rgba(2, 119, 198, 1) 0%, rgba(16, 62, 112, 1) 100%, rgba(16, 62, 112, 1) 100%);
  background:-moz-linear-gradient(90deg, rgba(2, 119, 198, 1) 0%, rgba(2, 119, 198, 1) 0%, rgba(16, 62, 112, 1) 100%, rgba(16, 62, 112, 1) 100%);
  background:linear-gradient(90deg, rgba(2, 119, 198, 1) 0%, rgba(2, 119, 198, 1) 0%, rgba(16, 62, 112, 1) 100%, rgba(16, 62, 112, 1) 100%);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#C280FF;
}
#u3760 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:54px;
  width:334px;
  height:247px;
  display:flex;
  opacity:0.07;
  color:#C280FF;
}
#u3760 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3760_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3761_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:334px;
  height:44px;
  background:-webkit-linear-gradient(0deg, rgba(2, 119, 198, 1) 0%, rgba(2, 119, 198, 1) 0%, rgba(4, 35, 73, 1) 100%, rgba(4, 35, 73, 1) 100%);
  background:-moz-linear-gradient(90deg, rgba(2, 119, 198, 1) 0%, rgba(2, 119, 198, 1) 0%, rgba(4, 35, 73, 1) 100%, rgba(4, 35, 73, 1) 100%);
  background:linear-gradient(90deg, rgba(2, 119, 198, 1) 0%, rgba(2, 119, 198, 1) 0%, rgba(4, 35, 73, 1) 100%, rgba(4, 35, 73, 1) 100%);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3761 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:54px;
  width:334px;
  height:44px;
  display:flex;
  opacity:0.8;
}
#u3761 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3761_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3762 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3763 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3764_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:147px;
  height:76px;
  background:inherit;
  background-color:rgba(13, 39, 66, 0.6);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u3764 {
  border-width:0px;
  position:absolute;
  left:10px;
  top:105px;
  width:147px;
  height:76px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u3764 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 60px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3764_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3765_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:12px;
}
#u3765 {
  border-width:0px;
  position:absolute;
  left:142px;
  top:106px;
  width:14px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u3765 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3765_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3766_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#00FFFF;
}
#u3766 {
  border-width:0px;
  position:absolute;
  left:24px;
  top:113px;
  width:87px;
  height:16px;
  display:flex;
  color:#00FFFF;
}
#u3766 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3766_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3767_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:31px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:30px;
  color:#FE7007;
}
#u3767 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:143px;
  width:70px;
  height:31px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:30px;
  color:#FE7007;
}
#u3767 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3767_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3768 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3769 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3770_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:147px;
  height:76px;
  background:inherit;
  background-color:rgba(13, 39, 66, 0.6);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u3770 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:105px;
  width:147px;
  height:76px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u3770 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 60px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3770_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3771_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:12px;
}
#u3771 {
  border-width:0px;
  position:absolute;
  left:308px;
  top:106px;
  width:14px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u3771 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3771_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3772_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#00FFFF;
}
#u3772 {
  border-width:0px;
  position:absolute;
  left:192px;
  top:113px;
  width:90px;
  height:17px;
  display:flex;
  color:#00FFFF;
}
#u3772 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3772_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3773_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:30px;
  color:#FE7007;
}
#u3773 {
  border-width:0px;
  position:absolute;
  left:185px;
  top:143px;
  width:74px;
  height:35px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:30px;
  color:#FE7007;
}
#u3773 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3773_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3774_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:34px;
}
#u3774 {
  border-width:0px;
  position:absolute;
  left:113px;
  top:127px;
  width:30px;
  height:34px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  color:#FFFFFF;
}
#u3774 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3774_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3775 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3776_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:13px;
}
#u3776 {
  border-width:0px;
  position:absolute;
  left:298px;
  top:136px;
  width:12px;
  height:13px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  color:#FFFFFF;
}
#u3776 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3776_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3777_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:14px;
}
#u3777 {
  border-width:0px;
  position:absolute;
  left:287px;
  top:136px;
  width:21px;
  height:14px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  color:#FFFFFF;
}
#u3777 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3777_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3778_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:28px;
}
#u3778 {
  border-width:0px;
  position:absolute;
  left:285px;
  top:132px;
  width:28px;
  height:28px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  color:#FFFFFF;
}
#u3778 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3778_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3779 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3780 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3781_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:147px;
  height:76px;
  background:inherit;
  background-color:rgba(13, 39, 66, 0.6);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u3781 {
  border-width:0px;
  position:absolute;
  left:10px;
  top:199px;
  width:147px;
  height:76px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u3781 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 60px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3781_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3782_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:12px;
}
#u3782 {
  border-width:0px;
  position:absolute;
  left:142px;
  top:200px;
  width:14px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u3782 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3782_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3783_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#00FFFF;
}
#u3783 {
  border-width:0px;
  position:absolute;
  left:24px;
  top:207px;
  width:87px;
  height:16px;
  display:flex;
  color:#00FFFF;
}
#u3783 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3783_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3784_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:31px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:30px;
  color:#FE7007;
}
#u3784 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:237px;
  width:70px;
  height:31px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:30px;
  color:#FE7007;
}
#u3784 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3784_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3785 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3786 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3787_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:147px;
  height:76px;
  background:inherit;
  background-color:rgba(13, 39, 66, 0.6);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u3787 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:199px;
  width:147px;
  height:76px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u3787 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 60px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3787_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3788_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:12px;
}
#u3788 {
  border-width:0px;
  position:absolute;
  left:308px;
  top:200px;
  width:14px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u3788 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3788_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3789_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#00FFFF;
}
#u3789 {
  border-width:0px;
  position:absolute;
  left:192px;
  top:207px;
  width:90px;
  height:17px;
  display:flex;
  color:#00FFFF;
}
#u3789 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3789_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3790_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:30px;
  color:#FE7007;
}
#u3790 {
  border-width:0px;
  position:absolute;
  left:185px;
  top:237px;
  width:74px;
  height:35px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:30px;
  color:#FE7007;
}
#u3790 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3790_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3791_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:34px;
}
#u3791 {
  border-width:0px;
  position:absolute;
  left:113px;
  top:221px;
  width:30px;
  height:34px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  color:#FFFFFF;
}
#u3791 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3791_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3792 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3793_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:13px;
}
#u3793 {
  border-width:0px;
  position:absolute;
  left:298px;
  top:230px;
  width:12px;
  height:13px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  color:#FFFFFF;
}
#u3793 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3793_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3794_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:14px;
}
#u3794 {
  border-width:0px;
  position:absolute;
  left:287px;
  top:230px;
  width:21px;
  height:14px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  color:#FFFFFF;
}
#u3794 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3794_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3795_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:28px;
}
#u3795 {
  border-width:0px;
  position:absolute;
  left:285px;
  top:226px;
  width:28px;
  height:28px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  color:#FFFFFF;
}
#u3795 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3795_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3796 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3797_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:160px;
  background:inherit;
  background-color:rgba(29, 27, 38, 1);
  border:none;
  border-radius:20px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3797 {
  border-width:0px;
  position:absolute;
  left:351px;
  top:111px;
  width:160px;
  height:160px;
  display:flex;
}
#u3797 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3797_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3798_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:128px;
  height:112px;
}
#u3798 {
  border-width:0px;
  position:absolute;
  left:362px;
  top:125px;
  width:128px;
  height:112px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  color:#C280FF;
}
#u3798 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3798_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3799_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#C280FF;
}
#u3799 {
  border-width:0px;
  position:absolute;
  left:395px;
  top:206px;
  width:56px;
  height:16px;
  display:flex;
  color:#C280FF;
}
#u3799 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3799_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3800_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#C280FF;
}
#u3800 {
  border-width:0px;
  position:absolute;
  left:463px;
  top:175px;
  width:14px;
  height:16px;
  display:flex;
  color:#C280FF;
}
#u3800 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3800_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3801 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3802_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:38px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'04b_21 ', '04b_21';
  font-weight:400;
  font-style:normal;
  font-size:28px;
  color:#00FFFF;
  text-align:right;
}
#u3802 {
  border-width:0px;
  position:absolute;
  left:378px;
  top:158px;
  width:71px;
  height:38px;
  display:flex;
  font-family:'04b_21 ', '04b_21';
  font-weight:400;
  font-style:normal;
  font-size:28px;
  color:#00FFFF;
  text-align:right;
}
#u3802 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3802_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3803 {
  border-width:0px;
  position:absolute;
  left:401px;
  top:198px;
  width:19px;
  height:24px;
}
#u3803_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:24px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3803_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u3803_state1 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:24px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u3803_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u3804_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  color:#00CCFF;
  text-align:right;
}
#u3804 {
  border-width:0px;
  position:absolute;
  left:399px;
  top:137px;
  width:50px;
  height:21px;
  display:flex;
  font-size:18px;
  color:#00CCFF;
  text-align:right;
}
#u3804 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3804_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3805_input {
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3805_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3805_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3805 {
  border-width:0px;
  position:absolute;
  left:430px;
  top:197px;
  width:19px;
  height:25px;
  display:flex;
}
#u3805 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3805_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3805.disabled {
}
#u3806_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(252, 206, 35, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:14px;
  color:#C280FF;
}
#u3806 {
  border-width:0px;
  position:absolute;
  left:391px;
  top:237px;
  width:75px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:14px;
  color:#C280FF;
}
#u3806 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3806_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3807 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3808_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:160px;
  background:inherit;
  background-color:rgba(29, 27, 38, 1);
  border:none;
  border-radius:20px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3808 {
  border-width:0px;
  position:absolute;
  left:514px;
  top:111px;
  width:160px;
  height:160px;
  display:flex;
}
#u3808 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3808_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3809_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:128px;
  height:112px;
}
#u3809 {
  border-width:0px;
  position:absolute;
  left:530px;
  top:128px;
  width:128px;
  height:112px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  color:#C280FF;
}
#u3809 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3809_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3810_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:75px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(252, 206, 35, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:14px;
  color:#C280FF;
}
#u3810 {
  border-width:0px;
  position:absolute;
  left:557px;
  top:237px;
  width:75px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:14px;
  color:#C280FF;
}
#u3810 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3810_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3811_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:76px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:25px;
  color:#C280FF;
  text-align:right;
  line-height:28px;
}
#u3811 {
  border-width:0px;
  position:absolute;
  left:545px;
  top:176px;
  width:76px;
  height:25px;
  display:flex;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:25px;
  color:#C280FF;
  text-align:right;
  line-height:28px;
}
#u3811 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3811_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3812_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#C280FF;
}
#u3812 {
  border-width:0px;
  position:absolute;
  left:564px;
  top:207px;
  width:56px;
  height:16px;
  display:flex;
  color:#C280FF;
}
#u3812 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3812_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3813_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#C280FF;
}
#u3813 {
  border-width:0px;
  position:absolute;
  left:625px;
  top:182px;
  width:14px;
  height:16px;
  display:flex;
  color:#C280FF;
}
#u3813 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3813_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3815 {
  border-width:0px;
  position:absolute;
  left:388px;
  top:16px;
  width:20px;
  height:16px;
}
#u3815_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:16px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3815_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u3815_state1 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:16px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u3815_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u3816_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:23px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
  color:#555555;
}
#u3816 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:14px;
  width:60px;
  height:23px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
  color:#555555;
}
#u3816 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3816_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3817_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
  color:#555555;
}
#u3817 {
  border-width:0px;
  position:absolute;
  left:238px;
  top:20px;
  width:65px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
  color:#555555;
}
#u3817 .text {
  position:absolute;
  align-self:flex-end;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3817_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3818_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
  color:#555555;
}
#u3818 {
  border-width:0px;
  position:absolute;
  left:192px;
  top:14px;
  width:40px;
  height:18px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
  color:#555555;
}
#u3818 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3818_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3819_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
  color:#555555;
}
#u3819 {
  border-width:0px;
  position:absolute;
  left:303px;
  top:14px;
  width:40px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
  color:#555555;
}
#u3819 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3819_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3820_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
  color:#555555;
}
#u3820 {
  border-width:0px;
  position:absolute;
  left:345px;
  top:14px;
  width:40px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
  color:#555555;
}
#u3820 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3820_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3821_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:507px;
  height:5px;
}
#u3821 {
  border-width:0px;
  position:absolute;
  left:7px;
  top:42px;
  width:504px;
  height:2px;
  display:flex;
  color:#555555;
}
#u3821 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3821_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3822_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:47px;
}
#u3822 {
  border-width:0px;
  position:absolute;
  left:877px;
  top:-70px;
  width:45px;
  height:47px;
  display:flex;
}
#u3822 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3822_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3823 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3824_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:150px;
  background:inherit;
  background-color:rgba(244, 250, 255, 1);
  border:none;
  border-radius:18px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3824 {
  border-width:0px;
  position:absolute;
  left:24px;
  top:473px;
  width:150px;
  height:150px;
  display:flex;
}
#u3824 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3824_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3825_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  text-align:center;
}
#u3825 {
  border-width:0px;
  position:absolute;
  left:56px;
  top:566px;
  width:96px;
  height:32px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  text-align:center;
}
#u3825 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3825_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3826_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:43px;
}
#u3826 {
  border-width:0px;
  position:absolute;
  left:74px;
  top:505px;
  width:50px;
  height:43px;
  display:flex;
}
#u3826 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3826_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3827 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3828_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:150px;
  background:inherit;
  background-color:rgba(244, 250, 255, 1);
  border:none;
  border-radius:18px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3828 {
  border-width:0px;
  position:absolute;
  left:377px;
  top:473px;
  width:150px;
  height:150px;
  display:flex;
}
#u3828 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3828_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3829_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  text-align:center;
}
#u3829 {
  border-width:0px;
  position:absolute;
  left:404px;
  top:572px;
  width:96px;
  height:32px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  text-align:center;
}
#u3829 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3829_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3830_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
}
#u3830 {
  border-width:0px;
  position:absolute;
  left:427px;
  top:505px;
  width:50px;
  height:50px;
  display:flex;
}
#u3830 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3830_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3831 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3832_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u3832 {
  border-width:0px;
  position:absolute;
  left:1075px;
  top:-70px;
  width:100px;
  height:30px;
  display:flex;
  font-size:14px;
  color:#FFFFFF;
  text-align:right;
}
#u3832 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3832_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3833 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3834 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3835_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u3835 {
  border-width:0px;
  position:absolute;
  left:1079px;
  top:-70px;
  width:30px;
  height:30px;
  display:flex;
}
#u3835 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3835_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3836_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:23px;
}
#u3836 {
  border-width:0px;
  position:absolute;
  left:1084px;
  top:-66px;
  width:23px;
  height:23px;
  display:flex;
}
#u3836 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3836_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3837_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#FFFFFF;
  text-align:right;
}
#u3837 {
  border-width:0px;
  position:absolute;
  left:1113px;
  top:-63px;
  width:56px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#FFFFFF;
  text-align:right;
}
#u3837 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3837_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3838_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:52px;
  height:57px;
}
#u3838 {
  border-width:0px;
  position:absolute;
  left:748px;
  top:-75px;
  width:52px;
  height:57px;
  display:flex;
}
#u3838 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3838_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3839_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:46px;
  height:47px;
}
#u3839 {
  border-width:0px;
  position:absolute;
  left:821px;
  top:-70px;
  width:46px;
  height:47px;
  display:flex;
}
#u3839 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3839_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3840 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3841_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:150px;
  background:inherit;
  background-color:rgba(244, 250, 255, 1);
  border:none;
  border-radius:18px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3841 {
  border-width:0px;
  position:absolute;
  left:201px;
  top:473px;
  width:150px;
  height:150px;
  display:flex;
}
#u3841 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3841_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3842_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  text-align:center;
}
#u3842 {
  border-width:0px;
  position:absolute;
  left:231px;
  top:571px;
  width:96px;
  height:32px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  text-align:center;
}
#u3842 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3842_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3843_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
}
#u3843 {
  border-width:0px;
  position:absolute;
  left:252px;
  top:493px;
  width:50px;
  height:50px;
  display:flex;
}
#u3843 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3843_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3844_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:22px;
  background:inherit;
  background-color:rgba(204, 204, 204, 1);
  border:none;
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:center;
}
#u3844 {
  border-width:0px;
  position:absolute;
  left:607px;
  top:216px;
  width:80px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:center;
}
#u3844 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3844_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3846_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:60px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:1px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3846 {
  border-width:0px;
  position:absolute;
  left:1146px;
  top:25px;
  width:120px;
  height:60px;
  display:flex;
  opacity:0.8;
}
#u3846 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3846_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3847_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:17px;
  height:15px;
}
#u3847 {
  border-width:0px;
  position:absolute;
  left:1146px;
  top:25px;
  width:13px;
  height:11px;
  display:flex;
}
#u3847 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3847_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3848_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:17px;
  height:15px;
}
#u3848 {
  border-width:0px;
  position:absolute;
  left:1253px;
  top:25px;
  width:13px;
  height:11px;
  display:flex;
}
#u3848 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3848_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3849_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:17px;
  height:15px;
}
#u3849 {
  border-width:0px;
  position:absolute;
  left:1146px;
  top:74px;
  width:13px;
  height:11px;
  display:flex;
}
#u3849 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3849_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3850_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:17px;
  height:15px;
}
#u3850 {
  border-width:0px;
  position:absolute;
  left:1253px;
  top:74px;
  width:13px;
  height:11px;
  display:flex;
}
#u3850 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3850_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3851_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
  text-align:center;
}
#u3851 {
  border-width:0px;
  position:absolute;
  left:1159px;
  top:31px;
  width:94px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
  text-align:center;
}
#u3851 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3851_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3852_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:88px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:28px;
  color:#D9001B;
  text-align:right;
}
#u3852 {
  border-width:0px;
  position:absolute;
  left:1146px;
  top:48px;
  width:88px;
  height:32px;
  display:flex;
  font-size:28px;
  color:#D9001B;
  text-align:right;
}
#u3852 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3852_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3853_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u3853 {
  border-width:0px;
  position:absolute;
  left:1235px;
  top:58px;
  width:29px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u3853 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3853_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3855_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:60px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:1px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3855 {
  border-width:0px;
  position:absolute;
  left:980px;
  top:23px;
  width:120px;
  height:60px;
  display:flex;
  opacity:0.8;
}
#u3855 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3855_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3856_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:17px;
  height:15px;
}
#u3856 {
  border-width:0px;
  position:absolute;
  left:980px;
  top:23px;
  width:13px;
  height:11px;
  display:flex;
}
#u3856 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3856_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3857_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:17px;
  height:15px;
}
#u3857 {
  border-width:0px;
  position:absolute;
  left:1087px;
  top:23px;
  width:13px;
  height:11px;
  display:flex;
}
#u3857 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3857_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3858_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:17px;
  height:15px;
}
#u3858 {
  border-width:0px;
  position:absolute;
  left:980px;
  top:72px;
  width:13px;
  height:11px;
  display:flex;
}
#u3858 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3858_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3859_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:17px;
  height:15px;
}
#u3859 {
  border-width:0px;
  position:absolute;
  left:1087px;
  top:72px;
  width:13px;
  height:11px;
  display:flex;
}
#u3859 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3859_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3860_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
  text-align:center;
}
#u3860 {
  border-width:0px;
  position:absolute;
  left:993px;
  top:29px;
  width:94px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
  text-align:center;
}
#u3860 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3860_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3861_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:88px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:28px;
  color:#D9001B;
  text-align:right;
}
#u3861 {
  border-width:0px;
  position:absolute;
  left:980px;
  top:46px;
  width:88px;
  height:32px;
  display:flex;
  font-size:28px;
  color:#D9001B;
  text-align:right;
}
#u3861 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3861_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3862_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u3862 {
  border-width:0px;
  position:absolute;
  left:1069px;
  top:56px;
  width:29px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u3862 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3862_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3864_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:60px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:1px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3864 {
  border-width:0px;
  position:absolute;
  left:822px;
  top:25px;
  width:120px;
  height:60px;
  display:flex;
  opacity:0.8;
}
#u3864 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3864_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3865_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:17px;
  height:15px;
}
#u3865 {
  border-width:0px;
  position:absolute;
  left:822px;
  top:25px;
  width:13px;
  height:11px;
  display:flex;
}
#u3865 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3865_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3866_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:17px;
  height:15px;
}
#u3866 {
  border-width:0px;
  position:absolute;
  left:929px;
  top:25px;
  width:13px;
  height:11px;
  display:flex;
}
#u3866 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3866_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3867_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:17px;
  height:15px;
}
#u3867 {
  border-width:0px;
  position:absolute;
  left:822px;
  top:74px;
  width:13px;
  height:11px;
  display:flex;
}
#u3867 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3867_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3868_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:17px;
  height:15px;
}
#u3868 {
  border-width:0px;
  position:absolute;
  left:929px;
  top:74px;
  width:13px;
  height:11px;
  display:flex;
}
#u3868 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3868_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3869_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
  text-align:center;
}
#u3869 {
  border-width:0px;
  position:absolute;
  left:835px;
  top:31px;
  width:94px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
  text-align:center;
}
#u3869 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3869_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3870_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:88px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:28px;
  color:#D9001B;
  text-align:right;
}
#u3870 {
  border-width:0px;
  position:absolute;
  left:822px;
  top:48px;
  width:88px;
  height:32px;
  display:flex;
  font-size:28px;
  color:#D9001B;
  text-align:right;
}
#u3870 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3870_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3871_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u3871 {
  border-width:0px;
  position:absolute;
  left:911px;
  top:58px;
  width:29px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u3871 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3871_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3873_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:60px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:1px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3873 {
  border-width:0px;
  position:absolute;
  left:1309px;
  top:25px;
  width:120px;
  height:60px;
  display:flex;
  opacity:0.8;
}
#u3873 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3873_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3874_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:17px;
  height:15px;
}
#u3874 {
  border-width:0px;
  position:absolute;
  left:1309px;
  top:25px;
  width:13px;
  height:11px;
  display:flex;
}
#u3874 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3874_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3875_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:17px;
  height:15px;
}
#u3875 {
  border-width:0px;
  position:absolute;
  left:1416px;
  top:25px;
  width:13px;
  height:11px;
  display:flex;
}
#u3875 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3875_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3876_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:17px;
  height:15px;
}
#u3876 {
  border-width:0px;
  position:absolute;
  left:1309px;
  top:74px;
  width:13px;
  height:11px;
  display:flex;
}
#u3876 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3876_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3877_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:17px;
  height:15px;
}
#u3877 {
  border-width:0px;
  position:absolute;
  left:1416px;
  top:74px;
  width:13px;
  height:11px;
  display:flex;
}
#u3877 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3877_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3878_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
  text-align:center;
}
#u3878 {
  border-width:0px;
  position:absolute;
  left:1322px;
  top:31px;
  width:94px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
  text-align:center;
}
#u3878 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3878_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3879_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:88px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:28px;
  color:#D9001B;
  text-align:right;
}
#u3879 {
  border-width:0px;
  position:absolute;
  left:1309px;
  top:48px;
  width:88px;
  height:32px;
  display:flex;
  font-size:28px;
  color:#D9001B;
  text-align:right;
}
#u3879 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3879_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3880_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u3880 {
  border-width:0px;
  position:absolute;
  left:1398px;
  top:58px;
  width:29px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u3880 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3880_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3881_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:80px;
}
#u3881 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:93px;
  width:80px;
  height:80px;
  display:flex;
}
#u3881 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3881_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3882 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3883 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3884_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:152px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3884 {
  border-width:0px;
  position:absolute;
  left:109px;
  top:135px;
  width:152px;
  height:40px;
  display:flex;
}
#u3884 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3884_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3885_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u3885 {
  border-width:0px;
  position:absolute;
  left:116px;
  top:142px;
  width:139px;
  height:26px;
  display:flex;
  font-size:18px;
}
#u3885 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3885_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3886 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3887_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:179px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(235, 223, 245, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u3887 {
  border-width:0px;
  position:absolute;
  left:595px;
  top:53px;
  width:179px;
  height:40px;
  display:flex;
  font-size:18px;
}
#u3887 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3887_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3888_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:22px;
  background:inherit;
  background-color:rgba(204, 204, 204, 1);
  border:none;
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:center;
}
#u3888 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:151px;
  width:60px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:200;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:center;
}
#u3888 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3888_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3889 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3890 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3891_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:367px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3891 {
  border-width:0px;
  position:absolute;
  left:110px;
  top:90px;
  width:367px;
  height:40px;
  display:flex;
}
#u3891 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3891_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3892_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:349px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u3892 {
  border-width:0px;
  position:absolute;
  left:119px;
  top:97px;
  width:349px;
  height:26px;
  display:flex;
  font-size:18px;
}
#u3892 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3892_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3893_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:170px;
  height:23px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#8400FF;
  text-align:right;
}
#u3893 {
  border-width:0px;
  position:absolute;
  left:307px;
  top:175px;
  width:170px;
  height:23px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#8400FF;
  text-align:right;
}
#u3893 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3893_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
