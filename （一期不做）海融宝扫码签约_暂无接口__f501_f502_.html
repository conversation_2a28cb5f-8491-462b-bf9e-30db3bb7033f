﻿<!DOCTYPE html>
<html>
  <head>
    <title>（一期不做）海融宝扫码签约(暂无接口)(F501\F502)</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/（一期不做）海融宝扫码签约_暂无接口__f501_f502_/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/（一期不做）海融宝扫码签约_暂无接口__f501_f502_/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u84" class="ax_default _形状">
        <div id="u84_div" class=""></div>
        <div id="u84_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (基础app框架(H5)) -->

      <!-- Unnamed (矩形) -->
      <div id="u86" class="ax_default box_1">
        <div id="u86_div" class=""></div>
        <div id="u86_text" class="text ">
          <p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u87" class="ax_default _二级标题">
        <div id="u87_div" class=""></div>
        <div id="u87_text" class="text ">
          <p><span>16:18</span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u88" class="ax_default icon">
        <img id="u88_img" class="img " src="images/海融宝签约_个人__f501_f502_/u3.svg"/>
        <div id="u88_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u89" class="ax_default icon">
        <img id="u89_img" class="img " src="images/海融宝签约_个人__f501_f502_/u4.svg"/>
        <div id="u89_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u90" class="ax_default _文本段落">
        <div id="u90_div" class=""></div>
        <div id="u90_text" class="text ">
          <p><span>海融宝签约(邮储数字人民币)</span></p>
        </div>
      </div>

      <!-- 操作状态 (动态面板) -->
      <div id="u91" class="ax_default ax_default_hidden" data-label="操作状态" style="display:none; visibility: hidden">
        <div id="u91_state0" class="panel_state" data-label="操作成功" style="">
          <div id="u91_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u92" class="ax_default box_3">
              <div id="u92_div" class=""></div>
              <div id="u92_text" class="text ">
                <p><span>操作成功！</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u91_state1" class="panel_state" data-label="操作失败" style="visibility: hidden;">
          <div id="u91_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u93" class="ax_default box_3">
              <div id="u93_div" class=""></div>
              <div id="u93_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u94" class="ax_default _文本段落">
              <div id="u94_div" class=""></div>
              <div id="u94_text" class="text ">
                <p><span>操作失败！</span></p>
              </div>
            </div>

            <!-- Unnamed (图片 ) -->
            <div id="u95" class="ax_default _图片_">
              <img id="u95_img" class="img " src="images/海融宝签约_个人__f501_f502_/u10.png"/>
              <div id="u95_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u96" class="ax_default _图片_">
        <img id="u96_img" class="img " src="images/海融宝签约_个人__f501_f502_/u11.png"/>
        <div id="u96_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u97" class="ax_default icon">
        <img id="u97_img" class="img " src="images/海融宝签约_个人__f501_f502_/u12.svg"/>
        <div id="u97_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u98" class="ax_default _文本段落">
        <div id="u98_div" class=""></div>
        <div id="u98_text" class="text ">
          <p><span>hrypt.com</span></p>
        </div>
      </div>
      <div id="u85" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (矩形) -->
      <div id="u99" class="ax_default primary_button">
        <div id="u99_div" class=""></div>
        <div id="u99_text" class="text ">
          <p><span>提交签约</span></p>
        </div>
      </div>

      <!-- 扫描结果 (动态面板) -->
      <div id="u100" class="ax_default ax_default_hidden" data-label="扫描结果" style="display:none; visibility: hidden">
        <div id="u100_state0" class="panel_state" data-label="扫码识别失败" style="">
          <div id="u100_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u101" class="ax_default box_1">
              <div id="u101_div" class=""></div>
              <div id="u101_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u102" class="ax_default _二级标题">
              <div id="u102_div" class=""></div>
              <div id="u102_text" class="text ">
                <p><span>×</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u103" class="ax_default _文本段落">
              <div id="u103_div" class=""></div>
              <div id="u103_text" class="text ">
                <p><span>扫码识别失败</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u104" class="ax_default _文本段落">
              <div id="u104_div" class=""></div>
              <div id="u104_text" class="text ">
                <p><span>&nbsp;&nbsp; &nbsp; &nbsp; 您扫描的二维码可能不正常，确实是邮储银行数字人民币智能合约子钱包吗？请您检查核实！</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u105" class="ax_default primary_button">
              <div id="u105_div" class=""></div>
              <div id="u105_text" class="text ">
                <p><span>确定</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u100_state1" class="panel_state" data-label="开通失败" style="visibility: hidden;">
          <div id="u100_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u106" class="ax_default box_1">
              <div id="u106_div" class=""></div>
              <div id="u106_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u107" class="ax_default _二级标题">
              <div id="u107_div" class=""></div>
              <div id="u107_text" class="text ">
                <p><span>×</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u108" class="ax_default _文本段落">
              <div id="u108_div" class=""></div>
              <div id="u108_text" class="text ">
                <p><span>&nbsp;&nbsp; &nbsp; &nbsp; 1、请您确认你是否开通邮储银行数字人民币钱包，未开通请去开通！</span></p><p><span>&nbsp;&nbsp; &nbsp; &nbsp; 2、您需要在海融宝里完善基本资料，请补充完善后再来签约！</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u109" class="ax_default primary_button">
              <div id="u109_div" class=""></div>
              <div id="u109_text" class="text ">
                <p><span>确定</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u110" class="ax_default _文本段落">
              <div id="u110_div" class=""></div>
              <div id="u110_text" class="text ">
                <p><span>开通失败</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u111" class="ax_default" data-left="118" data-top="287" data-width="224" data-height="211">

        <!-- Unnamed (组合) -->
        <div id="u112" class="ax_default" data-left="144.949342603119" data-top="324" data-width="164.101314793762" data-height="154">

          <!-- Unnamed (矩形) -->
          <div id="u113" class="ax_default _形状">
            <div id="u113_div" class=""></div>
            <div id="u113_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (线段) -->
          <div id="u114" class="ax_default _线段">
            <img id="u114_img" class="img " src="images/（一期不做）海融宝扫码签约_暂无接口__f501_f502_/u114.svg"/>
            <div id="u114_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (线段) -->
          <div id="u115" class="ax_default _线段">
            <img id="u115_img" class="img " src="images/（一期不做）海融宝扫码签约_暂无接口__f501_f502_/u114.svg"/>
            <div id="u115_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (线段) -->
          <div id="u116" class="ax_default _线段">
            <img id="u116_img" class="img " src="images/（一期不做）海融宝扫码签约_暂无接口__f501_f502_/u114.svg"/>
            <div id="u116_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (线段) -->
          <div id="u117" class="ax_default _线段">
            <img id="u117_img" class="img " src="images/（一期不做）海融宝扫码签约_暂无接口__f501_f502_/u114.svg"/>
            <div id="u117_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (线段) -->
          <div id="u118" class="ax_default _线段">
            <img id="u118_img" class="img " src="images/（一期不做）海融宝扫码签约_暂无接口__f501_f502_/u118.svg"/>
            <div id="u118_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (线段) -->
          <div id="u119" class="ax_default _线段">
            <img id="u119_img" class="img " src="images/（一期不做）海融宝扫码签约_暂无接口__f501_f502_/u118.svg"/>
            <div id="u119_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (线段) -->
          <div id="u120" class="ax_default _线段">
            <img id="u120_img" class="img " src="images/（一期不做）海融宝扫码签约_暂无接口__f501_f502_/u118.svg"/>
            <div id="u120_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (线段) -->
          <div id="u121" class="ax_default _线段">
            <img id="u121_img" class="img " src="images/（一期不做）海融宝扫码签约_暂无接口__f501_f502_/u118.svg"/>
            <div id="u121_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (线段) -->
          <div id="u122" class="ax_default _线段">
            <img id="u122_img" class="img " src="images/（一期不做）海融宝扫码签约_暂无接口__f501_f502_/u122.svg"/>
            <div id="u122_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u123" class="ax_default primary_button">
          <div id="u123_div" class=""></div>
          <div id="u123_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u124" class="ax_default _文本段落">
        <div id="u124_div" class=""></div>
        <div id="u124_text" class="text ">
          <p><span>数字人民币智能合约子钱包签约</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u125" class="ax_default _形状">
        <div id="u125_div" class=""></div>
        <div id="u125_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u126" class="ax_default _文本段落">
        <div id="u126_div" class=""></div>
        <div id="u126_text" class="text ">
          <p><span>海融宝平台为实现平台会员提供数字⼈⺠币结算服务，海融宝在邮储银⾏指定渠道开通，授权第三⽅平台通过系统指令对客⼾签约的合约⼦钱包进⾏相关操作的服务。服务内容包括但不限于平台通合约⼦钱包开⽴功能、资⾦划拨服务功能、余额查询功能、电⼦回单功能等。</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u127" class="ax_default _文本段落">
        <div id="u127_div" class=""></div>
        <div id="u127_text" class="text ">
          <p><span>提示：请务必开通邮储银行数字人民币钱包</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u128" class="ax_default _文本段落">
        <div id="u128_div" class=""></div>
        <div id="u128_text" class="text ">
          <p style="font-size:20px;"><span>F501 会员签约</span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span>节点属性&nbsp; &nbsp;&nbsp; 中文描述&nbsp; &nbsp;&nbsp; 数据类型&nbsp; &nbsp;&nbsp; 必输&nbsp; &nbsp;&nbsp; 说明</span></p><p style="font-size:13px;"><span>platCustId&nbsp; &nbsp;&nbsp; 平台客户ID&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 平台给会员分配的ID，会员注册登陆平台系统的ID</span></p><p style="font-size:13px;"><span>custIdType&nbsp; &nbsp;&nbsp; 客户证件类型&nbsp; &nbsp;&nbsp; char(4)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 只支持个人.&nbsp; &nbsp; &nbsp;&nbsp; 证件类型：IT01:居民身份证；IT02:军官证；IT03:护照；IT04-户口薄；IT05-士兵证；IT06-港澳往来内地通行证；IT07-台湾同胞来往内地通行证；IT08-临时身份证；IT09-外国人居留证</span></p><p style="font-size:13px;"><span>custIdNo&nbsp; &nbsp;&nbsp; 客户证件号码&nbsp; &nbsp;&nbsp; char(30)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; </span></p><p style="font-size:13px;"><span>custName&nbsp; &nbsp;&nbsp; 客户姓名&nbsp; &nbsp;&nbsp; char(60)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 证件名称</span></p><p style="font-size:13px;"><span>merchantId&nbsp; &nbsp; &nbsp; 合作方编号（商户号）&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 服开商户号，由分行老师提供</span></p><p style="font-size:13px;"><span>appID&nbsp; &nbsp; &nbsp; 应用ID&nbsp; &nbsp;&nbsp; char(30)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 商户在服开平台申请的合约参数 ，由分行老师提供</span></p><p style="font-size:13px;"><span>redirectUrl&nbsp; &nbsp;&nbsp; 回调的业务方地址&nbsp; &nbsp;&nbsp; char(256)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; 流程结束后跳转地址</span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span>节点属性&nbsp; &nbsp;&nbsp; 中文描述&nbsp; &nbsp;&nbsp; 数据类型&nbsp; &nbsp;&nbsp; 必输&nbsp; &nbsp;&nbsp; 说明</span></p><p style="font-size:13px;"><span>code&nbsp; &nbsp;&nbsp; 响应码&nbsp; &nbsp;&nbsp; char(6)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; </span></p><p style="font-size:13px;"><span>message&nbsp; &nbsp;&nbsp; 响应信息&nbsp; &nbsp;&nbsp; char(256)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; </span></p><p style="font-size:13px;"><span>userSignUrl&nbsp; &nbsp;&nbsp; 个人客户签约URL&nbsp; &nbsp;&nbsp; char(500)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; 页面URL</span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span><br></span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u129" class="ax_default _文本段落">
        <div id="u129_div" class=""></div>
        <div id="u129_text" class="text ">
          <p><span>说明：邮储需生产签约平台的二维码（目前暂无接口）</span></p><p><span><br></span></p><p><span>扫描识别传参按邮储，获取签约信息。</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u130" class="ax_default _文本段落">
        <div id="u130_div" class=""></div>
        <div id="u130_text" class="text ">
          <p style="font-size:20px;"><span>F502 会员签约查询</span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span>节点属性&nbsp; &nbsp;&nbsp; 中文描述&nbsp; &nbsp;&nbsp; 数据类型&nbsp; &nbsp;&nbsp; 必输&nbsp; &nbsp;&nbsp; 说明</span></p><p style="font-size:13px;"><span>platCustId&nbsp; &nbsp;&nbsp; 平台客户ID&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 平台给会员分配的ID，会员注册登陆平台系统的ID</span></p><p style="font-size:13px;"><span>custIdType&nbsp; &nbsp;&nbsp; 证件类型&nbsp; &nbsp;&nbsp; char(4)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 只支持个人&nbsp; &nbsp; 证件类型：IT01:居民身份证；IT02:军官证；IT03:护照；IT04-户口薄；IT05-士兵证；IT06-港澳往来内地通行证；IT07-台湾同胞来往内地通行证；IT08-临时身份证；IT09-外国人居留证</span></p><p style="font-size:13px;"><span>custIdNo&nbsp; &nbsp;&nbsp; 证件号码&nbsp; &nbsp;&nbsp; char(30)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 证件号码</span></p><p style="font-size:13px;"><span>custName&nbsp; &nbsp;&nbsp; 客户名称&nbsp; &nbsp;&nbsp; char(100)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 证件名称</span></p><p style="font-size:13px;"><span>custType&nbsp; &nbsp;&nbsp; 客户类型&nbsp; &nbsp;&nbsp; char(2)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; A-个人</span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span>节点属性&nbsp; &nbsp;&nbsp; 中文描述&nbsp; &nbsp;&nbsp; 数据类型&nbsp; &nbsp;&nbsp; 必输&nbsp; &nbsp;&nbsp; 说明</span></p><p style="font-size:13px;"><span>platCustId&nbsp; &nbsp;&nbsp; 平台客户ID&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 平台给会员分配的ID，会员注册登陆平台系统的ID</span></p><p style="font-size:13px;"><span>signContStatus&nbsp; &nbsp;&nbsp; 签约状态&nbsp; &nbsp;&nbsp; char(2)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 0-已签约；1-未签约；2-签约处理中；</span></p><p style="font-size:13px;"><span>custId&nbsp; &nbsp;&nbsp; 客户id&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; 个人签约时生成的ID</span></p><p style="font-size:13px;"><span>signAgrNo&nbsp; &nbsp;&nbsp; 签约协议号&nbsp; &nbsp;&nbsp; char(64)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; 银行生成的签约协议号</span></p><p style="font-size:13px;"><span><br></span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u131" class="ax_default" data-left="521" data-top="10" data-width="426" data-height="18">

        <!-- Unnamed (矩形) -->
        <div id="u132" class="ax_default _文本段落">
          <div id="u132_div" class=""></div>
          <div id="u132_text" class="text ">
            <p><span>交易通道</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u133" class="ax_default _文本段落">
          <div id="u133_div" class=""></div>
          <div id="u133_text" class="text ">
            <p><span>数优联/数市联/数蛋联/数药联/数牛联/数菜联</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u134" class="ax_default _二级标题">
        <div id="u134_div" class=""></div>
        <div id="u134_text" class="text ">
          <p><span>×</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u135" class="ax_default _文本段落">
        <div id="u135_div" class=""></div>
        <div id="u135_text" class="text ">
          <p><span>扫码识别失败</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u136" class="ax_default _文本段落">
        <div id="u136_div" class=""></div>
        <div id="u136_text" class="text ">
          <p><span>&nbsp;&nbsp; &nbsp; &nbsp; 您扫描的二维码可能不正常，确实是邮储银行数字人民币智能合约子钱包吗？请您检查核实！</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u137" class="ax_default primary_button">
        <div id="u137_div" class=""></div>
        <div id="u137_text" class="text ">
          <p><span>确定</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u138" class="ax_default _形状">
        <div id="u138_div" class=""></div>
        <div id="u138_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u139" class="ax_default _二级标题">
        <div id="u139_div" class=""></div>
        <div id="u139_text" class="text ">
          <p><span>×</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u140" class="ax_default _文本段落">
        <div id="u140_div" class=""></div>
        <div id="u140_text" class="text ">
          <p><span>&nbsp;&nbsp; &nbsp; &nbsp; 1、请您确认你是否开通邮储银行数字人民币钱包，未开通请去开通！</span></p><p><span>&nbsp;&nbsp; &nbsp; &nbsp; 2、您需要在海融宝里完善基本资料，请补充完善后再来签约！</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u141" class="ax_default primary_button">
        <div id="u141_div" class=""></div>
        <div id="u141_text" class="text ">
          <p><span>确定</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u142" class="ax_default _文本段落">
        <div id="u142_div" class=""></div>
        <div id="u142_text" class="text ">
          <p><span>开通失败</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
