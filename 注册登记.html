﻿<!DOCTYPE html>
<html>
  <head>
    <title>注册登记</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/注册登记/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/注册登记/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u2754" class="ax_default box_1">
        <div id="u2754_div" class=""></div>
        <div id="u2754_text" class="text ">
          <p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2755" class="ax_default _二级标题">
        <div id="u2755_div" class=""></div>
        <div id="u2755_text" class="text ">
          <p><span>16:18</span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u2756" class="ax_default icon">
        <img id="u2756_img" class="img " src="images/海融宝签约_个人__f501_f502_/u3.svg"/>
        <div id="u2756_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u2757" class="ax_default icon">
        <img id="u2757_img" class="img " src="images/海融宝签约_个人__f501_f502_/u4.svg"/>
        <div id="u2757_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2758" class="ax_default _文本段落">
        <div id="u2758_div" class=""></div>
        <div id="u2758_text" class="text ">
          <p><span>注册登记</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2759" class="ax_default _文本段落">
        <div id="u2759_div" class=""></div>
        <div id="u2759_text" class="text ">
          <p><span>hrypt.com</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2760" class="ax_default primary_button">
        <div id="u2760_div" class=""></div>
        <div id="u2760_text" class="text ">
          <p><span>确认注册</span></p>
        </div>
      </div>

      <!-- Unnamed (手机和验证码输入) -->

      <!-- Unnamed (矩形) -->
      <div id="u2762" class="ax_default _形状">
        <div id="u2762_div" class=""></div>
        <div id="u2762_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2763" class="ax_default _文本段落">
        <div id="u2763_div" class=""></div>
        <div id="u2763_text" class="text ">
          <p><span>手机号</span></p>
        </div>
      </div>

      <!-- 叫号面板按钮 (动态面板) -->
      <div id="u2764" class="ax_default" data-label="叫号面板按钮">
        <div id="u2764_state0" class="panel_state" data-label="State1" style="">
          <div id="u2764_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u2765" class="ax_default box_3">
              <div id="u2765_div" class=""></div>
              <div id="u2765_text" class="text ">
                <p><span>获取验证码</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u2764_state1" class="panel_state" data-label="State2" style="visibility: hidden;">
          <div id="u2764_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u2766" class="ax_default box_3">
              <div id="u2766_div" class=""></div>
              <div id="u2766_text" class="text ">
                <p><span>s</span></p>
              </div>
            </div>

            <!-- 叫号倒计时 (文本框) -->
            <div id="u2767" class="ax_default text_field ax_default_hidden" data-label="叫号倒计时" style="display:none; visibility: hidden">
              <div id="u2767_div" class=""></div>
              <input id="u2767_input" type="text" value="15" class="u2767_input" readonly/>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u2768" class="ax_default text_field">
        <div id="u2768_div" class=""></div>
        <input id="u2768_input" type="text" value="" class="u2768_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2769" class="ax_default _文本段落">
        <div id="u2769_div" class=""></div>
        <div id="u2769_text" class="text ">
          <p><span>134 8250 8234</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2770" class="ax_default _文本段落">
        <div id="u2770_div" class=""></div>
        <div id="u2770_text" class="text ">
          <p><span>验证码</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u2771" class="ax_default text_field">
        <div id="u2771_div" class=""></div>
        <input id="u2771_input" type="text" value="" class="u2771_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2772" class="ax_default _文本段落">
        <div id="u2772_div" class=""></div>
        <div id="u2772_text" class="text ">
          <p><span>输入短信验证码</span></p>
        </div>
      </div>
      <div id="u2761" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (矩形) -->
      <div id="u2773" class="ax_default _形状">
        <div id="u2773_div" class=""></div>
        <div id="u2773_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2774" class="ax_default box_1">
        <div id="u2774_div" class=""></div>
        <div id="u2774_text" class="text ">
          <p><span>输入密码</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2775" class="ax_default box_1">
        <div id="u2775_div" class=""></div>
        <div id="u2775_text" class="text ">
          <p><span>再输入一次</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2776" class="ax_default box_1">
        <div id="u2776_div" class=""></div>
        <div id="u2776_text" class="text ">
          <p><span>请输入登录密码</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2777" class="ax_default box_1">
        <div id="u2777_div" class=""></div>
        <div id="u2777_text" class="text ">
          <p><span>请再输入登录密码</span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u2778" class="ax_default icon">
        <img id="u2778_img" class="img " src="images/登陆密码修改/u2133.svg"/>
        <div id="u2778_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u2779" class="ax_default icon">
        <img id="u2779_img" class="img " src="images/登陆密码修改/u2133.svg"/>
        <div id="u2779_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 操作状态 (动态面板) -->
      <div id="u2780" class="ax_default ax_default_hidden" data-label="操作状态" style="display:none; visibility: hidden">
        <div id="u2780_state0" class="panel_state" data-label="操作成功" style="">
          <div id="u2780_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u2781" class="ax_default box_3">
              <div id="u2781_div" class=""></div>
              <div id="u2781_text" class="text ">
                <p><span>操作成功！</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u2780_state1" class="panel_state" data-label="操作失败" style="visibility: hidden;">
          <div id="u2780_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u2782" class="ax_default box_3">
              <div id="u2782_div" class=""></div>
              <div id="u2782_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2783" class="ax_default _文本段落">
              <div id="u2783_div" class=""></div>
              <div id="u2783_text" class="text ">
                <p><span>操作失败！</span></p>
              </div>
            </div>

            <!-- Unnamed (图片 ) -->
            <div id="u2784" class="ax_default _图片_">
              <img id="u2784_img" class="img " src="images/海融宝签约_个人__f501_f502_/u10.png"/>
              <div id="u2784_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
