﻿<!DOCTYPE html>
<html>
  <head>
    <title>示意图-邮储充值确认(邮储页面)</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/示意图-邮储充值确认_邮储页面_/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/示意图-邮储充值确认_邮储页面_/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (基础app框架(H5)) -->

      <!-- Unnamed (矩形) -->
      <div id="u610" class="ax_default box_1">
        <div id="u610_div" class=""></div>
        <div id="u610_text" class="text ">
          <p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u611" class="ax_default _二级标题">
        <div id="u611_div" class=""></div>
        <div id="u611_text" class="text ">
          <p><span>16:18</span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u612" class="ax_default icon">
        <img id="u612_img" class="img " src="images/海融宝签约_个人__f501_f502_/u3.svg"/>
        <div id="u612_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u613" class="ax_default icon">
        <img id="u613_img" class="img " src="images/海融宝签约_个人__f501_f502_/u4.svg"/>
        <div id="u613_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u614" class="ax_default _文本段落">
        <div id="u614_div" class=""></div>
        <div id="u614_text" class="text ">
          <p><span>充值确认</span></p>
        </div>
      </div>

      <!-- 操作状态 (动态面板) -->
      <div id="u615" class="ax_default ax_default_hidden" data-label="操作状态" style="display:none; visibility: hidden">
        <div id="u615_state0" class="panel_state" data-label="操作成功" style="">
          <div id="u615_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u616" class="ax_default box_3">
              <div id="u616_div" class=""></div>
              <div id="u616_text" class="text ">
                <p><span>操作成功！</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u615_state1" class="panel_state" data-label="操作失败" style="visibility: hidden;">
          <div id="u615_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u617" class="ax_default box_3">
              <div id="u617_div" class=""></div>
              <div id="u617_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u618" class="ax_default _文本段落">
              <div id="u618_div" class=""></div>
              <div id="u618_text" class="text ">
                <p><span>操作失败！</span></p>
              </div>
            </div>

            <!-- Unnamed (图片 ) -->
            <div id="u619" class="ax_default _图片_">
              <img id="u619_img" class="img " src="images/海融宝签约_个人__f501_f502_/u10.png"/>
              <div id="u619_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u620" class="ax_default _图片_">
        <img id="u620_img" class="img " src="images/海融宝签约_个人__f501_f502_/u11.png"/>
        <div id="u620_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u621" class="ax_default icon">
        <img id="u621_img" class="img " src="images/海融宝签约_个人__f501_f502_/u12.svg"/>
        <div id="u621_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u622" class="ax_default _文本段落">
        <div id="u622_div" class=""></div>
        <div id="u622_text" class="text ">
          <p><span>hrypt.com</span></p>
        </div>
      </div>
      <div id="u609" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (矩形) -->
      <div id="u623" class="ax_default primary_button">
        <div id="u623_div" class=""></div>
        <div id="u623_text" class="text ">
          <p><span>确认支付</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u624" class="ax_default _形状">
        <div id="u624_div" class=""></div>
        <div id="u624_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u625" class="ax_default _文本段落">
        <div id="u625_div" class=""></div>
        <div id="u625_text" class="text ">
          <p><span>￥53,000.00</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u626" class="ax_default _文本段落">
        <div id="u626_div" class=""></div>
        <div id="u626_text" class="text ">
          <p><span>充值账号:</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u627" class="ax_default" data-left="134" data-top="201" data-width="284" data-height="25">

        <!-- Unnamed (矩形) -->
        <div id="u628" class="ax_default _文本段落">
          <div id="u628_div" class=""></div>
          <div id="u628_text" class="text ">
            <p><span>中国邮政储蓄银行 (0416)</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u629" class="ax_default _文本段落">
        <div id="u629_div" class=""></div>
        <div id="u629_text" class="text ">
          <p><span>收款钱包ID:</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u630" class="ax_default" data-left="134" data-top="226" data-width="284" data-height="25">

        <!-- Unnamed (矩形) -->
        <div id="u631" class="ax_default _文本段落">
          <div id="u631_div" class=""></div>
          <div id="u631_text" class="text ">
            <p><span>3100 **** **** 8888</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u632" class="ax_default" data-left="22" data-top="389" data-width="460" data-height="128">

        <!-- Unnamed (矩形) -->
        <div id="u633" class="ax_default _形状">
          <div id="u633_div" class=""></div>
          <div id="u633_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u634" class="ax_default _文本段落">
          <div id="u634_div" class=""></div>
          <div id="u634_text" class="text ">
            <p><span>支付密码</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u635" class="ax_default" data-left="92" data-top="446" data-width="343" data-height="35">

          <!-- Unnamed (文本框) -->
          <div id="u636" class="ax_default text_field">
            <div id="u636_div" class=""></div>
            <input id="u636_input" type="text" value="" class="u636_input"/>
          </div>

          <!-- Unnamed (文本框) -->
          <div id="u637" class="ax_default text_field">
            <div id="u637_div" class=""></div>
            <input id="u637_input" type="text" value="" class="u637_input"/>
          </div>

          <!-- Unnamed (文本框) -->
          <div id="u638" class="ax_default text_field">
            <div id="u638_div" class=""></div>
            <input id="u638_input" type="text" value="" class="u638_input"/>
          </div>

          <!-- Unnamed (文本框) -->
          <div id="u639" class="ax_default text_field">
            <div id="u639_div" class=""></div>
            <input id="u639_input" type="text" value="" class="u639_input"/>
          </div>

          <!-- Unnamed (文本框) -->
          <div id="u640" class="ax_default text_field">
            <div id="u640_div" class=""></div>
            <input id="u640_input" type="text" value="" class="u640_input"/>
          </div>

          <!-- Unnamed (文本框) -->
          <div id="u641" class="ax_default text_field">
            <div id="u641_div" class=""></div>
            <input id="u641_input" type="text" value="" class="u641_input"/>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u642" class="ax_default _形状">
        <div id="u642_div" class=""></div>
        <div id="u642_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u643" class="ax_default _文本段落">
        <div id="u643_div" class=""></div>
        <div id="u643_text" class="text ">
          <p><span>温馨提示</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u644" class="ax_default _文本段落">
        <div id="u644_div" class=""></div>
        <div id="u644_text" class="text ">
          <p><span>1.交易确认后，资⾦将从您的数字⼈⺠币钱包划转海融宝合约⼦钱包内，海融宝合约⼦钱包内资⾦仅限在交易平台使⽤，在平台外⽆法使⽤该部分资⾦进⾏转钱、⽀付、查询等交易操作，请确保您的资⾦合理使⽤。</span></p><p><span>2.若您不再继续使⽤海融宝合约⼦钱包，请及时通过交易平台发起资⾦提取。</span></p><p><span>3.为了您的资⾦安全，请勿将钱包⽀付密码、短信验证码泄露给他⼈。</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u645" class="ax_default _文本段落">
        <div id="u645_div" class=""></div>
        <div id="u645_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u646" class="ax_default" data-left="22" data-top="271" data-width="442" data-height="43">

        <!-- Unnamed (矩形) -->
        <div id="u647" class="ax_default _文本段落">
          <div id="u647_div" class=""></div>
          <div id="u647_text" class="text ">
            <p><span>限100000.00元/笔,100000.00元/日</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u648" class="ax_default" data-left="22" data-top="271" data-width="442" data-height="25">

          <!-- Unnamed (矩形) -->
          <div id="u649" class="ax_default _文本段落">
            <div id="u649_div" class=""></div>
            <div id="u649_text" class="text ">
              <p><span>充值方式</span></p>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u650" class="ax_default" data-left="159" data-top="271" data-width="305" data-height="25">

            <!-- Unnamed (矩形) -->
            <div id="u651" class="ax_default _文本段落">
              <div id="u651_div" class=""></div>
              <div id="u651_text" class="text ">
                <p><span>中国邮政储蓄银行 (0416)</span></p>
              </div>
            </div>

            <!-- Unnamed (形状) -->
            <div id="u652" class="ax_default icon">
              <img id="u652_img" class="img " src="images/示意图-邮储充值确认_邮储页面_/u652.svg"/>
              <div id="u652_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u653" class="ax_default _形状">
        <div id="u653_div" class=""></div>
        <div id="u653_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u654" class="ax_default _文本段落">
        <div id="u654_div" class=""></div>
        <div id="u654_text" class="text ">
          <p><span>交易时间</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u655" class="ax_default _文本段落">
        <div id="u655_div" class=""></div>
        <div id="u655_text" class="text ">
          <p><span>子账户全称</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u656" class="ax_default _文本段落">
        <div id="u656_div" class=""></div>
        <div id="u656_text" class="text ">
          <p><span>收单机构</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u657" class="ax_default _文本段落">
        <div id="u657_div" class=""></div>
        <div id="u657_text" class="text ">
          <p><span>用户钱包ID</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u658" class="ax_default" data-left="574" data-top="113" data-width="426" data-height="18">

        <!-- Unnamed (矩形) -->
        <div id="u659" class="ax_default _文本段落">
          <div id="u659_div" class=""></div>
          <div id="u659_text" class="text ">
            <p><span>订单单号</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u660" class="ax_default _文本段落">
          <div id="u660_div" class=""></div>
          <div id="u660_text" class="text ">
            <p><span>4200002682886982550742025051</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u661" class="ax_default _文本段落">
        <div id="u661_div" class=""></div>
        <div id="u661_text" class="text ">
          <p><span>2025年5月18日 14:21:31</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u662" class="ax_default _文本段落">
        <div id="u662_div" class=""></div>
        <div id="u662_text" class="text ">
          <p><span>海创未来(福建)科技有限公司</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u663" class="ax_default _文本段落">
        <div id="u663_div" class=""></div>
        <div id="u663_text" class="text ">
          <p><span>中国邮政储蓄银行福建省分行</span></p><p><span><br></span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u664" class="ax_default _文本段落">
        <div id="u664_div" class=""></div>
        <div id="u664_text" class="text ">
          <p><span>邮储银行 数字人民币钱包id（8888）</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u665" class="ax_default" data-left="574" data-top="80" data-width="426" data-height="18">

        <!-- Unnamed (矩形) -->
        <div id="u666" class="ax_default _文本段落">
          <div id="u666_div" class=""></div>
          <div id="u666_text" class="text ">
            <p><span>交易金额</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u667" class="ax_default _文本段落">
          <div id="u667_div" class=""></div>
          <div id="u667_text" class="text ">
            <p><span>53,000.00</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u668" class="ax_default _文本段落">
        <div id="u668_div" class=""></div>
        <div id="u668_text" class="text ">
          <p><span>充值信息（应用平台接口数据）</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u669" class="ax_default" data-left="574" data-top="345" data-width="426" data-height="18">

        <!-- Unnamed (矩形) -->
        <div id="u670" class="ax_default _文本段落">
          <div id="u670_div" class=""></div>
          <div id="u670_text" class="text ">
            <p><span>操作附言</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u671" class="ax_default _文本段落">
          <div id="u671_div" class=""></div>
          <div id="u671_text" class="text ">
            <p><span>无</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u672" class="ax_default" data-left="574" data-top="311" data-width="426" data-height="18">

        <!-- Unnamed (矩形) -->
        <div id="u673" class="ax_default _文本段落">
          <div id="u673_div" class=""></div>
          <div id="u673_text" class="text ">
            <p><span>交易通道</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u674" class="ax_default _文本段落">
          <div id="u674_div" class=""></div>
          <div id="u674_text" class="text ">
            <p><span>数优联/数市联/数蛋联/数药联/数牛联/数菜联</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u675" class="ax_default _文本段落">
        <div id="u675_div" class=""></div>
        <div id="u675_text" class="text ">
          <p><span>说明：以上多余字段保留，实际传输以邮储为准。</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u676" class="ax_default" data-left="574" data-top="176" data-width="426" data-height="18">

        <!-- Unnamed (矩形) -->
        <div id="u677" class="ax_default _文本段落">
          <div id="u677_div" class=""></div>
          <div id="u677_text" class="text ">
            <p><span>付款单号</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u678" class="ax_default _文本段落">
          <div id="u678_div" class=""></div>
          <div id="u678_text" class="text ">
            <p><span>8869825504200002674202505182</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u679" class="ax_default _文本段落">
        <div id="u679_div" class=""></div>
        <div id="u679_text" class="text ">
          <p style="font-size:20px;"><span style="font-family:'PingFang SC ', 'PingFang SC';color:#000000;">F510入金创单</span></p><p style="font-size:13px;"><span style="font-family:'Arial Normal', 'Arial';"><br></span></p><p style="font-size:13px;"><span style="font-family:'Arial Normal', 'Arial';">节点属性&nbsp; &nbsp;&nbsp; 中文描述&nbsp; &nbsp;&nbsp; 数据类型&nbsp; &nbsp;&nbsp; 必输&nbsp; &nbsp;&nbsp; 说明</span></p><p style="font-size:13px;"><span style="font-family:'Arial Normal', 'Arial';">platOrderNo&nbsp; &nbsp;&nbsp; 平台入金订单号&nbsp; &nbsp;&nbsp; char(100)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 平台生成的唯一标识,银企客户号+yyyyMMdd+序号</span></p><p style="font-size:13px;"><span style="font-family:'Arial Normal', 'Arial';">custId&nbsp; &nbsp;&nbsp; 客户id&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 个人会员签约时生成</span></p><p style="font-size:13px;"><span style="font-family:'Arial Normal', 'Arial';">signAgrNo&nbsp; &nbsp;&nbsp; 签约协议号&nbsp; &nbsp;&nbsp; char(64)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 个人会员签约时生成</span></p><p style="font-size:13px;"><span style="font-family:'Arial Normal', 'Arial';">txAmt&nbsp; &nbsp;&nbsp; 交易金额&nbsp; &nbsp;&nbsp; char(22)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 保留两位小数</span></p><p style="font-size:13px;"><span style="font-family:'Arial Normal', 'Arial';">txCurr&nbsp; &nbsp;&nbsp; 交易币种&nbsp; &nbsp;&nbsp; char(3)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 人民币CNY</span></p><p style="font-size:13px;"><span style="font-family:'Arial Normal', 'Arial';">merchantId&nbsp; &nbsp; &nbsp; 合作方编号（商户号）&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 服开商户号，由分行老师提供</span></p><p style="font-size:13px;"><span style="font-family:'Arial Normal', 'Arial';">appID&nbsp; &nbsp; &nbsp; 应用ID&nbsp; &nbsp;&nbsp; char(30)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 商户在服开平台申请的合约参数 ，由分行老师提供</span></p><p style="font-size:13px;"><span style="font-family:'Arial Normal', 'Arial';">redirectUrl&nbsp; &nbsp;&nbsp; 回调的业务方地址&nbsp; &nbsp;&nbsp; char(256)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; 流程结束后跳转地址</span></p><p style="font-size:13px;"><span style="font-family:'Arial Normal', 'Arial';"><br></span></p><p style="font-size:13px;"><span style="font-family:'Arial Normal', 'Arial';">节点属性&nbsp; &nbsp;&nbsp; 中文描述&nbsp; &nbsp;&nbsp; 数据类型&nbsp; &nbsp;&nbsp; 必输&nbsp; &nbsp;&nbsp; 说明</span></p><p style="font-size:13px;"><span style="font-family:'Arial Normal', 'Arial';">userLgnUrl&nbsp; &nbsp;&nbsp; 用户登录URL&nbsp; &nbsp;&nbsp; Char(500)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 入金创单页面URL</span></p><p style="font-size:13px;"><span style="font-family:'Arial Normal', 'Arial';"><br></span></p><p style="font-size:13px;"><span style="font-family:'Arial Normal', 'Arial';"><br></span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u680" class="ax_default _文本段落">
        <div id="u680_div" class=""></div>
        <div id="u680_text" class="text ">
          <p style="font-size:20px;"><span>F507 入金订单查询</span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span>节点属性&nbsp; &nbsp;&nbsp; 中文描述&nbsp; &nbsp;&nbsp; 数据类型&nbsp; &nbsp;&nbsp; 必输&nbsp; &nbsp;&nbsp; 说明</span></p><p style="font-size:13px;"><span>platOrderNo&nbsp; &nbsp;&nbsp; 平台订单号&nbsp; &nbsp;&nbsp; char(100)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 出入金交易的平台订单号</span></p><p style="font-size:13px;"><span>custId&nbsp; &nbsp;&nbsp; 客户Id&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 出入金收付方都是同一个客户ID</span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span>节点属性&nbsp; &nbsp;&nbsp; 中文描述&nbsp; &nbsp;&nbsp; 数据类型&nbsp; &nbsp;&nbsp; 必输&nbsp; &nbsp;&nbsp; 说明</span></p><p style="font-size:13px;"><span>pwltId&nbsp; &nbsp;&nbsp; 母钱包id&nbsp; &nbsp;&nbsp; char(16)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; </span></p><p style="font-size:13px;"><span>pwltName&nbsp; &nbsp;&nbsp; 母钱包名称&nbsp; &nbsp;&nbsp; char(70)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; </span></p><p style="font-size:13px;"><span>txCurrCd&nbsp; &nbsp;&nbsp; 交易币种代码&nbsp; &nbsp;&nbsp; char(3)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 人民币CNY</span></p><p style="font-size:13px;"><span>txAmt&nbsp; &nbsp;&nbsp; 交易金额&nbsp; &nbsp;&nbsp; char(22)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; </span></p><p style="font-size:13px;"><span>frzAgrNo&nbsp; &nbsp;&nbsp; 冻结协议号&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; 入金并冻结时返回</span></p><p style="font-size:13px;"><span>txOrderNo&nbsp; &nbsp;&nbsp; 交易订单号&nbsp; &nbsp;&nbsp; char(100)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; </span></p><p style="font-size:13px;"><span>txRemark&nbsp; &nbsp;&nbsp; 交易备注&nbsp; &nbsp;&nbsp; char(256)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; </span></p><p style="font-size:13px;"><span>accblStmOrderStaCd&nbsp; &nbsp;&nbsp; 账单结算订单状态代码&nbsp; &nbsp;&nbsp; char(2)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 01-待结算，02-结算成功，03-结算失败，04-已关闭，05-订单处理中</span></p><p style="font-size:13px;"><span>failReason&nbsp; &nbsp;&nbsp; 失败原因&nbsp; &nbsp;&nbsp; char(500)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; 账单结算订单状态代码为03-结算失败时返回</span></p><p style="font-size:13px;"><span>platfMerOrderNo&nbsp; &nbsp;&nbsp; 平台商户出入金订单号&nbsp; &nbsp;&nbsp; char(100)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; </span></p><p style="font-size:13px;"><span>pwltPayAmt&nbsp; &nbsp;&nbsp; 付款方母钱支付包金额&nbsp; &nbsp;&nbsp; char(22)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; 查询入金信息时返回</span></p><p style="font-size:13px;"><span>bkcdPayAmt&nbsp; &nbsp;&nbsp; 付款方银行卡支付金额&nbsp; &nbsp;&nbsp; char(22)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; 查询入金信息时返回</span></p><p style="font-size:13px;"><span>bkcdName&nbsp; &nbsp;&nbsp; 付款方银行卡名称&nbsp; &nbsp;&nbsp; char(60)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; 查询入金信息时返回</span></p><p style="font-size:13px;"><span>bkcdNo&nbsp; &nbsp;&nbsp; 付款方银行卡卡号&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; 查询入金信息时返回</span></p><p style="font-size:13px;"><span>txTime&nbsp; &nbsp;&nbsp; 交易时间&nbsp; &nbsp;&nbsp; char(14)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; yyyyMMddHHmmss</span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span><br></span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
