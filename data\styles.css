﻿.ax_default {
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  line-height:normal;
  text-transform:none;
}
._形状 {
}
.box_1 {
}
._一级标题 {
  font-family:'Arial Normal', 'Arial';
  font-weight:bold;
  font-style:normal;
  font-size:32px;
  text-align:left;
}
._二级标题 {
  font-family:'Arial Normal', 'Arial';
  font-weight:bold;
  font-style:normal;
  font-size:24px;
  text-align:left;
}
._三级标题 {
  font-family:'Arial Normal', 'Arial';
  font-weight:bold;
  font-style:normal;
  font-size:18px;
  text-align:left;
}
._四级标题 {
  font-family:'Arial Normal', 'Arial';
  font-weight:bold;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
._五级标题 {
  font-family:'Arial Normal', 'Arial';
  font-weight:bold;
  font-style:normal;
  text-align:left;
}
._六级标题 {
  font-family:'Arial Normal', 'Arial';
  font-weight:bold;
  font-style:normal;
  font-size:10px;
  text-align:left;
}
._文本段落 {
  text-align:left;
}
._表单提示 {
  color:#999999;
}
._表单禁用 {
}
._流程形状 {
}
._线段 {
}
._连接 {
  font-size:10px;
}
._图片 {
}
.icon {
}
.image {
}
.button {
}
.text_field {
  color:#000000;
  text-align:left;
}
.form_hint {
  color:#999999;
}
.form_disabled {
}
.radio_button {
  text-align:left;
}
._图片_ {
}
.droplist {
  color:#000000;
  text-align:left;
}
.label {
  font-size:14px;
  text-align:left;
}
.line {
}
.primary_button {
  color:#FFFFFF;
}
.text_area {
  color:#000000;
  text-align:left;
}
.table_cell {
}
.marker {
  color:#FFFFFF;
}
.box_3 {
}
.box_2 {
}
.arrow {
}
.checkbox {
  text-align:left;
}
.ellipse {
}
._文本框 {
  color:#000000;
  text-align:left;
}
.label1 {
  font-size:14px;
  text-align:left;
}
.label2 {
  font-size:14px;
  text-align:left;
}
.flow_shape {
}
textarea, select, input, button { outline: none; }
