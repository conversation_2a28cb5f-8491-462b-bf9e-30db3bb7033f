﻿<!DOCTYPE html>
<html>
  <head>
    <title>示意图-解约子钱包（邮储页面）</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/示意图-解约子钱包（邮储页面）/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/示意图-解约子钱包（邮储页面）/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (基础app框架(H5)) -->

      <!-- Unnamed (矩形) -->
      <div id="u550" class="ax_default box_1">
        <div id="u550_div" class=""></div>
        <div id="u550_text" class="text ">
          <p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u551" class="ax_default _二级标题">
        <div id="u551_div" class=""></div>
        <div id="u551_text" class="text ">
          <p><span>16:18</span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u552" class="ax_default icon">
        <img id="u552_img" class="img " src="images/海融宝签约_个人__f501_f502_/u3.svg"/>
        <div id="u552_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u553" class="ax_default icon">
        <img id="u553_img" class="img " src="images/海融宝签约_个人__f501_f502_/u4.svg"/>
        <div id="u553_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u554" class="ax_default _文本段落">
        <div id="u554_div" class=""></div>
        <div id="u554_text" class="text ">
          <p><span>解约子钱包确认</span></p>
        </div>
      </div>

      <!-- 操作状态 (动态面板) -->
      <div id="u555" class="ax_default ax_default_hidden" data-label="操作状态" style="display:none; visibility: hidden">
        <div id="u555_state0" class="panel_state" data-label="操作成功" style="">
          <div id="u555_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u556" class="ax_default box_3">
              <div id="u556_div" class=""></div>
              <div id="u556_text" class="text ">
                <p><span>操作成功！</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u555_state1" class="panel_state" data-label="操作失败" style="visibility: hidden;">
          <div id="u555_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u557" class="ax_default box_3">
              <div id="u557_div" class=""></div>
              <div id="u557_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u558" class="ax_default _文本段落">
              <div id="u558_div" class=""></div>
              <div id="u558_text" class="text ">
                <p><span>操作失败！</span></p>
              </div>
            </div>

            <!-- Unnamed (图片 ) -->
            <div id="u559" class="ax_default _图片_">
              <img id="u559_img" class="img " src="images/海融宝签约_个人__f501_f502_/u10.png"/>
              <div id="u559_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u560" class="ax_default _图片_">
        <img id="u560_img" class="img " src="images/海融宝签约_个人__f501_f502_/u11.png"/>
        <div id="u560_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u561" class="ax_default icon">
        <img id="u561_img" class="img " src="images/海融宝签约_个人__f501_f502_/u12.svg"/>
        <div id="u561_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u562" class="ax_default _文本段落">
        <div id="u562_div" class=""></div>
        <div id="u562_text" class="text ">
          <p><span>hrypt.com</span></p>
        </div>
      </div>
      <div id="u549" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (矩形) -->
      <div id="u563" class="ax_default primary_button">
        <div id="u563_div" class=""></div>
        <div id="u563_text" class="text ">
          <p><span>确认解约</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u564" class="ax_default" data-left="31.9990489135652" data-top="386.750000738421" data-width="461.000951086435" data-height="134.249999261579">

        <!-- Unnamed (矩形) -->
        <div id="u565" class="ax_default _形状">
          <div id="u565_div" class=""></div>
          <div id="u565_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u566" class="ax_default _文本段落">
          <div id="u566_div" class=""></div>
          <div id="u566_text" class="text ">
            <p><span>支付密码</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u567" class="ax_default" data-left="103" data-top="444" data-width="343" data-height="35">

          <!-- Unnamed (文本框) -->
          <div id="u568" class="ax_default text_field">
            <div id="u568_div" class=""></div>
            <input id="u568_input" type="text" value="" class="u568_input"/>
          </div>

          <!-- Unnamed (文本框) -->
          <div id="u569" class="ax_default text_field">
            <div id="u569_div" class=""></div>
            <input id="u569_input" type="text" value="" class="u569_input"/>
          </div>

          <!-- Unnamed (文本框) -->
          <div id="u570" class="ax_default text_field">
            <div id="u570_div" class=""></div>
            <input id="u570_input" type="text" value="" class="u570_input"/>
          </div>

          <!-- Unnamed (文本框) -->
          <div id="u571" class="ax_default text_field">
            <div id="u571_div" class=""></div>
            <input id="u571_input" type="text" value="" class="u571_input"/>
          </div>

          <!-- Unnamed (文本框) -->
          <div id="u572" class="ax_default text_field">
            <div id="u572_div" class=""></div>
            <input id="u572_input" type="text" value="" class="u572_input"/>
          </div>

          <!-- Unnamed (文本框) -->
          <div id="u573" class="ax_default text_field">
            <div id="u573_div" class=""></div>
            <input id="u573_input" type="text" value="" class="u573_input"/>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u574" class="ax_default _线段">
          <img id="u574_img" class="img " src="images/示意图-解约子钱包（邮储页面）/u574.svg"/>
          <div id="u574_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u575" class="ax_default _文本段落">
        <div id="u575_div" class=""></div>
        <div id="u575_text" class="text ">
          <p><span>邮储签约数字⼈⺠币钱包</span></p><p><span>钱包ID：3100 **** **** 8888</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u576" class="ax_default _文本段落">
        <div id="u576_div" class=""></div>
        <div id="u576_text" class="text ">
          <p><span>节点属性&nbsp; &nbsp;&nbsp; 中文描述&nbsp; &nbsp;&nbsp; 数据类型&nbsp; &nbsp;&nbsp; 必输&nbsp; &nbsp;&nbsp; 说明</span></p><p><span>custId&nbsp; &nbsp;&nbsp; 客户ID&nbsp; &nbsp;&nbsp; Char(32)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 个人签约时生成</span></p><p><span>sessNo&nbsp; &nbsp;&nbsp; 会话编号&nbsp; &nbsp;&nbsp; Char(64)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 会员解约申请获取</span></p><p><span>verifyCode&nbsp; &nbsp;&nbsp; 短信验证码&nbsp; &nbsp;&nbsp; Char(6)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 会员解约申请获取</span></p><p><span><br></span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u577" class="ax_default _文本段落">
        <div id="u577_div" class=""></div>
        <div id="u577_text" class="text ">
          <p><span>节点属性&nbsp; &nbsp;&nbsp; 中文描述&nbsp; &nbsp;&nbsp; 数据类型&nbsp; &nbsp;&nbsp; 必输&nbsp; &nbsp;&nbsp; 说明</span></p><p><span>bdlStaCd&nbsp; &nbsp;&nbsp; 业务处理状态代码&nbsp; &nbsp;&nbsp; char(4)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; PR00-成功；PR01-失败；PR02-处理中</span></p><p><span>failReason&nbsp; &nbsp;&nbsp; 失败原因&nbsp; &nbsp;&nbsp; char(500)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; 当业务处理状态代码为PR01-失败时返回</span></p><p><span><br></span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u578" class="ax_default _文本段落">
        <div id="u578_div" class=""></div>
        <div id="u578_text" class="text ">
          <p><span>解约原因</span></p>
        </div>
      </div>

      <!-- Unnamed (文本域) -->
      <div id="u579" class="ax_default text_area">
        <div id="u579_div" class=""></div>
        <textarea id="u579_input" class="u579_input">请输入解约原因，不低于10个字。</textarea>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
