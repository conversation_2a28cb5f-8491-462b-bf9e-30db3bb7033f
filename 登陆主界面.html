﻿<!DOCTYPE html>
<html>
  <head>
    <title>登陆主界面</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/登陆主界面/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/登陆主界面/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u2613" class="ax_default box_1">
        <div id="u2613_div" class=""></div>
        <div id="u2613_text" class="text ">
          <p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2614" class="ax_default _二级标题">
        <div id="u2614_div" class=""></div>
        <div id="u2614_text" class="text ">
          <p><span>16:18</span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u2615" class="ax_default icon">
        <img id="u2615_img" class="img " src="images/海融宝签约_个人__f501_f502_/u3.svg"/>
        <div id="u2615_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u2616" class="ax_default icon">
        <img id="u2616_img" class="img " src="images/海融宝签约_个人__f501_f502_/u4.svg"/>
        <div id="u2616_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2617" class="ax_default _文本段落">
        <div id="u2617_div" class=""></div>
        <div id="u2617_text" class="text ">
          <p><span>登陆</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2618" class="ax_default _文本段落">
        <div id="u2618_div" class=""></div>
        <div id="u2618_text" class="text ">
          <p><span>hrypt.com</span></p>
        </div>
      </div>

      <!-- Unnamed (项目logo) -->

      <!-- Unnamed (图片 ) -->
      <div id="u2620" class="ax_default _图片_">
        <img id="u2620_img" class="img " src="images/登陆主界面/u2620.svg"/>
        <div id="u2620_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2621" class="ax_default _文本段落">
        <div id="u2621_div" class=""></div>
        <div id="u2621_text" class="text ">
          <p><span>海融宝清算平台</span></p>
        </div>
      </div>
      <div id="u2619" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (矩形) -->
      <div id="u2622" class="ax_default _一级标题">
        <div id="u2622_div" class=""></div>
        <div id="u2622_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2623" class="ax_default _文本段落">
        <div id="u2623_div" class=""></div>
        <div id="u2623_text" class="text ">
          <p><span style="color:#999999;">首次登录会自动进行注册，注册即为同意</span><span style="color:#33CC00;">《用户协议》《隐私政策》</span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u2624" class="ax_default icon">
        <img id="u2624_img" class="img " src="images/登陆主界面/u2624.svg"/>
        <div id="u2624_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2625" class="ax_default box_1">
        <div id="u2625_div" class=""></div>
        <div id="u2625_text" class="text ">
          <p><span>&nbsp; 请输入手机号</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2626" class="ax_default box_1">
        <div id="u2626_div" class=""></div>
        <div id="u2626_text" class="text ">
          <p><span>&nbsp; 请输入密码</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2627" class="ax_default _文本段落">
        <div id="u2627_div" class=""></div>
        <div id="u2627_text" class="text ">
          <p><span>忘记密码&gt;</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2628" class="ax_default _文本段落">
        <div id="u2628_div" class=""></div>
        <div id="u2628_text" class="text ">
          <p><span>注册登记&gt;</span></p>
        </div>
      </div>

      <!-- 错误提示一 (组合) -->
      <div id="u2629" class="ax_default ax_default_hidden" data-label="错误提示一" style="display:none; visibility: hidden" data-left="164" data-top="249" data-width="200" data-height="138">

        <!-- Unnamed (矩形) -->
        <div id="u2630" class="ax_default box_1">
          <div id="u2630_div" class=""></div>
          <div id="u2630_text" class="text ">
            <p><span>1、账号不存在</span></p><p><span>2、密码错误</span></p><p><span>3、网络错误</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2631" class="ax_default box_1">
          <div id="u2631_div" class=""></div>
          <div id="u2631_text" class="text ">
            <p><span>账号不存在</span></p>
          </div>
        </div>

        <!-- Unnamed (文本框) -->
        <div id="u2632" class="ax_default _文本框">
          <div id="u2632_div" class=""></div>
          <input id="u2632_input" type="text" value="提示" class="u2632_input"/>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2633" class="ax_default primary_button">
          <div id="u2633_div" class=""></div>
          <div id="u2633_text" class="text ">
            <p><span>确认</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2634" class="ax_default primary_button">
        <div id="u2634_div" class=""></div>
        <div id="u2634_text" class="text ">
          <p><span>登录</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2635" class="ax_default primary_button">
        <div id="u2635_div" class=""></div>
        <div id="u2635_text" class="text ">
          <p><span>短信验证登录</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2636" class="ax_default primary_button">
        <div id="u2636_div" class=""></div>
        <div id="u2636_text" class="text ">
          <p><span>微信一键登录</span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u2637" class="ax_default icon">
        <img id="u2637_img" class="img " src="images/登陆主界面/u2637.svg"/>
        <div id="u2637_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
