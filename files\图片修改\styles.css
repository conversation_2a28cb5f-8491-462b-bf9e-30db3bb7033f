﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:510px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u5399_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:895px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.298039215686275);
  border:none;
  border-radius:50px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u5399 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:895px;
  display:flex;
}
#u5399 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5399_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5400_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:486px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u5400 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:367px;
  width:510px;
  height:486px;
  display:flex;
}
#u5400 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5400_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5401_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u5401 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:392px;
  width:25px;
  height:25px;
  display:flex;
}
#u5401 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5401_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5402_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:354px;
  height:25px;
}
#u5402 {
  border-width:0px;
  position:absolute;
  left:71px;
  top:392px;
  width:354px;
  height:25px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u5402 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5402_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5403_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:511px;
  height:2px;
}
#u5403 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:434px;
  width:510px;
  height:1px;
  display:flex;
  font-size:16px;
}
#u5403 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5403_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5404 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u5405_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:190px;
  height:41px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(153, 153, 153, 1);
  border-radius:282px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#999999;
}
#u5405 {
  border-width:0px;
  position:absolute;
  left:48px;
  top:773px;
  width:190px;
  height:41px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#999999;
}
#u5405 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5405_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5406_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:190px;
  height:41px;
  background:inherit;
  background-color:rgba(18, 150, 219, 1);
  border:none;
  border-radius:282px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#FFFFFF;
}
#u5406 {
  border-width:0px;
  position:absolute;
  left:262px;
  top:773px;
  width:190px;
  height:41px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#FFFFFF;
}
#u5406 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5406_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5407_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:470px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u5407 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:686px;
  width:470px;
  height:50px;
  display:flex;
}
#u5407 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5407_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5408_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:447px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#999999;
}
#u5408 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:696px;
  width:447px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#999999;
}
#u5408 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5408_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5409_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:200px;
}
#u5409 {
  border-width:0px;
  position:absolute;
  left:148px;
  top:448px;
  width:200px;
  height:200px;
  display:flex;
}
#u5409 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5409_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5410 {
  border-width:0px;
  position:absolute;
  left:125px;
  top:479px;
  width:250px;
  height:180px;
  visibility:hidden;
}
#u5410_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:250px;
  height:180px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u5410_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u5411_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:250px;
  height:180px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u5411 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:250px;
  height:180px;
  display:flex;
}
#u5411 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5411_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5412_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:28px;
  text-align:center;
}
#u5412 {
  border-width:0px;
  position:absolute;
  left:218px;
  top:0px;
  width:23px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:28px;
  text-align:center;
}
#u5412 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5412_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5413_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:251px;
  height:2px;
}
#u5413 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:60px;
  width:250px;
  height:1px;
  display:flex;
}
#u5413 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5413_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5414_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:165px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  text-align:center;
}
#u5414 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:27px;
  width:165px;
  height:21px;
  display:flex;
  font-size:18px;
  text-align:center;
}
#u5414 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5414_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5415_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:251px;
  height:2px;
}
#u5415 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:120px;
  width:250px;
  height:1px;
  display:flex;
}
#u5415 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5415_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5416_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  text-align:center;
}
#u5416 {
  border-width:0px;
  position:absolute;
  left:43px;
  top:80px;
  width:150px;
  height:21px;
  display:flex;
  font-size:18px;
  text-align:center;
}
#u5416 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5416_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5417_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:35px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u5417 {
  border-width:0px;
  position:absolute;
  left:53px;
  top:131px;
  width:140px;
  height:35px;
  display:flex;
  font-size:18px;
}
#u5417 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5417_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
