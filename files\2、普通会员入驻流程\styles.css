﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-42px;
  width:1078px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u6432 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u6433 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u6434_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:257px;
  height:527px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6434 {
  border-width:0px;
  position:absolute;
  left:91px;
  top:114px;
  width:257px;
  height:527px;
  display:flex;
}
#u6434 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6434_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6435_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:257px;
  height:527px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6435 {
  border-width:0px;
  position:absolute;
  left:348px;
  top:114px;
  width:257px;
  height:527px;
  display:flex;
}
#u6435 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6435_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6436_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:257px;
  height:527px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6436 {
  border-width:0px;
  position:absolute;
  left:606px;
  top:114px;
  width:257px;
  height:527px;
  display:flex;
}
#u6436 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6436_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6437_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:257px;
  height:527px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6437 {
  border-width:0px;
  position:absolute;
  left:863px;
  top:114px;
  width:257px;
  height:527px;
  display:flex;
}
#u6437 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6437_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6438_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:121px;
  height:49px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u6438 {
  border-width:0px;
  position:absolute;
  left:160px;
  top:124px;
  width:121px;
  height:49px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u6438 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u6438_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6439_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:101px;
  height:49px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u6439 {
  border-width:0px;
  position:absolute;
  left:417px;
  top:124px;
  width:101px;
  height:49px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u6439 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u6439_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6440_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:175px;
  height:49px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u6440 {
  border-width:0px;
  position:absolute;
  left:647px;
  top:124px;
  width:175px;
  height:49px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u6440 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u6440_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6441_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:49px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u6441 {
  border-width:0px;
  position:absolute;
  left:911px;
  top:124px;
  width:162px;
  height:49px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u6441 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u6441_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6442_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:362px;
  height:23px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
}
#u6442 {
  border-width:0px;
  position:absolute;
  left:42px;
  top:83px;
  width:362px;
  height:23px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
}
#u6442 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u6442_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6443_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:41px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6443 {
  border-width:0px;
  position:absolute;
  left:108px;
  top:196px;
  width:97px;
  height:41px;
  display:flex;
}
#u6443 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6443_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6444_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:41px;
  background:inherit;
  background-color:rgba(128, 128, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6444 {
  border-width:0px;
  position:absolute;
  left:406px;
  top:196px;
  width:186px;
  height:41px;
  display:flex;
}
#u6444 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6444_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6445_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:144px;
  height:49px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6445 {
  border-width:0px;
  position:absolute;
  left:945px;
  top:410px;
  width:144px;
  height:49px;
  display:flex;
}
#u6445 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6445_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6446_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:144px;
  height:47px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6446 {
  border-width:0px;
  position:absolute;
  left:945px;
  top:545px;
  width:144px;
  height:47px;
  display:flex;
}
#u6446 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6446_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6447_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:43px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6447 {
  border-width:0px;
  position:absolute;
  left:235px;
  top:310px;
  width:97px;
  height:43px;
  display:flex;
}
#u6447 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6447_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6448_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:43px;
  background:inherit;
  background-color:rgba(128, 128, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6448 {
  border-width:0px;
  position:absolute;
  left:406px;
  top:310px;
  width:186px;
  height:43px;
  display:flex;
}
#u6448 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6448_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6449_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:49px;
  background:inherit;
  background-color:rgba(128, 128, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6449 {
  border-width:0px;
  position:absolute;
  left:406px;
  top:410px;
  width:186px;
  height:49px;
  display:flex;
}
#u6449 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6449_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6450_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:97px;
  height:47px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6450 {
  border-width:0px;
  position:absolute;
  left:211px;
  top:545px;
  width:97px;
  height:47px;
  display:flex;
}
#u6450 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6450_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6451_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:186px;
  height:47px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6451 {
  border-width:0px;
  position:absolute;
  left:406px;
  top:545px;
  width:186px;
  height:47px;
  display:flex;
}
#u6451 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6451_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6452 {
  border-width:0px;
  position:absolute;
  left:205px;
  top:217px;
  width:0px;
  height:0px;
}
#u6452_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:206px;
  height:10px;
}
#u6452_seg1 {
  border-width:0px;
  position:absolute;
  left:189px;
  top:-10px;
  width:20px;
  height:20px;
}
#u6452_text {
  border-width:0px;
  position:absolute;
  left:50px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6453 {
  border-width:0px;
  position:absolute;
  left:157px;
  top:237px;
  width:0px;
  height:0px;
}
#u6453_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:100px;
}
#u6453_seg1 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:90px;
  width:83px;
  height:10px;
}
#u6453_seg2 {
  border-width:0px;
  position:absolute;
  left:66px;
  top:85px;
  width:20px;
  height:20px;
}
#u6453_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:78px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6454 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:332px;
  width:0px;
  height:0px;
}
#u6454_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:79px;
  height:10px;
}
#u6454_seg1 {
  border-width:0px;
  position:absolute;
  left:62px;
  top:-10px;
  width:20px;
  height:20px;
}
#u6454_text {
  border-width:0px;
  position:absolute;
  left:-13px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6455 {
  border-width:0px;
  position:absolute;
  left:499px;
  top:353px;
  width:0px;
  height:0px;
}
#u6455_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:62px;
}
#u6455_seg1 {
  border-width:0px;
  position:absolute;
  left:-10px;
  top:45px;
  width:20px;
  height:20px;
}
#u6455_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:20px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6456 {
  border-width:0px;
  position:absolute;
  left:592px;
  top:435px;
  width:0px;
  height:0px;
}
#u6456_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:358px;
  height:10px;
}
#u6456_seg1 {
  border-width:0px;
  position:absolute;
  left:341px;
  top:-10px;
  width:20px;
  height:20px;
}
#u6456_text {
  border-width:0px;
  position:absolute;
  left:126px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6457 {
  border-width:0px;
  position:absolute;
  left:1017px;
  top:459px;
  width:0px;
  height:0px;
}
#u6457_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:91px;
}
#u6457_seg1 {
  border-width:0px;
  position:absolute;
  left:-10px;
  top:74px;
  width:20px;
  height:20px;
}
#u6457_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:35px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6458 {
  border-width:0px;
  position:absolute;
  left:945px;
  top:569px;
  width:0px;
  height:0px;
}
#u6458_seg0 {
  border-width:0px;
  position:absolute;
  left:-353px;
  top:-5px;
  width:358px;
  height:10px;
}
#u6458_seg1 {
  border-width:0px;
  position:absolute;
  left:-361px;
  top:-10px;
  width:20px;
  height:20px;
}
#u6458_text {
  border-width:0px;
  position:absolute;
  left:-226px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6459 {
  border-width:0px;
  position:absolute;
  left:406px;
  top:569px;
  width:0px;
  height:0px;
}
#u6459_seg0 {
  border-width:0px;
  position:absolute;
  left:-98px;
  top:-5px;
  width:103px;
  height:10px;
}
#u6459_seg1 {
  border-width:0px;
  position:absolute;
  left:-106px;
  top:-10px;
  width:20px;
  height:20px;
}
#u6459_text {
  border-width:0px;
  position:absolute;
  left:-99px;
  top:-11px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
