﻿<!DOCTYPE html>
<html>
  <head>
    <title>子钱包充值(F510\F507)</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/子钱包充值_f510_f507_/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/子钱包充值_f510_f507_/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (基础app框架(H5)) -->

      <!-- Unnamed (矩形) -->
      <div id="u581" class="ax_default box_1">
        <div id="u581_div" class=""></div>
        <div id="u581_text" class="text ">
          <p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u582" class="ax_default _二级标题">
        <div id="u582_div" class=""></div>
        <div id="u582_text" class="text ">
          <p><span>16:18</span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u583" class="ax_default icon">
        <img id="u583_img" class="img " src="images/海融宝签约_个人__f501_f502_/u3.svg"/>
        <div id="u583_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u584" class="ax_default icon">
        <img id="u584_img" class="img " src="images/海融宝签约_个人__f501_f502_/u4.svg"/>
        <div id="u584_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u585" class="ax_default _文本段落">
        <div id="u585_div" class=""></div>
        <div id="u585_text" class="text ">
          <p><span>充值</span></p>
        </div>
      </div>

      <!-- 操作状态 (动态面板) -->
      <div id="u586" class="ax_default ax_default_hidden" data-label="操作状态" style="display:none; visibility: hidden">
        <div id="u586_state0" class="panel_state" data-label="操作成功" style="">
          <div id="u586_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u587" class="ax_default box_3">
              <div id="u587_div" class=""></div>
              <div id="u587_text" class="text ">
                <p><span>操作成功！</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u586_state1" class="panel_state" data-label="操作失败" style="visibility: hidden;">
          <div id="u586_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u588" class="ax_default box_3">
              <div id="u588_div" class=""></div>
              <div id="u588_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u589" class="ax_default _文本段落">
              <div id="u589_div" class=""></div>
              <div id="u589_text" class="text ">
                <p><span>操作失败！</span></p>
              </div>
            </div>

            <!-- Unnamed (图片 ) -->
            <div id="u590" class="ax_default _图片_">
              <img id="u590_img" class="img " src="images/海融宝签约_个人__f501_f502_/u10.png"/>
              <div id="u590_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u591" class="ax_default _图片_">
        <img id="u591_img" class="img " src="images/海融宝签约_个人__f501_f502_/u11.png"/>
        <div id="u591_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u592" class="ax_default icon">
        <img id="u592_img" class="img " src="images/海融宝签约_个人__f501_f502_/u12.svg"/>
        <div id="u592_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u593" class="ax_default _文本段落">
        <div id="u593_div" class=""></div>
        <div id="u593_text" class="text ">
          <p><span>hrypt.com</span></p>
        </div>
      </div>
      <div id="u580" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (矩形) -->
      <div id="u594" class="ax_default primary_button">
        <div id="u594_div" class=""></div>
        <div id="u594_text" class="text ">
          <p><span>提交充值</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u595" class="ax_default _文本段落">
        <div id="u595_div" class=""></div>
        <div id="u595_text" class="text ">
          <p><span>说明：</span></p><p><span><br></span></p><p><span>当前充值方式写死，只有邮储的母账户</span></p><p><span><br></span></p><p><span>充值方式对接后可以在海融宝账户操作</span></p><p><span><br></span></p><p><span>1、余额不足，重新选择充值方式，选择其他银行也是数字人民币账户</span></p><p><span>2、提交充值，跳转邮储数字人民里输入密码</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u596" class="ax_default _形状">
        <div id="u596_div" class=""></div>
        <div id="u596_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u597" class="ax_default" data-left="85" data-top="180" data-width="317" data-height="36">

        <!-- Unnamed (文本框) -->
        <div id="u598" class="ax_default text_field">
          <div id="u598_div" class=""></div>
          <input id="u598_input" type="text" value="" class="u598_input"/>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u599" class="ax_default _文本段落">
          <div id="u599_div" class=""></div>
          <div id="u599_text" class="text ">
            <p><span>请输入充值金额</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u600" class="ax_default _文本段落">
        <div id="u600_div" class=""></div>
        <div id="u600_text" class="text ">
          <p><span>充值金额</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u601" class="ax_default _文本段落">
        <div id="u601_div" class=""></div>
        <div id="u601_text" class="text ">
          <p><span>￥</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u602" class="ax_default _文本段落">
        <div id="u602_div" class=""></div>
        <div id="u602_text" class="text ">
          <p><span>数字人民币合约子钱包用户ID</span></p><p><span>3100 1234 9876 8888</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u603" class="ax_default _文本段落">
        <div id="u603_div" class=""></div>
        <div id="u603_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u604" class="ax_default _文本段落">
        <div id="u604_div" class=""></div>
        <div id="u604_text" class="text ">
          <p><span>说明：充值资金通过您的邮储银行数字人民币钱包或其绑定的银行卡转入您的合约子钱包。</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u605" class="ax_default _文本段落">
        <div id="u605_div" class=""></div>
        <div id="u605_text" class="text ">
          <p style="font-size:20px;"><span style="font-family:'PingFang SC ', 'PingFang SC';color:#000000;">F510入金创单</span></p><p style="font-size:13px;"><span style="font-family:'Arial Normal', 'Arial';"><br></span></p><p style="font-size:13px;"><span style="font-family:'Arial Normal', 'Arial';">节点属性&nbsp; &nbsp;&nbsp; 中文描述&nbsp; &nbsp;&nbsp; 数据类型&nbsp; &nbsp;&nbsp; 必输&nbsp; &nbsp;&nbsp; 说明</span></p><p style="font-size:13px;"><span style="font-family:'Arial Normal', 'Arial';">platOrderNo&nbsp; &nbsp;&nbsp; 平台入金订单号&nbsp; &nbsp;&nbsp; char(100)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 平台生成的唯一标识,银企客户号+yyyyMMdd+序号</span></p><p style="font-size:13px;"><span style="font-family:'Arial Normal', 'Arial';">custId&nbsp; &nbsp;&nbsp; 客户id&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 个人会员签约时生成</span></p><p style="font-size:13px;"><span style="font-family:'Arial Normal', 'Arial';">signAgrNo&nbsp; &nbsp;&nbsp; 签约协议号&nbsp; &nbsp;&nbsp; char(64)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 个人会员签约时生成</span></p><p style="font-size:13px;"><span style="font-family:'Arial Normal', 'Arial';">txAmt&nbsp; &nbsp;&nbsp; 交易金额&nbsp; &nbsp;&nbsp; char(22)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 保留两位小数</span></p><p style="font-size:13px;"><span style="font-family:'Arial Normal', 'Arial';">txCurr&nbsp; &nbsp;&nbsp; 交易币种&nbsp; &nbsp;&nbsp; char(3)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 人民币CNY</span></p><p style="font-size:13px;"><span style="font-family:'Arial Normal', 'Arial';">merchantId&nbsp; &nbsp; &nbsp; 合作方编号（商户号）&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 服开商户号，由分行老师提供</span></p><p style="font-size:13px;"><span style="font-family:'Arial Normal', 'Arial';">appID&nbsp; &nbsp; &nbsp; 应用ID&nbsp; &nbsp;&nbsp; char(30)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 商户在服开平台申请的合约参数 ，由分行老师提供</span></p><p style="font-size:13px;"><span style="font-family:'Arial Normal', 'Arial';">redirectUrl&nbsp; &nbsp;&nbsp; 回调的业务方地址&nbsp; &nbsp;&nbsp; char(256)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; 流程结束后跳转地址</span></p><p style="font-size:13px;"><span style="font-family:'Arial Normal', 'Arial';"><br></span></p><p style="font-size:13px;"><span style="font-family:'Arial Normal', 'Arial';">节点属性&nbsp; &nbsp;&nbsp; 中文描述&nbsp; &nbsp;&nbsp; 数据类型&nbsp; &nbsp;&nbsp; 必输&nbsp; &nbsp;&nbsp; 说明</span></p><p style="font-size:13px;"><span style="font-family:'Arial Normal', 'Arial';">userLgnUrl&nbsp; &nbsp;&nbsp; 用户登录URL&nbsp; &nbsp;&nbsp; Char(500)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 入金创单页面URL</span></p><p style="font-size:13px;"><span style="font-family:'Arial Normal', 'Arial';"><br></span></p><p style="font-size:13px;"><span style="font-family:'Arial Normal', 'Arial';"><br></span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u606" class="ax_default primary_button">
        <div id="u606_div" class=""></div>
        <div id="u606_text" class="text ">
          <p><span>充值未完成，继续充值</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u607" class="ax_default primary_button">
        <div id="u607_div" class=""></div>
        <div id="u607_text" class="text ">
          <p><span>业务取消，关闭订单</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u608" class="ax_default _文本段落">
        <div id="u608_div" class=""></div>
        <div id="u608_text" class="text ">
          <p style="font-size:20px;"><span>F507 入金订单查询</span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span>节点属性&nbsp; &nbsp;&nbsp; 中文描述&nbsp; &nbsp;&nbsp; 数据类型&nbsp; &nbsp;&nbsp; 必输&nbsp; &nbsp;&nbsp; 说明</span></p><p style="font-size:13px;"><span>platOrderNo&nbsp; &nbsp;&nbsp; 平台订单号&nbsp; &nbsp;&nbsp; char(100)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 出入金交易的平台订单号</span></p><p style="font-size:13px;"><span>custId&nbsp; &nbsp;&nbsp; 客户Id&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 出入金收付方都是同一个客户ID</span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span>节点属性&nbsp; &nbsp;&nbsp; 中文描述&nbsp; &nbsp;&nbsp; 数据类型&nbsp; &nbsp;&nbsp; 必输&nbsp; &nbsp;&nbsp; 说明</span></p><p style="font-size:13px;"><span>pwltId&nbsp; &nbsp;&nbsp; 母钱包id&nbsp; &nbsp;&nbsp; char(16)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; </span></p><p style="font-size:13px;"><span>pwltName&nbsp; &nbsp;&nbsp; 母钱包名称&nbsp; &nbsp;&nbsp; char(70)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; </span></p><p style="font-size:13px;"><span>txCurrCd&nbsp; &nbsp;&nbsp; 交易币种代码&nbsp; &nbsp;&nbsp; char(3)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 人民币CNY</span></p><p style="font-size:13px;"><span>txAmt&nbsp; &nbsp;&nbsp; 交易金额&nbsp; &nbsp;&nbsp; char(22)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; </span></p><p style="font-size:13px;"><span>frzAgrNo&nbsp; &nbsp;&nbsp; 冻结协议号&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; 入金并冻结时返回</span></p><p style="font-size:13px;"><span>txOrderNo&nbsp; &nbsp;&nbsp; 交易订单号&nbsp; &nbsp;&nbsp; char(100)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; </span></p><p style="font-size:13px;"><span>txRemark&nbsp; &nbsp;&nbsp; 交易备注&nbsp; &nbsp;&nbsp; char(256)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; </span></p><p style="font-size:13px;"><span>accblStmOrderStaCd&nbsp; &nbsp;&nbsp; 账单结算订单状态代码&nbsp; &nbsp;&nbsp; char(2)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 01-待结算，02-结算成功，03-结算失败，04-已关闭，05-订单处理中</span></p><p style="font-size:13px;"><span>failReason&nbsp; &nbsp;&nbsp; 失败原因&nbsp; &nbsp;&nbsp; char(500)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; 账单结算订单状态代码为03-结算失败时返回</span></p><p style="font-size:13px;"><span>platfMerOrderNo&nbsp; &nbsp;&nbsp; 平台商户出入金订单号&nbsp; &nbsp;&nbsp; char(100)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; </span></p><p style="font-size:13px;"><span>pwltPayAmt&nbsp; &nbsp;&nbsp; 付款方母钱支付包金额&nbsp; &nbsp;&nbsp; char(22)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; 查询入金信息时返回</span></p><p style="font-size:13px;"><span>bkcdPayAmt&nbsp; &nbsp;&nbsp; 付款方银行卡支付金额&nbsp; &nbsp;&nbsp; char(22)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; 查询入金信息时返回</span></p><p style="font-size:13px;"><span>bkcdName&nbsp; &nbsp;&nbsp; 付款方银行卡名称&nbsp; &nbsp;&nbsp; char(60)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; 查询入金信息时返回</span></p><p style="font-size:13px;"><span>bkcdNo&nbsp; &nbsp;&nbsp; 付款方银行卡卡号&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; 查询入金信息时返回</span></p><p style="font-size:13px;"><span>txTime&nbsp; &nbsp;&nbsp; 交易时间&nbsp; &nbsp;&nbsp; char(14)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; yyyyMMddHHmmss</span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span><br></span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
