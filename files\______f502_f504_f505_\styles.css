﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:1205px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u510_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:900px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:50px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u510 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:900px;
  display:flex;
  opacity:0.49;
}
#u510 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u510_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u511_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u511 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:20px;
  width:51px;
  height:40px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u511 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u511_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u512_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:18px;
}
#u512 {
  border-width:0px;
  position:absolute;
  left:425px;
  top:19px;
  width:23px;
  height:18px;
  display:flex;
}
#u512 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u512_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u513_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:16px;
}
#u513 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:21px;
  width:26px;
  height:16px;
  display:flex;
}
#u513 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u513_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u514_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u514 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:50px;
  width:300px;
  height:25px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u514 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u514_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u515 {
  position:fixed;
  left:50%;
  margin-left:-75px;
  top:50%;
  margin-top:-25px;
  visibility:hidden;
}
#u515_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u515_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u516_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u516 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u516 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u516_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u515_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u515_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u517_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u517 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u517 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u517_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u518_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u518 {
  border-width:0px;
  position:absolute;
  left:60px;
  top:16px;
  width:80px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u518 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u518_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u519_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u519 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:10px;
  width:30px;
  height:30px;
  display:flex;
}
#u519 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u519_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u520_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u520 {
  border-width:0px;
  position:absolute;
  left:463px;
  top:50px;
  width:25px;
  height:25px;
  display:flex;
}
#u520 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u520_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u521_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:25px;
}
#u521 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:50px;
  width:15px;
  height:25px;
  display:flex;
}
#u521 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u521_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u522_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:228px;
  height:11px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  text-align:center;
}
#u522 {
  border-width:0px;
  position:absolute;
  left:136px;
  top:71px;
  width:228px;
  height:11px;
  display:flex;
  font-size:10px;
  text-align:center;
}
#u522 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u522_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u523_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:439px;
  height:50px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u523 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:786px;
  width:439px;
  height:50px;
  display:flex;
  font-size:18px;
}
#u523 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u523_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u524_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:460px;
  height:132px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u524 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:107px;
  width:460px;
  height:132px;
  display:flex;
  font-size:16px;
}
#u524 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u524_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u525 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u526_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:460px;
  height:177px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u526 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:294px;
  width:460px;
  height:177px;
  display:flex;
  font-size:16px;
}
#u526 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u526_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u527_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:225px;
  height:33px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  color:#000000;
}
#u527 {
  border-width:0px;
  position:absolute;
  left:33px;
  top:301px;
  width:225px;
  height:33px;
  display:flex;
  font-size:20px;
  color:#000000;
}
#u527 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u527_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u528_input {
  position:absolute;
  left:0px;
  top:0px;
  width:395px;
  height:106px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#7F7F7F;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
  resize:none;
}
#u528_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:395px;
  height:106px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#7F7F7F;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
  resize:none;
}
#u528_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:395px;
  height:106px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#7F7F7F;
}
#u528 {
  border-width:0px;
  position:absolute;
  left:62px;
  top:334px;
  width:395px;
  height:106px;
  display:flex;
  color:#7F7F7F;
}
#u528 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u528_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:395px;
  height:106px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#7F7F7F;
}
#u528.disabled {
}
#u529_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:351px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u529 {
  border-width:0px;
  position:absolute;
  left:537px;
  top:0px;
  width:351px;
  height:32px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u529 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u529_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u530_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:216px;
  height:31px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:28px;
  color:#000000;
  text-align:center;
}
#u530 {
  border-width:0px;
  position:absolute;
  left:130px;
  top:158px;
  width:216px;
  height:31px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:28px;
  color:#000000;
  text-align:center;
}
#u530 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u530_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u531_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:210px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u531 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:209px;
  width:210px;
  height:22px;
  display:flex;
}
#u531 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u531_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u532_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:207px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:right;
}
#u532 {
  border-width:0px;
  position:absolute;
  left:264px;
  top:209px;
  width:207px;
  height:22px;
  display:flex;
  text-align:right;
}
#u532 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u532_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u533_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:36px;
  background:inherit;
  background-color:rgba(18, 150, 219, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(201, 201, 201, 1);
  border-radius:58px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#FFFFFF;
}
#u533 {
  border-width:0px;
  position:absolute;
  left:376px;
  top:111px;
  width:99px;
  height:36px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#FFFFFF;
}
#u533 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u533_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u534_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:437px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Nunito Sans';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#D9001B;
}
#u534 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:242px;
  width:437px;
  height:16px;
  display:flex;
  font-family:'Nunito Sans';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#D9001B;
}
#u534 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u534_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u535_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:635px;
  height:188px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u535 {
  border-width:0px;
  position:absolute;
  left:570px;
  top:418px;
  width:635px;
  height:188px;
  display:flex;
}
#u535 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u535_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u536_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:591px;
  height:188px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u536 {
  border-width:0px;
  position:absolute;
  left:570px;
  top:623px;
  width:591px;
  height:188px;
  display:flex;
}
#u536 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u536_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u537 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u538 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u539_input {
  position:absolute;
  left:0px;
  top:0px;
  width:317px;
  height:36px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u539_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:317px;
  height:36px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u539_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:317px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u539 {
  border-width:0px;
  position:absolute;
  left:147px;
  top:514px;
  width:317px;
  height:36px;
  display:flex;
}
#u539 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u539_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:317px;
  height:36px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u539.disabled {
}
#u540 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u541 {
  border-width:0px;
  position:absolute;
  left:342px;
  top:518px;
  width:110px;
  height:30px;
}
#u541_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u541_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u542_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:111px;
  height:30px;
  background:inherit;
  background-color:rgba(194, 128, 255, 1);
  border:none;
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#000000;
}
#u542 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:111px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#000000;
}
#u542 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u542_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u541_state1 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u541_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u543_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(128, 128, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u543 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u543 .text {
  position:absolute;
  align-self:center;
  padding:2px 20px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u543_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u544_input {
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u544_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u544_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:center;
}
#u544 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:0px;
  width:60px;
  height:30px;
  display:flex;
  font-size:16px;
  text-align:center;
}
#u544 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u544_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:center;
}
#u544.disabled {
}
#u545_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#7F7F7F;
}
#u545 {
  border-width:0px;
  position:absolute;
  left:158px;
  top:518px;
  width:160px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#7F7F7F;
}
#u545 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u545_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u546_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:107px;
  height:33px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  color:#AAAAAA;
}
#u546 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:515px;
  width:107px;
  height:33px;
  display:flex;
  font-size:20px;
  color:#AAAAAA;
}
#u546 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u546_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u547_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:608px;
  height:278px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u547 {
  border-width:0px;
  position:absolute;
  left:570px;
  top:81px;
  width:608px;
  height:278px;
  display:flex;
}
#u547 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u547_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u548_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:351px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u548 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:111px;
  width:351px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u548 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u548_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
