﻿<!DOCTYPE html>
<html>
  <head>
    <title>选择省信息</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/选择省信息/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/选择省信息/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (基础app框架(H5)) -->

      <!-- Unnamed (矩形) -->
      <div id="u5955" class="ax_default box_1">
        <div id="u5955_div" class=""></div>
        <div id="u5955_text" class="text ">
          <p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5956" class="ax_default _二级标题">
        <div id="u5956_div" class=""></div>
        <div id="u5956_text" class="text ">
          <p><span>16:18</span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u5957" class="ax_default icon">
        <img id="u5957_img" class="img " src="images/海融宝签约_个人__f501_f502_/u3.svg"/>
        <div id="u5957_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u5958" class="ax_default icon">
        <img id="u5958_img" class="img " src="images/海融宝签约_个人__f501_f502_/u4.svg"/>
        <div id="u5958_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5959" class="ax_default _文本段落">
        <div id="u5959_div" class=""></div>
        <div id="u5959_text" class="text ">
          <p><span>选择省信息</span></p>
        </div>
      </div>

      <!-- 操作状态 (动态面板) -->
      <div id="u5960" class="ax_default ax_default_hidden" data-label="操作状态" style="display:none; visibility: hidden">
        <div id="u5960_state0" class="panel_state" data-label="操作成功" style="">
          <div id="u5960_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u5961" class="ax_default box_3">
              <div id="u5961_div" class=""></div>
              <div id="u5961_text" class="text ">
                <p><span>操作成功！</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u5960_state1" class="panel_state" data-label="操作失败" style="visibility: hidden;">
          <div id="u5960_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u5962" class="ax_default box_3">
              <div id="u5962_div" class=""></div>
              <div id="u5962_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5963" class="ax_default _文本段落">
              <div id="u5963_div" class=""></div>
              <div id="u5963_text" class="text ">
                <p><span>操作失败！</span></p>
              </div>
            </div>

            <!-- Unnamed (图片 ) -->
            <div id="u5964" class="ax_default _图片_">
              <img id="u5964_img" class="img " src="images/海融宝签约_个人__f501_f502_/u10.png"/>
              <div id="u5964_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u5965" class="ax_default _图片_">
        <img id="u5965_img" class="img " src="images/海融宝签约_个人__f501_f502_/u11.png"/>
        <div id="u5965_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u5966" class="ax_default icon">
        <img id="u5966_img" class="img " src="images/海融宝签约_个人__f501_f502_/u12.svg"/>
        <div id="u5966_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5967" class="ax_default _文本段落">
        <div id="u5967_div" class=""></div>
        <div id="u5967_text" class="text ">
          <p><span>hrypt.com</span></p>
        </div>
      </div>
      <div id="u5954" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (矩形) -->
      <div id="u5968" class="ax_default box_1">
        <div id="u5968_div" class=""></div>
        <div id="u5968_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5969" class="ax_default box_1">
        <div id="u5969_div" class=""></div>
        <div id="u5969_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5970" class="ax_default box_1">
        <div id="u5970_div" class=""></div>
        <div id="u5970_text" class="text ">
          <p><span>福建</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5971" class="ax_default box_1">
        <div id="u5971_div" class=""></div>
        <div id="u5971_text" class="text ">
          <p><span>天津</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5972" class="ax_default box_1">
        <div id="u5972_div" class=""></div>
        <div id="u5972_text" class="text ">
          <p><span>河北</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5973" class="ax_default box_1">
        <div id="u5973_div" class=""></div>
        <div id="u5973_text" class="text ">
          <p><span>山西</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5974" class="ax_default box_1">
        <div id="u5974_div" class=""></div>
        <div id="u5974_text" class="text ">
          <p><span>内蒙古</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5975" class="ax_default box_1">
        <div id="u5975_div" class=""></div>
        <div id="u5975_text" class="text ">
          <p><span>辽宁</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5976" class="ax_default box_1">
        <div id="u5976_div" class=""></div>
        <div id="u5976_text" class="text ">
          <p><span>吉林</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5977" class="ax_default box_1">
        <div id="u5977_div" class=""></div>
        <div id="u5977_text" class="text ">
          <p><span>黑龙江</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5978" class="ax_default box_1">
        <div id="u5978_div" class=""></div>
        <div id="u5978_text" class="text ">
          <p><span>上海</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5979" class="ax_default box_1">
        <div id="u5979_div" class=""></div>
        <div id="u5979_text" class="text ">
          <p><span>北京</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5980" class="ax_default box_1">
        <div id="u5980_div" class=""></div>
        <div id="u5980_text" class="text ">
          <p><span>浙江</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5981" class="ax_default box_1">
        <div id="u5981_div" class=""></div>
        <div id="u5981_text" class="text ">
          <p><span>河南</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5982" class="ax_default box_1">
        <div id="u5982_div" class=""></div>
        <div id="u5982_text" class="text ">
          <p><span>安徽</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5983" class="ax_default box_1">
        <div id="u5983_div" class=""></div>
        <div id="u5983_text" class="text ">
          <p><span>江西</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5984" class="ax_default box_1">
        <div id="u5984_div" class=""></div>
        <div id="u5984_text" class="text ">
          <p><span>山东</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u5985" class="ax_default" data-left="64" data-top="628" data-width="381" data-height="40">

        <!-- Unnamed (矩形) -->
        <div id="u5986" class="ax_default box_1">
          <div id="u5986_div" class=""></div>
          <div id="u5986_text" class="text ">
            <p><span>取消</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u5987" class="ax_default box_1">
          <div id="u5987_div" class=""></div>
          <div id="u5987_text" class="text ">
            <p><span>确定</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5988" class="ax_default box_1">
        <div id="u5988_div" class=""></div>
        <div id="u5988_text" class="text ">
          <p><span>江苏</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5989" class="ax_default box_1">
        <div id="u5989_div" class=""></div>
        <div id="u5989_text" class="text ">
          <p><span>湖南</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5990" class="ax_default box_1">
        <div id="u5990_div" class=""></div>
        <div id="u5990_text" class="text ">
          <p><span>广东</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5991" class="ax_default box_1">
        <div id="u5991_div" class=""></div>
        <div id="u5991_text" class="text ">
          <p><span>广西</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5992" class="ax_default box_1">
        <div id="u5992_div" class=""></div>
        <div id="u5992_text" class="text ">
          <p><span>海南</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5993" class="ax_default box_1">
        <div id="u5993_div" class=""></div>
        <div id="u5993_text" class="text ">
          <p><span>重庆</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5994" class="ax_default box_1">
        <div id="u5994_div" class=""></div>
        <div id="u5994_text" class="text ">
          <p><span>四川</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5995" class="ax_default box_1">
        <div id="u5995_div" class=""></div>
        <div id="u5995_text" class="text ">
          <p><span>贵州</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5996" class="ax_default box_1">
        <div id="u5996_div" class=""></div>
        <div id="u5996_text" class="text ">
          <p><span>云南</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5997" class="ax_default box_1">
        <div id="u5997_div" class=""></div>
        <div id="u5997_text" class="text ">
          <p><span>西藏</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5998" class="ax_default box_1">
        <div id="u5998_div" class=""></div>
        <div id="u5998_text" class="text ">
          <p><span>陕西</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5999" class="ax_default box_1">
        <div id="u5999_div" class=""></div>
        <div id="u5999_text" class="text ">
          <p><span>甘肃</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u6000" class="ax_default box_1">
        <div id="u6000_div" class=""></div>
        <div id="u6000_text" class="text ">
          <p><span>青海</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u6001" class="ax_default box_1">
        <div id="u6001_div" class=""></div>
        <div id="u6001_text" class="text ">
          <p><span>宁夏</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u6002" class="ax_default box_1">
        <div id="u6002_div" class=""></div>
        <div id="u6002_text" class="text ">
          <p><span>新疆</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u6003" class="ax_default box_1">
        <div id="u6003_div" class=""></div>
        <div id="u6003_text" class="text ">
          <p><span>台湾</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u6004" class="ax_default box_1">
        <div id="u6004_div" class=""></div>
        <div id="u6004_text" class="text ">
          <p><span>湖北</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u6005" class="ax_default box_1">
        <div id="u6005_div" class=""></div>
        <div id="u6005_text" class="text ">
          <p><span>香港</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u6006" class="ax_default box_1">
        <div id="u6006_div" class=""></div>
        <div id="u6006_text" class="text ">
          <p><span>澳门</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u6007" class="ax_default" data-left="73" data-top="138" data-width="379" data-height="41">

        <!-- Unnamed (组合) -->
        <div id="u6008" class="ax_default" data-left="73" data-top="138" data-width="379" data-height="41">

          <!-- Unnamed (矩形) -->
          <div id="u6009" class="ax_default box_1">
            <div id="u6009_div" class=""></div>
            <div id="u6009_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (形状) -->
          <div id="u6010" class="ax_default icon">
            <img id="u6010_img" class="img " src="images/选择省信息/u6010.svg"/>
            <div id="u6010_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u6011" class="ax_default _一级标题">
            <div id="u6011_div" class=""></div>
            <div id="u6011_text" class="text ">
              <p><span>请输入省份/城市/市区搜索</span></p>
            </div>
          </div>

          <!-- Unnamed (图片 ) -->
          <div id="u6012" class="ax_default _图片">
            <img id="u6012_img" class="img " src="images/____________f502_f503____f506_f507_f508_f509_/u304.png"/>
            <div id="u6012_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u6013" class="ax_default line">
        <img id="u6013_img" class="img " src="images/选择省信息/u6013.svg"/>
        <div id="u6013_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u6014" class="ax_default _图片_">
        <img id="u6014_img" class="img " src="images/充值方式/u1461.png"/>
        <div id="u6014_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
