﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:510px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u2575_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:900px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:50px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2575 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:900px;
  display:flex;
  opacity:0.49;
}
#u2575 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2575_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2576_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u2576 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:20px;
  width:51px;
  height:40px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u2576 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2576_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2577_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:18px;
}
#u2577 {
  border-width:0px;
  position:absolute;
  left:425px;
  top:19px;
  width:23px;
  height:18px;
  display:flex;
}
#u2577 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2577_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2578_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:16px;
}
#u2578 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:21px;
  width:26px;
  height:16px;
  display:flex;
}
#u2578 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2578_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2579_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u2579 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:50px;
  width:300px;
  height:25px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u2579 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2579_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2580 {
  position:fixed;
  left:50%;
  margin-left:-75px;
  top:50%;
  margin-top:-25px;
  visibility:hidden;
}
#u2580_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2580_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u2581_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u2581 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u2581 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2581_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2580_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u2580_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u2582_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u2582 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u2582 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2582_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2583_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u2583 {
  border-width:0px;
  position:absolute;
  left:60px;
  top:16px;
  width:80px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u2583 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2583_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2584_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u2584 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:10px;
  width:30px;
  height:30px;
  display:flex;
}
#u2584 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2584_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2585_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u2585 {
  border-width:0px;
  position:absolute;
  left:463px;
  top:50px;
  width:25px;
  height:25px;
  display:flex;
}
#u2585 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2585_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2586_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:25px;
}
#u2586 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:50px;
  width:15px;
  height:25px;
  display:flex;
}
#u2586 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2586_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2587_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:228px;
  height:11px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  text-align:center;
}
#u2587 {
  border-width:0px;
  position:absolute;
  left:136px;
  top:71px;
  width:228px;
  height:11px;
  display:flex;
  font-size:10px;
  text-align:center;
}
#u2587 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2587_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2588_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:486px;
  height:241px;
  background:inherit;
  background-color:rgba(255, 204, 255, 1);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#AEAEAE;
  text-align:left;
}
#u2588 {
  border-width:0px;
  position:absolute;
  left:12px;
  top:119px;
  width:486px;
  height:241px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#AEAEAE;
  text-align:left;
}
#u2588 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2588_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2589 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2590_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:356px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#8400FF;
}
#u2590 {
  border-width:0px;
  position:absolute;
  left:133px;
  top:142px;
  width:356px;
  height:28px;
  display:flex;
  font-size:16px;
  color:#8400FF;
}
#u2590 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2590_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2591_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:102px;
  height:33px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:right;
}
#u2591 {
  border-width:0px;
  position:absolute;
  left:31px;
  top:142px;
  width:102px;
  height:33px;
  display:flex;
  font-size:16px;
  text-align:right;
}
#u2591 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2591_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2592_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:17px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u2592 {
  border-width:0px;
  position:absolute;
  left:432px;
  top:148px;
  width:45px;
  height:17px;
  display:flex;
  font-size:12px;
}
#u2592 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2592_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2593 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2594_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:356px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#8400FF;
}
#u2594 {
  border-width:0px;
  position:absolute;
  left:133px;
  top:174px;
  width:356px;
  height:28px;
  display:flex;
  font-size:16px;
  color:#8400FF;
}
#u2594 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2594_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2595_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:102px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:right;
}
#u2595 {
  border-width:0px;
  position:absolute;
  left:31px;
  top:174px;
  width:102px;
  height:28px;
  display:flex;
  font-size:16px;
  text-align:right;
}
#u2595 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2595_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2596_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:17px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u2596 {
  border-width:0px;
  position:absolute;
  left:432px;
  top:181px;
  width:45px;
  height:17px;
  display:flex;
  font-size:12px;
}
#u2596 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2596_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2597 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2598_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:356px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#8400FF;
}
#u2598 {
  border-width:0px;
  position:absolute;
  left:133px;
  top:208px;
  width:356px;
  height:28px;
  display:flex;
  font-size:16px;
  color:#8400FF;
}
#u2598 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2598_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2599_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:102px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:right;
}
#u2599 {
  border-width:0px;
  position:absolute;
  left:31px;
  top:208px;
  width:102px;
  height:28px;
  display:flex;
  font-size:16px;
  text-align:right;
}
#u2599 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2599_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2600_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:17px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u2600 {
  border-width:0px;
  position:absolute;
  left:327px;
  top:215px;
  width:45px;
  height:17px;
  display:flex;
  font-size:12px;
}
#u2600 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2600_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2601 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2602_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:356px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#8400FF;
}
#u2602 {
  border-width:0px;
  position:absolute;
  left:133px;
  top:241px;
  width:356px;
  height:28px;
  display:flex;
  font-size:16px;
  color:#8400FF;
}
#u2602 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2602_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2603_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:102px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:right;
}
#u2603 {
  border-width:0px;
  position:absolute;
  left:31px;
  top:241px;
  width:102px;
  height:24px;
  display:flex;
  font-size:16px;
  text-align:right;
}
#u2603 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2603_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2604_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:17px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u2604 {
  border-width:0px;
  position:absolute;
  left:327px;
  top:247px;
  width:45px;
  height:17px;
  display:flex;
  font-size:12px;
}
#u2604 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2604_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2605 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2606_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:356px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#8400FF;
}
#u2606 {
  border-width:0px;
  position:absolute;
  left:133px;
  top:274px;
  width:356px;
  height:28px;
  display:flex;
  font-size:16px;
  color:#8400FF;
}
#u2606 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2606_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2607_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:102px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:right;
}
#u2607 {
  border-width:0px;
  position:absolute;
  left:31px;
  top:274px;
  width:102px;
  height:28px;
  display:flex;
  font-size:16px;
  text-align:right;
}
#u2607 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2607_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2608_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:17px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u2608 {
  border-width:0px;
  position:absolute;
  left:432px;
  top:280px;
  width:45px;
  height:17px;
  display:flex;
  font-size:12px;
}
#u2608 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2608_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2609 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2610_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:356px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#8400FF;
}
#u2610 {
  border-width:0px;
  position:absolute;
  left:133px;
  top:307px;
  width:356px;
  height:28px;
  display:flex;
  font-size:16px;
  color:#8400FF;
}
#u2610 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2610_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2611_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:102px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:right;
}
#u2611 {
  border-width:0px;
  position:absolute;
  left:31px;
  top:307px;
  width:102px;
  height:28px;
  display:flex;
  font-size:16px;
  text-align:right;
}
#u2611 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2611_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2612_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:17px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u2612 {
  border-width:0px;
  position:absolute;
  left:432px;
  top:313px;
  width:45px;
  height:17px;
  display:flex;
  font-size:12px;
}
#u2612 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2612_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
