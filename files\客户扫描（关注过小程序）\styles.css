﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:923px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u6648_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:900px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:50px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6648 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:900px;
  display:flex;
  opacity:0.49;
}
#u6648 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6648_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6649_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u6649 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:20px;
  width:51px;
  height:40px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u6649 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u6649_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6650_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:18px;
}
#u6650 {
  border-width:0px;
  position:absolute;
  left:425px;
  top:19px;
  width:23px;
  height:18px;
  display:flex;
}
#u6650 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6650_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6651_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:16px;
}
#u6651 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:21px;
  width:26px;
  height:16px;
  display:flex;
}
#u6651 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6651_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6652_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u6652 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:50px;
  width:300px;
  height:25px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u6652 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u6652_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6653 {
  position:fixed;
  left:50%;
  margin-left:-75px;
  top:50%;
  margin-top:-25px;
  visibility:hidden;
}
#u6653_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6653_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u6654_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u6654 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u6654 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u6654_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6653_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u6653_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u6655_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u6655 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u6655 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u6655_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6656_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u6656 {
  border-width:0px;
  position:absolute;
  left:60px;
  top:16px;
  width:80px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u6656 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u6656_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u6657_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u6657 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:10px;
  width:30px;
  height:30px;
  display:flex;
}
#u6657 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6657_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6658_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u6658 {
  border-width:0px;
  position:absolute;
  left:463px;
  top:50px;
  width:25px;
  height:25px;
  display:flex;
}
#u6658 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6658_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6659_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:25px;
}
#u6659 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:50px;
  width:15px;
  height:25px;
  display:flex;
}
#u6659 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6659_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6660_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:228px;
  height:11px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  text-align:center;
}
#u6660 {
  border-width:0px;
  position:absolute;
  left:136px;
  top:71px;
  width:228px;
  height:11px;
  display:flex;
  font-size:10px;
  text-align:center;
}
#u6660 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u6660_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6661 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u6662_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:470px;
  height:60px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6662 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:94px;
  width:470px;
  height:60px;
  display:flex;
}
#u6662 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6662_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6663_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:50px;
}
#u6663 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:99px;
  width:50px;
  height:50px;
  display:flex;
}
#u6663 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6663_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6664_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:394px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  color:#000000;
}
#u6664 {
  border-width:0px;
  position:absolute;
  left:90px;
  top:109px;
  width:394px;
  height:30px;
  display:flex;
  font-size:18px;
  color:#000000;
}
#u6664 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u6664_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6665 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u6666_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:470px;
  height:75px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6666 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:164px;
  width:470px;
  height:75px;
  display:flex;
}
#u6666 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6666_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6667_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u6667 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:171px;
  width:119px;
  height:18px;
  display:flex;
  font-size:16px;
}
#u6667 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u6667_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6668_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:30px;
  text-align:right;
}
#u6668 {
  border-width:0px;
  position:absolute;
  left:57px;
  top:197px;
  width:40px;
  height:30px;
  display:flex;
  font-size:30px;
  text-align:right;
}
#u6668 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u6668_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6669_input {
  position:absolute;
  left:0px;
  top:0px;
  width:381px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:28px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u6669_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:381px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:28px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u6669_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:381px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:28px;
  color:#D7D7D7;
}
#u6669 {
  border-width:0px;
  position:absolute;
  left:94px;
  top:197px;
  width:381px;
  height:30px;
  display:flex;
  font-size:28px;
  color:#D7D7D7;
}
#u6669 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6669_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:381px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:28px;
  color:#D7D7D7;
}
#u6669.disabled {
}
#u6670_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:28px;
  color:#D7D7D7;
}
#u6670 {
  border-width:0px;
  position:absolute;
  left:103px;
  top:198px;
  width:60px;
  height:30px;
  display:flex;
  font-size:28px;
  color:#D7D7D7;
}
#u6670 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u6670_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6671 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u6672_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:470px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6672 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:249px;
  width:470px;
  height:50px;
  display:flex;
}
#u6672 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6672_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6673_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:109px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u6673 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:265px;
  width:109px;
  height:18px;
  display:flex;
  font-size:16px;
}
#u6673 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u6673_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6674_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:83px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u6674 {
  border-width:0px;
  position:absolute;
  left:407px;
  top:259px;
  width:83px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u6674 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u6674_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6675 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u6676_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:470px;
  height:50px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6676 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:309px;
  width:470px;
  height:50px;
  display:flex;
}
#u6676 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6676_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6677_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:101px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:right;
}
#u6677 {
  border-width:0px;
  position:absolute;
  left:295px;
  top:325px;
  width:101px;
  height:18px;
  display:flex;
  font-size:16px;
  text-align:right;
}
#u6677 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u6677_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6678_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:83px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u6678 {
  border-width:0px;
  position:absolute;
  left:407px;
  top:319px;
  width:83px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u6678 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u6678_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6679_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:439px;
  height:50px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u6679 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:475px;
  width:439px;
  height:50px;
  display:flex;
  font-size:18px;
}
#u6679 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6679_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6680_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:439px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#AAAAAA;
  text-align:center;
}
#u6680 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:525px;
  width:439px;
  height:20px;
  display:flex;
  font-size:12px;
  color:#AAAAAA;
  text-align:center;
}
#u6680 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u6680_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6681 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u6682_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:470px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6682 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:379px;
  width:470px;
  height:54px;
  display:flex;
}
#u6682 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6682_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6683_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u6683 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:397px;
  width:119px;
  height:18px;
  display:flex;
  font-size:16px;
}
#u6683 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u6683_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6684_input {
  position:absolute;
  left:0px;
  top:0px;
  width:342px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:28px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u6684_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:342px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:28px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u6684_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:342px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:28px;
  color:#D7D7D7;
}
#u6684 {
  border-width:0px;
  position:absolute;
  left:133px;
  top:389px;
  width:342px;
  height:30px;
  display:flex;
  font-size:28px;
  color:#D7D7D7;
}
#u6684 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6684_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:342px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:28px;
  color:#D7D7D7;
}
#u6684.disabled {
}
#u6685_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:202px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#AAAAAA;
}
#u6685 {
  border-width:0px;
  position:absolute;
  left:163px;
  top:395px;
  width:202px;
  height:20px;
  display:flex;
  font-size:12px;
  color:#AAAAAA;
}
#u6685 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u6685_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6686_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:364px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6686 {
  border-width:0px;
  position:absolute;
  left:559px;
  top:154px;
  width:364px;
  height:15px;
  display:flex;
}
#u6686 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u6686_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
