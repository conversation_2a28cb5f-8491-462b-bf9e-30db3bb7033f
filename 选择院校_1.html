﻿<!DOCTYPE html>
<html>
  <head>
    <title>选择院校</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/选择院校_1/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/选择院校_1/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (基础app框架(H5)) -->

      <!-- Unnamed (矩形) -->
      <div id="u5918" class="ax_default box_1">
        <div id="u5918_div" class=""></div>
        <div id="u5918_text" class="text ">
          <p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5919" class="ax_default _二级标题">
        <div id="u5919_div" class=""></div>
        <div id="u5919_text" class="text ">
          <p><span>16:18</span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u5920" class="ax_default icon">
        <img id="u5920_img" class="img " src="images/海融宝签约_个人__f501_f502_/u3.svg"/>
        <div id="u5920_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u5921" class="ax_default icon">
        <img id="u5921_img" class="img " src="images/海融宝签约_个人__f501_f502_/u4.svg"/>
        <div id="u5921_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5922" class="ax_default _文本段落">
        <div id="u5922_div" class=""></div>
        <div id="u5922_text" class="text ">
          <p><span>选择院校</span></p>
        </div>
      </div>

      <!-- 操作状态 (动态面板) -->
      <div id="u5923" class="ax_default ax_default_hidden" data-label="操作状态" style="display:none; visibility: hidden">
        <div id="u5923_state0" class="panel_state" data-label="操作成功" style="">
          <div id="u5923_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u5924" class="ax_default box_3">
              <div id="u5924_div" class=""></div>
              <div id="u5924_text" class="text ">
                <p><span>操作成功！</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u5923_state1" class="panel_state" data-label="操作失败" style="visibility: hidden;">
          <div id="u5923_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u5925" class="ax_default box_3">
              <div id="u5925_div" class=""></div>
              <div id="u5925_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5926" class="ax_default _文本段落">
              <div id="u5926_div" class=""></div>
              <div id="u5926_text" class="text ">
                <p><span>操作失败！</span></p>
              </div>
            </div>

            <!-- Unnamed (图片 ) -->
            <div id="u5927" class="ax_default _图片_">
              <img id="u5927_img" class="img " src="images/海融宝签约_个人__f501_f502_/u10.png"/>
              <div id="u5927_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u5928" class="ax_default _图片_">
        <img id="u5928_img" class="img " src="images/海融宝签约_个人__f501_f502_/u11.png"/>
        <div id="u5928_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u5929" class="ax_default icon">
        <img id="u5929_img" class="img " src="images/海融宝签约_个人__f501_f502_/u12.svg"/>
        <div id="u5929_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5930" class="ax_default _文本段落">
        <div id="u5930_div" class=""></div>
        <div id="u5930_text" class="text ">
          <p><span>hrypt.com</span></p>
        </div>
      </div>
      <div id="u5917" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (矩形) -->
      <div id="u5931" class="ax_default box_1">
        <div id="u5931_div" class=""></div>
        <div id="u5931_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5932" class="ax_default box_1">
        <div id="u5932_div" class=""></div>
        <div id="u5932_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u5933" class="ax_default line">
        <img id="u5933_img" class="img " src="images/选择兴趣/u5702.svg"/>
        <div id="u5933_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u5934" class="ax_default" data-left="102" data-top="700" data-width="300" data-height="33">

        <!-- Unnamed (矩形) -->
        <div id="u5935" class="ax_default box_1">
          <div id="u5935_div" class=""></div>
          <div id="u5935_text" class="text ">
            <p><span>取消</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u5936" class="ax_default box_1">
          <div id="u5936_div" class=""></div>
          <div id="u5936_text" class="text ">
            <p><span>确定</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5937" class="ax_default box_1">
        <div id="u5937_div" class=""></div>
        <div id="u5937_text" class="text ">
          <p><span>请选择院校</span></p>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u5938" class="ax_default _图片_">
        <img id="u5938_img" class="img " src="images/充值方式/u1461.png"/>
        <div id="u5938_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u5939" class="ax_default" data-left="34" data-top="234" data-width="436" data-height="44">

        <!-- Unnamed (组合) -->
        <div id="u5940" class="ax_default" data-left="34" data-top="234" data-width="436" data-height="44">

          <!-- Unnamed (矩形) -->
          <div id="u5941" class="ax_default box_1">
            <div id="u5941_div" class=""></div>
            <div id="u5941_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (形状) -->
          <div id="u5942" class="ax_default icon">
            <img id="u5942_img" class="img " src="images/选择院校_1/u5942.svg"/>
            <div id="u5942_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u5943" class="ax_default _一级标题">
            <div id="u5943_div" class=""></div>
            <div id="u5943_text" class="text ">
              <p><span>交通</span></p>
            </div>
          </div>

          <!-- Unnamed (图片 ) -->
          <div id="u5944" class="ax_default _图片">
            <img id="u5944_img" class="img " src="images/____________f502_f503____f506_f507_f508_f509_/u304.png"/>
            <div id="u5944_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u5945" class="ax_default" data-left="59" data-top="290" data-width="405" data-height="30">

        <!-- Unnamed (矩形) -->
        <div id="u5946" class="ax_default box_1">
          <div id="u5946_div" class=""></div>
          <div id="u5946_text" class="text ">
            <p><span>上海交通大学</span></p>
          </div>
        </div>

        <!-- Unnamed (形状) -->
        <div id="u5947" class="ax_default icon">
          <img id="u5947_img" class="img " src="images/选择院校_1/u5947.svg"/>
          <div id="u5947_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u5948" class="ax_default" data-left="59" data-top="325" data-width="405" data-height="30">

        <!-- Unnamed (矩形) -->
        <div id="u5949" class="ax_default box_1">
          <div id="u5949_div" class=""></div>
          <div id="u5949_text" class="text ">
            <p><span>西安交通大学</span></p>
          </div>
        </div>

        <!-- Unnamed (形状) -->
        <div id="u5950" class="ax_default icon">
          <img id="u5950_img" class="img " src="images/选择院校_1/u5950.svg"/>
          <div id="u5950_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u5951" class="ax_default" data-left="59" data-top="360" data-width="405" data-height="30">

        <!-- Unnamed (矩形) -->
        <div id="u5952" class="ax_default box_1">
          <div id="u5952_div" class=""></div>
          <div id="u5952_text" class="text ">
            <p><span>北京交通大学</span></p>
          </div>
        </div>

        <!-- Unnamed (形状) -->
        <div id="u5953" class="ax_default icon">
          <img id="u5953_img" class="img " src="images/选择院校_1/u5950.svg"/>
          <div id="u5953_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
