﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-90px;
  width:1031px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u6166_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6166 {
  border-width:0px;
  position:absolute;
  left:275px;
  top:173px;
  width:86px;
  height:48px;
  display:flex;
}
#u6166 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6166_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6167_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6167 {
  border-width:0px;
  position:absolute;
  left:275px;
  top:247px;
  width:86px;
  height:48px;
  display:flex;
}
#u6167 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6167_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6168_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6168 {
  border-width:0px;
  position:absolute;
  left:90px;
  top:141px;
  width:86px;
  height:48px;
  display:flex;
}
#u6168 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6168_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6169_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6169 {
  border-width:0px;
  position:absolute;
  left:90px;
  top:197px;
  width:86px;
  height:48px;
  display:flex;
}
#u6169 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6169_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6170_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6170 {
  border-width:0px;
  position:absolute;
  left:90px;
  top:258px;
  width:86px;
  height:48px;
  display:flex;
}
#u6170 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6170_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6171_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6171 {
  border-width:0px;
  position:absolute;
  left:90px;
  top:314px;
  width:86px;
  height:48px;
  display:flex;
}
#u6171 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6171_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6172 {
  border-width:0px;
  position:absolute;
  left:176px;
  top:165px;
  width:0px;
  height:0px;
}
#u6172_seg0 {
  border-width:0px;
  position:absolute;
  left:-4px;
  top:13px;
  width:106px;
  height:6px;
  -webkit-transform:rotate(-162.087490614037deg);
  -moz-transform:rotate(-162.087490614037deg);
  -ms-transform:rotate(-162.087490614037deg);
  transform:rotate(-162.087490614037deg);
}
#u6172_seg1 {
  border-width:0px;
  position:absolute;
  left:-8px;
  top:-9px;
  width:21px;
  height:20px;
  -webkit-transform:rotate(17.912509385963deg);
  -moz-transform:rotate(17.912509385963deg);
  -ms-transform:rotate(17.912509385963deg);
  transform:rotate(17.912509385963deg);
}
#u6172_seg2 {
  border-width:0px;
  position:absolute;
  left:86px;
  top:21px;
  width:21px;
  height:20px;
  -webkit-transform:rotate(-162.087490614037deg);
  -moz-transform:rotate(-162.087490614037deg);
  -ms-transform:rotate(-162.087490614037deg);
  transform:rotate(-162.087490614037deg);
}
#u6172_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:10px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
#u6173 {
  border-width:0px;
  position:absolute;
  left:176px;
  top:221px;
  width:0px;
  height:0px;
}
#u6173_seg0 {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-15px;
  width:104px;
  height:6px;
  -webkit-transform:rotate(166.373005140108deg);
  -moz-transform:rotate(166.373005140108deg);
  -ms-transform:rotate(166.373005140108deg);
  transform:rotate(166.373005140108deg);
}
#u6173_seg1 {
  border-width:0px;
  position:absolute;
  left:-8px;
  top:-11px;
  width:21px;
  height:20px;
  -webkit-transform:rotate(-13.626994859891deg);
  -moz-transform:rotate(-13.626994859891deg);
  -ms-transform:rotate(-13.626994859891deg);
  transform:rotate(-13.626994859891deg);
}
#u6173_seg2 {
  border-width:0px;
  position:absolute;
  left:86px;
  top:-33px;
  width:21px;
  height:20px;
  -webkit-transform:rotate(166.373005140109deg);
  -moz-transform:rotate(166.373005140109deg);
  -ms-transform:rotate(166.373005140109deg);
  transform:rotate(166.373005140109deg);
}
#u6173_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-20px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6174 {
  border-width:0px;
  position:absolute;
  left:176px;
  top:282px;
  width:0px;
  height:0px;
}
#u6174_seg0 {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-8px;
  width:102px;
  height:6px;
  -webkit-transform:rotate(173.65980825409deg);
  -moz-transform:rotate(173.65980825409deg);
  -ms-transform:rotate(173.65980825409deg);
  transform:rotate(173.65980825409deg);
}
#u6174_seg1 {
  border-width:0px;
  position:absolute;
  left:-8px;
  top:-10px;
  width:21px;
  height:20px;
  -webkit-transform:rotate(-6.34019174590829deg);
  -moz-transform:rotate(-6.34019174590829deg);
  -ms-transform:rotate(-6.34019174590829deg);
  transform:rotate(-6.34019174590829deg);
}
#u6174_seg2 {
  border-width:0px;
  position:absolute;
  left:86px;
  top:-21px;
  width:21px;
  height:20px;
  -webkit-transform:rotate(173.659808254092deg);
  -moz-transform:rotate(173.659808254092deg);
  -ms-transform:rotate(173.659808254092deg);
  transform:rotate(173.659808254092deg);
}
#u6174_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-13px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6175 {
  border-width:0px;
  position:absolute;
  left:176px;
  top:338px;
  width:0px;
  height:0px;
}
#u6175_seg0 {
  border-width:0px;
  position:absolute;
  left:-12px;
  top:-38px;
  width:122px;
  height:8px;
  -webkit-transform:rotate(145.911128384283deg);
  -moz-transform:rotate(145.911128384283deg);
  -ms-transform:rotate(145.911128384283deg);
  transform:rotate(145.911128384283deg);
}
#u6175_seg1 {
  border-width:0px;
  position:absolute;
  left:-9px;
  top:-11px;
  width:21px;
  height:20px;
  -webkit-transform:rotate(-34.0888716157161deg);
  -moz-transform:rotate(-34.0888716157161deg);
  -ms-transform:rotate(-34.0888716157161deg);
  transform:rotate(-34.0888716157161deg);
}
#u6175_seg2 {
  border-width:0px;
  position:absolute;
  left:86px;
  top:-76px;
  width:21px;
  height:20px;
  -webkit-transform:rotate(145.911128384284deg);
  -moz-transform:rotate(145.911128384284deg);
  -ms-transform:rotate(145.911128384284deg);
  transform:rotate(145.911128384284deg);
}
#u6175_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-38px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
#u6176 {
  border-width:0px;
  position:absolute;
  left:176px;
  top:221px;
  width:0px;
  height:0px;
}
#u6176_seg0 {
  border-width:0px;
  position:absolute;
  left:-6px;
  top:21px;
  width:112px;
  height:8px;
  -webkit-transform:rotate(-153.203918706027deg);
  -moz-transform:rotate(-153.203918706027deg);
  -ms-transform:rotate(-153.203918706027deg);
  transform:rotate(-153.203918706027deg);
}
#u6176_seg1 {
  border-width:0px;
  position:absolute;
  left:-8px;
  top:-9px;
  width:21px;
  height:20px;
  -webkit-transform:rotate(26.7960812939725deg);
  -moz-transform:rotate(26.7960812939725deg);
  -ms-transform:rotate(26.7960812939725deg);
  transform:rotate(26.7960812939725deg);
}
#u6176_seg2 {
  border-width:0px;
  position:absolute;
  left:86px;
  top:39px;
  width:21px;
  height:20px;
  -webkit-transform:rotate(-153.203918706025deg);
  -moz-transform:rotate(-153.203918706025deg);
  -ms-transform:rotate(-153.203918706025deg);
  transform:rotate(-153.203918706025deg);
}
#u6176_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:17px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6177_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6177 {
  border-width:0px;
  position:absolute;
  left:481px;
  top:203px;
  width:86px;
  height:48px;
  display:flex;
}
#u6177 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6177_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6178 {
  border-width:0px;
  position:absolute;
  left:361px;
  top:197px;
  width:0px;
  height:0px;
}
#u6178_seg0 {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:11px;
  width:126px;
  height:8px;
  -webkit-transform:rotate(-165.963756532074deg);
  -moz-transform:rotate(-165.963756532074deg);
  -ms-transform:rotate(-165.963756532074deg);
  transform:rotate(-165.963756532074deg);
}
#u6178_seg1 {
  border-width:0px;
  position:absolute;
  left:-8px;
  top:-9px;
  width:21px;
  height:20px;
  -webkit-transform:rotate(14.0362434679265deg);
  -moz-transform:rotate(14.0362434679265deg);
  -ms-transform:rotate(14.0362434679265deg);
  transform:rotate(14.0362434679265deg);
}
#u6178_seg2 {
  border-width:0px;
  position:absolute;
  left:107px;
  top:19px;
  width:21px;
  height:20px;
  -webkit-transform:rotate(-165.963756532074deg);
  -moz-transform:rotate(-165.963756532074deg);
  -ms-transform:rotate(-165.963756532074deg);
  transform:rotate(-165.963756532074deg);
}
#u6178_text {
  border-width:0px;
  position:absolute;
  left:10px;
  top:10px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
#u6179 {
  border-width:0px;
  position:absolute;
  left:361px;
  top:271px;
  width:0px;
  height:0px;
}
#u6179_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-25px;
  width:130px;
  height:6px;
  -webkit-transform:rotate(159.863696571752deg);
  -moz-transform:rotate(159.863696571752deg);
  -ms-transform:rotate(159.863696571752deg);
  transform:rotate(159.863696571752deg);
}
#u6179_seg1 {
  border-width:0px;
  position:absolute;
  left:-8px;
  top:-11px;
  width:21px;
  height:20px;
  -webkit-transform:rotate(-20.1363034282478deg);
  -moz-transform:rotate(-20.1363034282478deg);
  -ms-transform:rotate(-20.1363034282478deg);
  transform:rotate(-20.1363034282478deg);
}
#u6179_seg2 {
  border-width:0px;
  position:absolute;
  left:107px;
  top:-53px;
  width:21px;
  height:20px;
  -webkit-transform:rotate(159.863696571752deg);
  -moz-transform:rotate(159.863696571752deg);
  -ms-transform:rotate(159.863696571752deg);
  transform:rotate(159.863696571752deg);
}
#u6179_text {
  border-width:0px;
  position:absolute;
  left:10px;
  top:-28px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
#u6180_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6180 {
  border-width:0px;
  position:absolute;
  left:679px;
  top:203px;
  width:86px;
  height:48px;
  display:flex;
}
#u6180 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6180_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6181 {
  border-width:0px;
  position:absolute;
  left:567px;
  top:227px;
  width:0px;
  height:0px;
}
#u6181_seg0 {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-3px;
  width:114px;
  height:6px;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u6181_seg1 {
  border-width:0px;
  position:absolute;
  left:-8px;
  top:-12px;
  width:25px;
  height:24px;
}
#u6181_seg2 {
  border-width:0px;
  position:absolute;
  left:95px;
  top:-12px;
  width:25px;
  height:24px;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
}
#u6181_text {
  border-width:0px;
  position:absolute;
  left:6px;
  top:-6px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
#u6182_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6182 {
  border-width:0px;
  position:absolute;
  left:864px;
  top:105px;
  width:86px;
  height:48px;
  display:flex;
}
#u6182 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6182_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6183_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6183 {
  border-width:0px;
  position:absolute;
  left:864px;
  top:167px;
  width:86px;
  height:48px;
  display:flex;
}
#u6183 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6183_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6184_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6184 {
  border-width:0px;
  position:absolute;
  left:864px;
  top:235px;
  width:86px;
  height:48px;
  display:flex;
}
#u6184 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6184_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6185_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6185 {
  border-width:0px;
  position:absolute;
  left:864px;
  top:448px;
  width:86px;
  height:48px;
  display:flex;
}
#u6185 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6185_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6186 {
  border-width:0px;
  position:absolute;
  left:765px;
  top:227px;
  width:0px;
  height:0px;
}
#u6186_seg0 {
  border-width:0px;
  position:absolute;
  left:-21px;
  top:-53px;
  width:142px;
  height:8px;
  -webkit-transform:rotate(135.290839022351deg);
  -moz-transform:rotate(135.290839022351deg);
  -ms-transform:rotate(135.290839022351deg);
  transform:rotate(135.290839022351deg);
}
#u6186_seg1 {
  border-width:0px;
  position:absolute;
  left:-9px;
  top:-12px;
  width:21px;
  height:20px;
  -webkit-transform:rotate(-44.7091609776472deg);
  -moz-transform:rotate(-44.7091609776472deg);
  -ms-transform:rotate(-44.7091609776472deg);
  transform:rotate(-44.7091609776472deg);
}
#u6186_seg2 {
  border-width:0px;
  position:absolute;
  left:87px;
  top:-106px;
  width:21px;
  height:20px;
  -webkit-transform:rotate(135.290839022353deg);
  -moz-transform:rotate(135.290839022353deg);
  -ms-transform:rotate(135.290839022353deg);
  transform:rotate(135.290839022353deg);
}
#u6186_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-60px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
#u6187 {
  border-width:0px;
  position:absolute;
  left:765px;
  top:227px;
  width:0px;
  height:0px;
}
#u6187_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-21px;
  width:108px;
  height:6px;
  -webkit-transform:rotate(160.0168934781deg);
  -moz-transform:rotate(160.0168934781deg);
  -ms-transform:rotate(160.0168934781deg);
  transform:rotate(160.0168934781deg);
}
#u6187_seg1 {
  border-width:0px;
  position:absolute;
  left:-8px;
  top:-11px;
  width:21px;
  height:20px;
  -webkit-transform:rotate(-19.9831065218994deg);
  -moz-transform:rotate(-19.9831065218994deg);
  -ms-transform:rotate(-19.9831065218994deg);
  transform:rotate(-19.9831065218994deg);
}
#u6187_seg2 {
  border-width:0px;
  position:absolute;
  left:86px;
  top:-45px;
  width:21px;
  height:20px;
  -webkit-transform:rotate(160.016893478101deg);
  -moz-transform:rotate(160.016893478101deg);
  -ms-transform:rotate(160.016893478101deg);
  transform:rotate(160.016893478101deg);
}
#u6187_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-26px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6188 {
  border-width:0px;
  position:absolute;
  left:765px;
  top:227px;
  width:0px;
  height:0px;
}
#u6188_seg0 {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:13px;
  width:106px;
  height:6px;
  -webkit-transform:rotate(-162.087490614037deg);
  -moz-transform:rotate(-162.087490614037deg);
  -ms-transform:rotate(-162.087490614037deg);
  transform:rotate(-162.087490614037deg);
}
#u6188_seg1 {
  border-width:0px;
  position:absolute;
  left:-8px;
  top:-9px;
  width:21px;
  height:20px;
  -webkit-transform:rotate(17.912509385962deg);
  -moz-transform:rotate(17.912509385962deg);
  -ms-transform:rotate(17.912509385962deg);
  transform:rotate(17.912509385962deg);
}
#u6188_seg2 {
  border-width:0px;
  position:absolute;
  left:86px;
  top:21px;
  width:21px;
  height:20px;
  -webkit-transform:rotate(-162.087490614036deg);
  -moz-transform:rotate(-162.087490614036deg);
  -ms-transform:rotate(-162.087490614036deg);
  transform:rotate(-162.087490614036deg);
}
#u6188_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6189 {
  border-width:0px;
  position:absolute;
  left:765px;
  top:227px;
  width:0px;
  height:0px;
}
#u6189_seg0 {
  border-width:0px;
  position:absolute;
  left:-83px;
  top:119px;
  width:266px;
  height:8px;
  -webkit-transform:rotate(-112.002729035619deg);
  -moz-transform:rotate(-112.002729035619deg);
  -ms-transform:rotate(-112.002729035619deg);
  transform:rotate(-112.002729035619deg);
}
#u6189_seg1 {
  border-width:0px;
  position:absolute;
  left:-10px;
  top:-8px;
  width:21px;
  height:20px;
  -webkit-transform:rotate(67.9972709643827deg);
  -moz-transform:rotate(67.9972709643827deg);
  -ms-transform:rotate(67.9972709643827deg);
  transform:rotate(67.9972709643827deg);
}
#u6189_seg2 {
  border-width:0px;
  position:absolute;
  left:87px;
  top:233px;
  width:21px;
  height:20px;
  -webkit-transform:rotate(-112.002729035617deg);
  -moz-transform:rotate(-112.002729035617deg);
  -ms-transform:rotate(-112.002729035617deg);
  transform:rotate(-112.002729035617deg);
}
#u6189_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:112px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
#u6190_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6190 {
  border-width:0px;
  position:absolute;
  left:1035px;
  top:388px;
  width:86px;
  height:48px;
  display:flex;
}
#u6190 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6190_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6191_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6191 {
  border-width:0px;
  position:absolute;
  left:1035px;
  top:450px;
  width:86px;
  height:48px;
  display:flex;
}
#u6191 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6191_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6192_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6192 {
  border-width:0px;
  position:absolute;
  left:1035px;
  top:518px;
  width:86px;
  height:48px;
  display:flex;
}
#u6192 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6192_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6193 {
  border-width:0px;
  position:absolute;
  left:950px;
  top:472px;
  width:0px;
  height:0px;
}
#u6193_seg0 {
  border-width:0px;
  position:absolute;
  left:-10px;
  top:-34px;
  width:106px;
  height:8px;
  -webkit-transform:rotate(144.782407031807deg);
  -moz-transform:rotate(144.782407031807deg);
  -ms-transform:rotate(144.782407031807deg);
  transform:rotate(144.782407031807deg);
}
#u6193_seg1 {
  border-width:0px;
  position:absolute;
  left:-9px;
  top:-11px;
  width:21px;
  height:20px;
  -webkit-transform:rotate(-35.2175929681933deg);
  -moz-transform:rotate(-35.2175929681933deg);
  -ms-transform:rotate(-35.2175929681933deg);
  transform:rotate(-35.2175929681933deg);
}
#u6193_seg2 {
  border-width:0px;
  position:absolute;
  left:72px;
  top:-69px;
  width:21px;
  height:20px;
  -webkit-transform:rotate(144.78240703181deg);
  -moz-transform:rotate(144.78240703181deg);
  -ms-transform:rotate(144.78240703181deg);
  transform:rotate(144.78240703181deg);
}
#u6193_text {
  border-width:0px;
  position:absolute;
  left:-7px;
  top:-41px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
#u6194 {
  border-width:0px;
  position:absolute;
  left:950px;
  top:472px;
  width:0px;
  height:0px;
}
#u6194_seg0 {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:88px;
  height:6px;
  -webkit-transform:rotate(-178.652112719801deg);
  -moz-transform:rotate(-178.652112719801deg);
  -ms-transform:rotate(-178.652112719801deg);
  transform:rotate(-178.652112719801deg);
}
#u6194_seg1 {
  border-width:0px;
  position:absolute;
  left:-8px;
  top:-10px;
  width:21px;
  height:20px;
  -webkit-transform:rotate(1.34788728020002deg);
  -moz-transform:rotate(1.34788728020002deg);
  -ms-transform:rotate(1.34788728020002deg);
  transform:rotate(1.34788728020002deg);
}
#u6194_seg2 {
  border-width:0px;
  position:absolute;
  left:72px;
  top:-8px;
  width:21px;
  height:20px;
  -webkit-transform:rotate(-178.6521127198deg);
  -moz-transform:rotate(-178.6521127198deg);
  -ms-transform:rotate(-178.6521127198deg);
  transform:rotate(-178.6521127198deg);
}
#u6194_text {
  border-width:0px;
  position:absolute;
  left:-7px;
  top:-7px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6195 {
  border-width:0px;
  position:absolute;
  left:950px;
  top:472px;
  width:0px;
  height:0px;
}
#u6195_seg0 {
  border-width:0px;
  position:absolute;
  left:-14px;
  top:32px;
  width:112px;
  height:6px;
  -webkit-transform:rotate(-140.527540151656deg);
  -moz-transform:rotate(-140.527540151656deg);
  -ms-transform:rotate(-140.527540151656deg);
  transform:rotate(-140.527540151656deg);
}
#u6195_seg1 {
  border-width:0px;
  position:absolute;
  left:-9px;
  top:-8px;
  width:21px;
  height:20px;
  -webkit-transform:rotate(39.4724598483416deg);
  -moz-transform:rotate(39.4724598483416deg);
  -ms-transform:rotate(39.4724598483416deg);
  transform:rotate(39.4724598483416deg);
}
#u6195_seg2 {
  border-width:0px;
  position:absolute;
  left:72px;
  top:58px;
  width:21px;
  height:20px;
  -webkit-transform:rotate(-140.527540151652deg);
  -moz-transform:rotate(-140.527540151652deg);
  -ms-transform:rotate(-140.527540151652deg);
  transform:rotate(-140.527540151652deg);
}
#u6195_text {
  border-width:0px;
  position:absolute;
  left:-7px;
  top:24px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
#u6196_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:697px;
  height:150px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6196 {
  border-width:0px;
  position:absolute;
  left:90px;
  top:472px;
  width:697px;
  height:150px;
  display:flex;
}
#u6196 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u6196_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u6197_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6197 {
  border-width:0px;
  position:absolute;
  left:864px;
  top:300px;
  width:86px;
  height:48px;
  display:flex;
}
#u6197 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6197_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6198 {
  border-width:0px;
  position:absolute;
  left:765px;
  top:227px;
  width:0px;
  height:0px;
}
#u6198_seg0 {
  border-width:0px;
  position:absolute;
  left:-21px;
  top:45px;
  width:140px;
  height:8px;
  -webkit-transform:rotate(-135.584630520705deg);
  -moz-transform:rotate(-135.584630520705deg);
  -ms-transform:rotate(-135.584630520705deg);
  transform:rotate(-135.584630520705deg);
}
#u6198_seg1 {
  border-width:0px;
  position:absolute;
  left:-9px;
  top:-8px;
  width:21px;
  height:20px;
  -webkit-transform:rotate(44.415369479294deg);
  -moz-transform:rotate(44.415369479294deg);
  -ms-transform:rotate(44.415369479294deg);
  transform:rotate(44.415369479294deg);
}
#u6198_seg2 {
  border-width:0px;
  position:absolute;
  left:87px;
  top:85px;
  width:21px;
  height:20px;
  -webkit-transform:rotate(-135.584630520705deg);
  -moz-transform:rotate(-135.584630520705deg);
  -ms-transform:rotate(-135.584630520705deg);
  transform:rotate(-135.584630520705deg);
}
#u6198_text {
  border-width:0px;
  position:absolute;
  left:0px;
  top:38px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
#u6199_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:554px;
  height:60px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6199 {
  border-width:0px;
  position:absolute;
  left:90px;
  top:388px;
  width:554px;
  height:60px;
  display:flex;
}
#u6199 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u6199_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u6200_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:399px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u6200 {
  border-width:0px;
  position:absolute;
  left:90px;
  top:86px;
  width:399px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u6200 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u6200_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
