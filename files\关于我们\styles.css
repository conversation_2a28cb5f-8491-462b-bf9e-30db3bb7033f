﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:510px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u4803_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:900px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:50px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4803 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:900px;
  display:flex;
  opacity:0.49;
}
#u4803 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4803_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4804 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4805 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4806_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u4806 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:834px;
  width:26px;
  height:25px;
  display:flex;
}
#u4806 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4806_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4807_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u4807 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:860px;
  width:26px;
  height:14px;
  display:flex;
  text-align:center;
}
#u4807 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4807_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4808 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4809_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u4809 {
  border-width:0px;
  position:absolute;
  left:130px;
  top:834px;
  width:26px;
  height:25px;
  display:flex;
}
#u4809 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4809_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4810_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u4810 {
  border-width:0px;
  position:absolute;
  left:130px;
  top:860px;
  width:26px;
  height:14px;
  display:flex;
  text-align:center;
}
#u4810 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4810_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4811 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4812_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u4812 {
  border-width:0px;
  position:absolute;
  left:345px;
  top:834px;
  width:26px;
  height:25px;
  display:flex;
}
#u4812 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4812_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4813_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  background-image:url('../../images/平台首页/u2795.png');
  background-repeat:no-repeat;
  background-size:200px 200px;
  background-position: left top;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u4813 {
  border-width:0px;
  position:absolute;
  left:345px;
  top:860px;
  width:26px;
  height:14px;
  display:flex;
  text-align:center;
}
#u4813 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4813_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4814 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4815_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u4815 {
  border-width:0px;
  position:absolute;
  left:452px;
  top:834px;
  width:26px;
  height:25px;
  display:flex;
}
#u4815 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4815_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4816_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u4816 {
  border-width:0px;
  position:absolute;
  left:452px;
  top:860px;
  width:26px;
  height:14px;
  display:flex;
  text-align:center;
}
#u4816 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4816_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4817 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4818_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u4818 {
  border-width:0px;
  position:absolute;
  left:237px;
  top:834px;
  width:26px;
  height:25px;
  display:flex;
}
#u4818 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4818_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4819_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:44px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u4819 {
  border-width:0px;
  position:absolute;
  left:228px;
  top:860px;
  width:44px;
  height:14px;
  display:flex;
  text-align:center;
}
#u4819 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4819_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4820_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 0, 0, 1);
  box-sizing:border-box;
  border-width:2px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:75px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u4820 {
  border-width:0px;
  position:absolute;
  left:363px;
  top:834px;
  width:21px;
  height:15px;
  display:flex;
  color:#FFFFFF;
}
#u4820 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4820_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4821_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u4821 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:20px;
  width:51px;
  height:40px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u4821 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4821_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4822_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:18px;
}
#u4822 {
  border-width:0px;
  position:absolute;
  left:425px;
  top:19px;
  width:23px;
  height:18px;
  display:flex;
}
#u4822 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4822_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4823_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:16px;
}
#u4823 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:21px;
  width:26px;
  height:16px;
  display:flex;
}
#u4823 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4823_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4824_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:25px;
}
#u4824 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:50px;
  width:24px;
  height:25px;
  display:flex;
}
#u4824 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4824_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4825_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u4825 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:49px;
  width:300px;
  height:25px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u4825 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4825_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4826 {
  position:fixed;
  left:50%;
  margin-left:-75px;
  top:50%;
  margin-top:-25px;
  visibility:hidden;
}
#u4826_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4826_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u4827_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u4827 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u4827 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u4827_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4826_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u4826_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u4828_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u4828 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u4828 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u4828_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4829_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u4829 {
  border-width:0px;
  position:absolute;
  left:60px;
  top:16px;
  width:80px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u4829 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4829_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4830_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u4830 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:10px;
  width:30px;
  height:30px;
  display:flex;
}
#u4830 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4830_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4831_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:228px;
  height:11px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  text-align:center;
}
#u4831 {
  border-width:0px;
  position:absolute;
  left:136px;
  top:71px;
  width:228px;
  height:11px;
  display:flex;
  font-size:10px;
  text-align:center;
}
#u4831 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4831_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4833_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:400px;
  height:200px;
}
#u4833 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:110px;
  width:400px;
  height:200px;
  display:flex;
}
#u4833 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4833_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4834_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:400px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:28px;
  color:#0000FF;
  text-align:center;
}
#u4834 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:116px;
  width:400px;
  height:32px;
  display:flex;
  font-size:28px;
  color:#0000FF;
  text-align:center;
}
#u4834 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4834_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4835_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:17px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u4835 {
  border-width:0px;
  position:absolute;
  left:211px;
  top:317px;
  width:89px;
  height:17px;
  display:flex;
  font-size:16px;
}
#u4835 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4835_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4836_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:484px;
  height:295px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u4836 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:496px;
  width:484px;
  height:295px;
  display:flex;
  font-size:16px;
}
#u4836 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4836_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4837 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4838_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:222px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u4838 {
  border-width:0px;
  position:absolute;
  left:33px;
  top:516px;
  width:222px;
  height:20px;
  display:flex;
  font-size:16px;
}
#u4838 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4838_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4839_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:20px;
}
#u4839 {
  border-width:0px;
  position:absolute;
  left:464px;
  top:516px;
  width:11px;
  height:20px;
  display:flex;
  font-size:16px;
}
#u4839 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4839_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4840_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:222px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u4840 {
  border-width:0px;
  position:absolute;
  left:33px;
  top:570px;
  width:222px;
  height:20px;
  display:flex;
  font-size:16px;
}
#u4840 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4840_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4841 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4842_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:222px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u4842 {
  border-width:0px;
  position:absolute;
  left:33px;
  top:625px;
  width:222px;
  height:20px;
  display:flex;
  font-size:16px;
}
#u4842 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4842_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4843_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:20px;
}
#u4843 {
  border-width:0px;
  position:absolute;
  left:464px;
  top:625px;
  width:11px;
  height:20px;
  display:flex;
  font-size:16px;
}
#u4843 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4843_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4844_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:459px;
  height:2px;
}
#u4844 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:552px;
  width:458px;
  height:1px;
  display:flex;
  font-size:16px;
}
#u4844 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4844_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4845_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:459px;
  height:2px;
}
#u4845 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:606px;
  width:458px;
  height:1px;
  display:flex;
  font-size:16px;
}
#u4845 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4845_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4846_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:459px;
  height:2px;
}
#u4846 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:666px;
  width:458px;
  height:1px;
  display:flex;
  font-size:16px;
}
#u4846 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4846_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4847_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:222px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u4847 {
  border-width:0px;
  position:absolute;
  left:33px;
  top:691px;
  width:222px;
  height:20px;
  display:flex;
  font-size:16px;
}
#u4847 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4847_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4848_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:141px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u4848 {
  border-width:0px;
  position:absolute;
  left:334px;
  top:570px;
  width:141px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#7F7F7F;
  text-align:right;
}
#u4848 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4848_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4849_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:182px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-decoration:underline ;
  color:#7F7F7F;
  text-align:right;
}
#u4849 {
  border-width:0px;
  position:absolute;
  left:302px;
  top:691px;
  width:182px;
  height:21px;
  display:flex;
  font-size:16px;
  text-decoration:underline ;
  color:#7F7F7F;
  text-align:right;
}
#u4849 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4849_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4850_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:459px;
  height:2px;
}
#u4850 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:725px;
  width:458px;
  height:1px;
  display:flex;
  font-size:16px;
}
#u4850 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4850_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4851 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4852_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:222px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u4852 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:748px;
  width:222px;
  height:20px;
  display:flex;
  font-size:16px;
}
#u4852 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4852_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4853_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:20px;
}
#u4853 {
  border-width:0px;
  position:absolute;
  left:457px;
  top:749px;
  width:11px;
  height:20px;
  display:flex;
  font-size:16px;
}
#u4853 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4853_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4854_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:93px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#8400FF;
  text-align:right;
}
#u4854 {
  border-width:0px;
  position:absolute;
  left:405px;
  top:474px;
  width:93px;
  height:22px;
  display:flex;
  font-size:14px;
  color:#8400FF;
  text-align:right;
}
#u4854 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4854_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
