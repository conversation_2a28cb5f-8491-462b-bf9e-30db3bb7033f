﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:510px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u4921_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:900px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:50px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4921 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:900px;
  display:flex;
  opacity:0.49;
}
#u4921 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4921_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4922 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4923 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4924_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u4924 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:834px;
  width:26px;
  height:25px;
  display:flex;
}
#u4924 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4924_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4925_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u4925 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:860px;
  width:26px;
  height:14px;
  display:flex;
  text-align:center;
}
#u4925 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4925_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4926 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4927_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u4927 {
  border-width:0px;
  position:absolute;
  left:130px;
  top:834px;
  width:26px;
  height:25px;
  display:flex;
}
#u4927 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4927_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4928_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u4928 {
  border-width:0px;
  position:absolute;
  left:130px;
  top:860px;
  width:26px;
  height:14px;
  display:flex;
  text-align:center;
}
#u4928 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4928_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4929 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4930_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u4930 {
  border-width:0px;
  position:absolute;
  left:345px;
  top:834px;
  width:26px;
  height:25px;
  display:flex;
}
#u4930 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4930_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4931_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  background-image:url('../../images/平台首页/u2795.png');
  background-repeat:no-repeat;
  background-size:200px 200px;
  background-position: left top;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u4931 {
  border-width:0px;
  position:absolute;
  left:345px;
  top:860px;
  width:26px;
  height:14px;
  display:flex;
  text-align:center;
}
#u4931 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4931_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4932 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4933_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u4933 {
  border-width:0px;
  position:absolute;
  left:452px;
  top:834px;
  width:26px;
  height:25px;
  display:flex;
}
#u4933 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4933_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4934_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u4934 {
  border-width:0px;
  position:absolute;
  left:452px;
  top:860px;
  width:26px;
  height:14px;
  display:flex;
  text-align:center;
}
#u4934 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4934_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4935 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4936_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u4936 {
  border-width:0px;
  position:absolute;
  left:237px;
  top:834px;
  width:26px;
  height:25px;
  display:flex;
}
#u4936 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4936_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4937_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:44px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u4937 {
  border-width:0px;
  position:absolute;
  left:228px;
  top:860px;
  width:44px;
  height:14px;
  display:flex;
  text-align:center;
}
#u4937 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4937_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4938_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 0, 0, 1);
  box-sizing:border-box;
  border-width:2px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:75px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u4938 {
  border-width:0px;
  position:absolute;
  left:363px;
  top:834px;
  width:21px;
  height:15px;
  display:flex;
  color:#FFFFFF;
}
#u4938 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4938_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4939_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u4939 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:20px;
  width:51px;
  height:40px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u4939 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4939_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4940_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:18px;
}
#u4940 {
  border-width:0px;
  position:absolute;
  left:425px;
  top:19px;
  width:23px;
  height:18px;
  display:flex;
}
#u4940 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4940_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4941_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:16px;
}
#u4941 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:21px;
  width:26px;
  height:16px;
  display:flex;
}
#u4941 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4941_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4942_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:25px;
}
#u4942 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:50px;
  width:24px;
  height:25px;
  display:flex;
}
#u4942 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4942_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4943_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u4943 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:49px;
  width:300px;
  height:25px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u4943 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4943_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4944 {
  position:fixed;
  left:50%;
  margin-left:-75px;
  top:50%;
  margin-top:-25px;
  visibility:hidden;
}
#u4944_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4944_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u4945_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u4945 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u4945 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u4945_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4944_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u4944_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u4946_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u4946 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u4946 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u4946_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4947_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u4947 {
  border-width:0px;
  position:absolute;
  left:60px;
  top:16px;
  width:80px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u4947 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4947_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4948_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u4948 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:10px;
  width:30px;
  height:30px;
  display:flex;
}
#u4948 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4948_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4949_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:228px;
  height:11px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  text-align:center;
}
#u4949 {
  border-width:0px;
  position:absolute;
  left:136px;
  top:71px;
  width:228px;
  height:11px;
  display:flex;
  font-size:10px;
  text-align:center;
}
#u4949 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4949_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4950_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:500px;
  height:340px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 228, 228, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AEAEAE;
  text-align:left;
}
#u4950 {
  border-width:0px;
  position:absolute;
  left:5px;
  top:475px;
  width:500px;
  height:340px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AEAEAE;
  text-align:left;
}
#u4950 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4950_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4952 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4953_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u4953 {
  border-width:0px;
  position:absolute;
  left:50px;
  top:715px;
  width:260px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u4953 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4953_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4954_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:30px;
}
#u4954 {
  border-width:0px;
  position:absolute;
  left:479px;
  top:715px;
  width:15px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u4954 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4954_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4955_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u4955 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:715px;
  width:30px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u4955 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4955_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4956_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:159px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u4956 {
  border-width:0px;
  position:absolute;
  left:310px;
  top:715px;
  width:159px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u4956 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4956_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4957_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u4957 {
  border-width:0px;
  position:absolute;
  left:5px;
  top:755px;
  width:500px;
  height:1px;
  display:flex;
}
#u4957 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4957_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4958_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u4958 {
  border-width:0px;
  position:absolute;
  left:5px;
  top:706px;
  width:500px;
  height:1px;
  display:flex;
}
#u4958 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4958_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4960 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4961_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u4961 {
  border-width:0px;
  position:absolute;
  left:50px;
  top:665px;
  width:260px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u4961 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4961_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4962_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:30px;
}
#u4962 {
  border-width:0px;
  position:absolute;
  left:479px;
  top:665px;
  width:15px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u4962 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4962_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4963_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u4963 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:665px;
  width:30px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u4963 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4963_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4964_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:159px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u4964 {
  border-width:0px;
  position:absolute;
  left:310px;
  top:665px;
  width:159px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u4964 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4964_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4965_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u4965 {
  border-width:0px;
  position:absolute;
  left:5px;
  top:705px;
  width:500px;
  height:1px;
  display:flex;
}
#u4965 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4965_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4966_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u4966 {
  border-width:0px;
  position:absolute;
  left:5px;
  top:656px;
  width:500px;
  height:1px;
  display:flex;
}
#u4966 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4966_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4968 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4969_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u4969 {
  border-width:0px;
  position:absolute;
  left:50px;
  top:615px;
  width:260px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u4969 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4969_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4970_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:30px;
}
#u4970 {
  border-width:0px;
  position:absolute;
  left:479px;
  top:615px;
  width:15px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u4970 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4970_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4971_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u4971 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:615px;
  width:30px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u4971 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4971_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4972_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:159px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u4972 {
  border-width:0px;
  position:absolute;
  left:310px;
  top:615px;
  width:159px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u4972 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4972_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4973_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u4973 {
  border-width:0px;
  position:absolute;
  left:5px;
  top:655px;
  width:500px;
  height:1px;
  display:flex;
}
#u4973 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4973_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4974_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u4974 {
  border-width:0px;
  position:absolute;
  left:5px;
  top:606px;
  width:500px;
  height:1px;
  display:flex;
}
#u4974 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4974_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4976 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4977_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u4977 {
  border-width:0px;
  position:absolute;
  left:50px;
  top:565px;
  width:260px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u4977 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4977_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4978_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:30px;
}
#u4978 {
  border-width:0px;
  position:absolute;
  left:479px;
  top:565px;
  width:15px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u4978 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4978_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4979_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u4979 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:565px;
  width:30px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u4979 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4979_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4980_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:159px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u4980 {
  border-width:0px;
  position:absolute;
  left:310px;
  top:565px;
  width:159px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u4980 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4980_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4981_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u4981 {
  border-width:0px;
  position:absolute;
  left:5px;
  top:605px;
  width:500px;
  height:1px;
  display:flex;
}
#u4981 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4981_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4982_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u4982 {
  border-width:0px;
  position:absolute;
  left:5px;
  top:556px;
  width:500px;
  height:1px;
  display:flex;
}
#u4982 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4982_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4984 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4985_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u4985 {
  border-width:0px;
  position:absolute;
  left:50px;
  top:515px;
  width:260px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u4985 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4985_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4986_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:30px;
}
#u4986 {
  border-width:0px;
  position:absolute;
  left:479px;
  top:515px;
  width:15px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u4986 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4986_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4987_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u4987 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:515px;
  width:30px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u4987 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4987_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4988_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:159px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u4988 {
  border-width:0px;
  position:absolute;
  left:310px;
  top:515px;
  width:159px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u4988 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4988_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4989_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u4989 {
  border-width:0px;
  position:absolute;
  left:5px;
  top:555px;
  width:500px;
  height:1px;
  display:flex;
}
#u4989 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4989_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4990_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u4990 {
  border-width:0px;
  position:absolute;
  left:5px;
  top:506px;
  width:500px;
  height:1px;
  display:flex;
}
#u4990 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4990_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4992 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4993_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u4993 {
  border-width:0px;
  position:absolute;
  left:50px;
  top:765px;
  width:260px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
}
#u4993 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4993_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4994_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:30px;
}
#u4994 {
  border-width:0px;
  position:absolute;
  left:479px;
  top:765px;
  width:15px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u4994 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4994_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4995_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u4995 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:765px;
  width:30px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u4995 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4995_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4996_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:159px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u4996 {
  border-width:0px;
  position:absolute;
  left:310px;
  top:765px;
  width:159px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
}
#u4996 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4996_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4997_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u4997 {
  border-width:0px;
  position:absolute;
  left:5px;
  top:805px;
  width:500px;
  height:1px;
  display:flex;
}
#u4997 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4997_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4998_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:501px;
  height:2px;
}
#u4998 {
  border-width:0px;
  position:absolute;
  left:5px;
  top:756px;
  width:500px;
  height:1px;
  display:flex;
}
#u4998 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4998_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4999 {
  border-width:0px;
  position:absolute;
  left:12px;
  top:516px;
  width:480px;
  height:30px;
  overflow:hidden;
  background-image:url('../../resources/images/transparent.gif');
}
#u5000 {
  border-width:0px;
  position:absolute;
  left:10px;
  top:564px;
  width:480px;
  height:30px;
  overflow:hidden;
  background-image:url('../../resources/images/transparent.gif');
}
#u5001 {
  border-width:0px;
  position:absolute;
  left:10px;
  top:614px;
  width:480px;
  height:30px;
  overflow:hidden;
  background-image:url('../../resources/images/transparent.gif');
}
#u5002 {
  border-width:0px;
  position:absolute;
  left:10px;
  top:664px;
  width:480px;
  height:30px;
  overflow:hidden;
  background-image:url('../../resources/images/transparent.gif');
}
#u5003 {
  border-width:0px;
  position:absolute;
  left:10px;
  top:714px;
  width:480px;
  height:30px;
  overflow:hidden;
  background-image:url('../../resources/images/transparent.gif');
}
#u5004 {
  border-width:0px;
  position:absolute;
  left:10px;
  top:766px;
  width:480px;
  height:30px;
  overflow:hidden;
  background-image:url('../../resources/images/transparent.gif');
}
