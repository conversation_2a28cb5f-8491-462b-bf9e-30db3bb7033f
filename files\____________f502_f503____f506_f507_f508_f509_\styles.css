﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:1998px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u220_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:900px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:50px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u220 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:900px;
  display:flex;
  opacity:0.49;
}
#u220 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u220_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u221_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u221 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:20px;
  width:51px;
  height:40px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u221 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u221_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u222_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:18px;
}
#u222 {
  border-width:0px;
  position:absolute;
  left:425px;
  top:19px;
  width:23px;
  height:18px;
  display:flex;
}
#u222 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u222_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u223_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:16px;
}
#u223 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:21px;
  width:26px;
  height:16px;
  display:flex;
}
#u223 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u223_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u224_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u224 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:50px;
  width:300px;
  height:25px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u224 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u224_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u225 {
  position:fixed;
  left:50%;
  margin-left:-75px;
  top:50%;
  margin-top:-25px;
  visibility:hidden;
}
#u225_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u225_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u226_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u226 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u226 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u226_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u225_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u225_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u227_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u227 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u227 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u227_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u228_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u228 {
  border-width:0px;
  position:absolute;
  left:60px;
  top:16px;
  width:80px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u228 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u228_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u229_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u229 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:10px;
  width:30px;
  height:30px;
  display:flex;
}
#u229 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u229_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u230_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u230 {
  border-width:0px;
  position:absolute;
  left:463px;
  top:50px;
  width:25px;
  height:25px;
  display:flex;
}
#u230 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u230_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u231_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:25px;
}
#u231 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:50px;
  width:15px;
  height:25px;
  display:flex;
}
#u231 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u231_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u232_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:228px;
  height:11px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  text-align:center;
}
#u232 {
  border-width:0px;
  position:absolute;
  left:136px;
  top:71px;
  width:228px;
  height:11px;
  display:flex;
  font-size:10px;
  text-align:center;
}
#u232 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u232_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u233_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:486px;
  height:172px;
  background:inherit;
  background-color:rgba(255, 204, 255, 1);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#AEAEAE;
  text-align:left;
}
#u233 {
  border-width:0px;
  position:absolute;
  left:12px;
  top:103px;
  width:486px;
  height:172px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#AEAEAE;
  text-align:left;
}
#u233 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u233_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u234_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:216px;
  height:31px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:28px;
  color:#000000;
  text-align:center;
}
#u234 {
  border-width:0px;
  position:absolute;
  left:93px;
  top:158px;
  width:216px;
  height:31px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:28px;
  color:#000000;
  text-align:center;
}
#u234 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u234_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u235_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:210px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u235 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:226px;
  width:210px;
  height:22px;
  display:flex;
}
#u235 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u235_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u236_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:207px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:right;
}
#u236 {
  border-width:0px;
  position:absolute;
  left:277px;
  top:226px;
  width:207px;
  height:22px;
  display:flex;
  text-align:right;
}
#u236 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u236_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u237_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:351px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u237 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:111px;
  width:351px;
  height:32px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u237 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u237_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u238_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:36px;
  background:inherit;
  background-color:rgba(18, 150, 219, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(201, 201, 201, 1);
  border-radius:58px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#FFFFFF;
}
#u238 {
  border-width:0px;
  position:absolute;
  left:385px;
  top:120px;
  width:99px;
  height:36px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#FFFFFF;
}
#u238 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u238_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u239_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:36px;
  background:inherit;
  background-color:rgba(18, 150, 219, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(201, 201, 201, 1);
  border-radius:58px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#FFFFFF;
}
#u239 {
  border-width:0px;
  position:absolute;
  left:385px;
  top:171px;
  width:99px;
  height:36px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#FFFFFF;
}
#u239 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u239_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u240 {
  border-width:0px;
  position:absolute;
  left:12px;
  top:284px;
  width:488px;
  height:527px;
}
#u240_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:488px;
  height:527px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u240_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u241 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:488px;
  height:517px;
}
#u241_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:488px;
  height:517px;
  overflow:auto;
  -webkit-overflow-scrolling:touch;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u241_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u244_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u244 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:106px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u244 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u244_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u245_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u245 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:99px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u245 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u245_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u246_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u246 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:127px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u246 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u246_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u247_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:329px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u247 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:99px;
  width:329px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u247 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u247_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u248_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u248 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:155px;
  width:470px;
  height:2px;
  display:flex;
}
#u248 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u248_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u249_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u249 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:131px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u249 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u249_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u250_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u250 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:127px;
  width:201px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u250 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u250_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u252_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u252 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:516px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u252 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u252_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u253_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u253 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:509px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u253 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u253_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u254_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u254 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:537px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u254 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u254_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u255_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:329px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u255 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:509px;
  width:329px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u255 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u255_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u256_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u256 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:565px;
  width:470px;
  height:2px;
  display:flex;
}
#u256 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u256_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u257_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u257 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:541px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u257 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u257_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u258_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u258 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:537px;
  width:201px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u258 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u258_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u260_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u260 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:188px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u260 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u260_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u261_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u261 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:181px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u261 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u261_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u262_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u262 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:209px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u262 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u262_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u263_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:328px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u263 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:181px;
  width:328px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u263 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u263_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u264_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u264 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:237px;
  width:470px;
  height:2px;
  display:flex;
}
#u264 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u264_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u265_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u265 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:211px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u265 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u265_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u266_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u266 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:209px;
  width:200px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u266 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u266_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u268_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u268 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:434px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u268 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u268_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u269_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u269 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:427px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u269 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u269_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u270_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u270 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:455px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u270 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u270_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u271_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:328px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u271 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:427px;
  width:328px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u271 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u271_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u272_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u272 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:483px;
  width:470px;
  height:2px;
  display:flex;
}
#u272 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u272_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u273_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u273 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:457px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u273 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u273_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u274_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u274 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:455px;
  width:200px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u274 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u274_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u276_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u276 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:352px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u276 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u276_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u277_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u277 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:345px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u277 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u277_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u278_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u278 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:373px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u278 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u278_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u279_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:328px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u279 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:345px;
  width:328px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u279 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u279_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u280_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u280 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:401px;
  width:470px;
  height:2px;
  display:flex;
}
#u280 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u280_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u281_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u281 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:375px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u281 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u281_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u282_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u282 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:373px;
  width:200px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u282 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u282_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u284_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u284 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:270px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u284 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u284_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u285_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u285 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:263px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u285 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u285_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u286_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u286 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:291px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u286 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u286_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u287_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:329px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u287 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:263px;
  width:329px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u287 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u287_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u288_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u288 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:319px;
  width:470px;
  height:2px;
  display:flex;
}
#u288 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u288_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u289_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u289 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:295px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u289 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u289_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u290_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u290 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:291px;
  width:201px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u290 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u290_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u292 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u293_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:479px;
  height:80px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u293 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:479px;
  height:80px;
  display:flex;
}
#u293 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u293_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u294_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u294 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:48px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u294 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u294_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u295_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u295 {
  border-width:0px;
  position:absolute;
  left:92px;
  top:47px;
  width:19px;
  height:25px;
  display:flex;
}
#u295 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u295_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u296_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u296 {
  border-width:0px;
  position:absolute;
  left:257px;
  top:48px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u296 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u296_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u297_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u297 {
  border-width:0px;
  position:absolute;
  left:308px;
  top:47px;
  width:19px;
  height:25px;
  display:flex;
}
#u297 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u297_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u298_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u298 {
  border-width:0px;
  position:absolute;
  left:152px;
  top:48px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u298 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u298_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u299_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u299 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:47px;
  width:19px;
  height:25px;
  display:flex;
}
#u299 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u299_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u300 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u301_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:425px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(201, 201, 201, 1);
  border-radius:75px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u301 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:7px;
  width:425px;
  height:34px;
  display:flex;
}
#u301 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u301_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u302_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:14px;
}
#u302 {
  border-width:0px;
  position:absolute;
  left:39px;
  top:17px;
  width:16px;
  height:14px;
  display:flex;
}
#u302 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u302_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u303_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:372px;
  height:29px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u303 {
  border-width:0px;
  position:absolute;
  left:62px;
  top:10px;
  width:372px;
  height:29px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u303 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u303_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u304_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:17px;
}
#u304 {
  border-width:0px;
  position:absolute;
  left:424px;
  top:16px;
  width:19px;
  height:17px;
  display:flex;
}
#u304 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u304_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u305_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u305 {
  border-width:0px;
  position:absolute;
  left:364px;
  top:49px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u305 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u305_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u306_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u306 {
  border-width:0px;
  position:absolute;
  left:413px;
  top:48px;
  width:19px;
  height:25px;
  display:flex;
}
#u306 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u306_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u308_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u308 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:680px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u308 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u308_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u309_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u309 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:673px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u309 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u309_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u310_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u310 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:701px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u310 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u310_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u311_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:329px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u311 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:673px;
  width:329px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u311 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u311_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u312_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u312 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:729px;
  width:470px;
  height:2px;
  display:flex;
}
#u312 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u312_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u313_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u313 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:705px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u313 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u313_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u314_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u314 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:701px;
  width:201px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u314 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u314_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u316_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u316 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:598px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u316 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u316_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u317_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u317 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:591px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u317 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u317_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u318_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u318 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:619px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u318 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u318_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u319_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:328px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u319 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:591px;
  width:328px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u319 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u319_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u320_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u320 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:647px;
  width:470px;
  height:2px;
  display:flex;
}
#u320 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u320_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u321_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u321 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:621px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u321 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u321_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u322_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u322 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:619px;
  width:200px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u322 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u322_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u324_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u324 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:844px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u324 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u324_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u325_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u325 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:837px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u325 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u325_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u326_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u326 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:865px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u326 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u326_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u327_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:329px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u327 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:837px;
  width:329px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u327 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u327_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u328_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u328 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:893px;
  width:470px;
  height:2px;
  display:flex;
}
#u328 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u328_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u329_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u329 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:869px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u329 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u329_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u330_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u330 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:865px;
  width:201px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u330 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u330_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u332_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u332 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:762px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u332 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u332_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u333_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u333 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:755px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u333 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u333_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u334_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u334 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:783px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u334 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u334_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u335_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:328px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u335 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:755px;
  width:328px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u335 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u335_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u336_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u336 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:811px;
  width:470px;
  height:2px;
  display:flex;
}
#u336 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u336_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u337_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u337 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:785px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u337 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u337_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u338_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u338 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:783px;
  width:200px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u338 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u338_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u240_state1 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:488px;
  height:527px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u240_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u340_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u340 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:106px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u340 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u340_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u341_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u341 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:99px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u341 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u341_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u342_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u342 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:127px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u342 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u342_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u343_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:329px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u343 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:99px;
  width:329px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u343 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u343_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u344_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u344 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:155px;
  width:470px;
  height:2px;
  display:flex;
}
#u344 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u344_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u345_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u345 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:131px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u345 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u345_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u346_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u346 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:127px;
  width:201px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u346 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u346_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u348 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u349_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:479px;
  height:80px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u349 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:479px;
  height:80px;
  display:flex;
}
#u349 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u349_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u350_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u350 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:48px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u350 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u350_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u351_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u351 {
  border-width:0px;
  position:absolute;
  left:92px;
  top:47px;
  width:19px;
  height:25px;
  display:flex;
}
#u351 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u351_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u352_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u352 {
  border-width:0px;
  position:absolute;
  left:257px;
  top:48px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u352 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u352_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u353_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u353 {
  border-width:0px;
  position:absolute;
  left:308px;
  top:47px;
  width:19px;
  height:25px;
  display:flex;
}
#u353 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u353_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u354_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u354 {
  border-width:0px;
  position:absolute;
  left:152px;
  top:48px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u354 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u354_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u355_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u355 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:47px;
  width:19px;
  height:25px;
  display:flex;
}
#u355 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u355_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u356 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u357_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:425px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(201, 201, 201, 1);
  border-radius:75px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u357 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:7px;
  width:425px;
  height:34px;
  display:flex;
}
#u357 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u357_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u358_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:14px;
}
#u358 {
  border-width:0px;
  position:absolute;
  left:39px;
  top:17px;
  width:16px;
  height:14px;
  display:flex;
}
#u358 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u358_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u359_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:372px;
  height:29px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u359 {
  border-width:0px;
  position:absolute;
  left:62px;
  top:10px;
  width:372px;
  height:29px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u359 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u359_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u360_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:17px;
}
#u360 {
  border-width:0px;
  position:absolute;
  left:424px;
  top:16px;
  width:19px;
  height:17px;
  display:flex;
}
#u360 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u360_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u361_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u361 {
  border-width:0px;
  position:absolute;
  left:364px;
  top:49px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u361 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u361_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u362_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u362 {
  border-width:0px;
  position:absolute;
  left:413px;
  top:48px;
  width:19px;
  height:25px;
  display:flex;
}
#u362 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u362_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u240_state2 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:488px;
  height:527px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u240_state2_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u364 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u365_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:479px;
  height:80px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u365 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:479px;
  height:80px;
  display:flex;
}
#u365 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u365_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u366_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u366 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:48px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u366 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u366_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u367_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u367 {
  border-width:0px;
  position:absolute;
  left:92px;
  top:47px;
  width:19px;
  height:25px;
  display:flex;
}
#u367 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u367_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u368_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u368 {
  border-width:0px;
  position:absolute;
  left:257px;
  top:48px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u368 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u368_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u369_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u369 {
  border-width:0px;
  position:absolute;
  left:308px;
  top:47px;
  width:19px;
  height:25px;
  display:flex;
}
#u369 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u369_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u370_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u370 {
  border-width:0px;
  position:absolute;
  left:152px;
  top:48px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u370 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u370_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u371_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u371 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:47px;
  width:19px;
  height:25px;
  display:flex;
}
#u371 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u371_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u372 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u373_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:425px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(201, 201, 201, 1);
  border-radius:75px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u373 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:7px;
  width:425px;
  height:34px;
  display:flex;
}
#u373 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u373_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u374_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:14px;
}
#u374 {
  border-width:0px;
  position:absolute;
  left:39px;
  top:17px;
  width:16px;
  height:14px;
  display:flex;
}
#u374 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u374_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u375_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:372px;
  height:29px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u375 {
  border-width:0px;
  position:absolute;
  left:62px;
  top:10px;
  width:372px;
  height:29px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u375 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u375_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u376_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:17px;
}
#u376 {
  border-width:0px;
  position:absolute;
  left:424px;
  top:16px;
  width:19px;
  height:17px;
  display:flex;
}
#u376 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u376_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u377_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u377 {
  border-width:0px;
  position:absolute;
  left:364px;
  top:49px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u377 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u377_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u378_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u378 {
  border-width:0px;
  position:absolute;
  left:413px;
  top:48px;
  width:19px;
  height:25px;
  display:flex;
}
#u378 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u378_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u380_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u380 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:111px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u380 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u380_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u381_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u381 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:104px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u381 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u381_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u382_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u382 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:132px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u382 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u382_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u383_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:328px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u383 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:104px;
  width:328px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u383 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u383_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u384_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u384 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:160px;
  width:470px;
  height:2px;
  display:flex;
}
#u384 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u384_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u385_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u385 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:134px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u385 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u385_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u386_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u386 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:132px;
  width:200px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u386 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u386_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u240_state3 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:488px;
  height:527px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u240_state3_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u388 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u389_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:479px;
  height:80px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u389 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:479px;
  height:80px;
  display:flex;
}
#u389 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u389_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u390_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u390 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:48px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u390 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u390_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u391_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u391 {
  border-width:0px;
  position:absolute;
  left:92px;
  top:47px;
  width:19px;
  height:25px;
  display:flex;
}
#u391 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u391_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u392_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u392 {
  border-width:0px;
  position:absolute;
  left:257px;
  top:48px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u392 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u392_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u393_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u393 {
  border-width:0px;
  position:absolute;
  left:308px;
  top:47px;
  width:19px;
  height:25px;
  display:flex;
}
#u393 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u393_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u394_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u394 {
  border-width:0px;
  position:absolute;
  left:152px;
  top:48px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u394 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u394_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u395_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u395 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:47px;
  width:19px;
  height:25px;
  display:flex;
}
#u395 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u395_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u396 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u397_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:425px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(201, 201, 201, 1);
  border-radius:75px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u397 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:7px;
  width:425px;
  height:34px;
  display:flex;
}
#u397 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u397_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u398_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:14px;
}
#u398 {
  border-width:0px;
  position:absolute;
  left:39px;
  top:17px;
  width:16px;
  height:14px;
  display:flex;
}
#u398 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u398_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u399_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:372px;
  height:29px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u399 {
  border-width:0px;
  position:absolute;
  left:62px;
  top:10px;
  width:372px;
  height:29px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u399 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u399_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u400_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:17px;
}
#u400 {
  border-width:0px;
  position:absolute;
  left:424px;
  top:16px;
  width:19px;
  height:17px;
  display:flex;
}
#u400 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u400_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u401_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u401 {
  border-width:0px;
  position:absolute;
  left:364px;
  top:49px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u401 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u401_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u402_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u402 {
  border-width:0px;
  position:absolute;
  left:413px;
  top:48px;
  width:19px;
  height:25px;
  display:flex;
}
#u402 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u402_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u404_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u404 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:193px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u404 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u404_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u405_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u405 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:186px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u405 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u405_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u406_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u406 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:214px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u406 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u406_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u407_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:329px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u407 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:186px;
  width:329px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u407 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u407_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u408_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u408 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:242px;
  width:470px;
  height:2px;
  display:flex;
}
#u408 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u408_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u409_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u409 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:218px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u409 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u409_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u410_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u410 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:214px;
  width:201px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u410 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u410_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u412_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u412 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:111px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u412 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u412_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u413_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u413 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:104px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u413 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u413_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u414_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u414 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:132px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u414 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u414_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u415_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:329px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u415 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:104px;
  width:329px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u415 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u415_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u416_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u416 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:160px;
  width:470px;
  height:2px;
  display:flex;
}
#u416 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u416_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u417_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u417 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:136px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u417 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u417_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u418_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u418 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:132px;
  width:201px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u418 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u418_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u420_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u420 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:275px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u420 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u420_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u421_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u421 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:268px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u421 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u421_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u422_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u422 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:296px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u422 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u422_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u423_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:329px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u423 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:268px;
  width:329px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u423 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u423_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u424_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u424 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:324px;
  width:470px;
  height:2px;
  display:flex;
}
#u424 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u424_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u425_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u425 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:300px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u425 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u425_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u426_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u426 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:296px;
  width:201px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u426 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u426_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u428_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u428 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:357px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u428 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u428_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u429_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u429 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:350px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#D9001B;
  text-align:right;
}
#u429 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u429_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u430_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u430 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:378px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u430 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u430_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u431_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:329px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u431 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:350px;
  width:329px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u431 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u431_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u432_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u432 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:406px;
  width:470px;
  height:2px;
  display:flex;
}
#u432 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u432_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u433_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u433 {
  border-width:0px;
  position:absolute;
  left:332px;
  top:382px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u433 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u433_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u434_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u434 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:378px;
  width:201px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u434 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u434_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u240_state4 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:488px;
  height:527px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u240_state4_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u436 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u437_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:479px;
  height:80px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u437 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:479px;
  height:80px;
  display:flex;
}
#u437 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u437_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u438_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u438 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:48px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u438 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u438_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u439_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u439 {
  border-width:0px;
  position:absolute;
  left:92px;
  top:47px;
  width:19px;
  height:25px;
  display:flex;
}
#u439 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u439_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u440_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u440 {
  border-width:0px;
  position:absolute;
  left:257px;
  top:48px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u440 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u440_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u441_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u441 {
  border-width:0px;
  position:absolute;
  left:308px;
  top:47px;
  width:19px;
  height:25px;
  display:flex;
}
#u441 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u441_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u442_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u442 {
  border-width:0px;
  position:absolute;
  left:152px;
  top:48px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u442 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u442_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u443_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u443 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:47px;
  width:19px;
  height:25px;
  display:flex;
}
#u443 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u443_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u444 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u445_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:425px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(201, 201, 201, 1);
  border-radius:75px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u445 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:7px;
  width:425px;
  height:34px;
  display:flex;
}
#u445 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u445_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u446_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:14px;
}
#u446 {
  border-width:0px;
  position:absolute;
  left:39px;
  top:17px;
  width:16px;
  height:14px;
  display:flex;
}
#u446 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u446_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u447_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:372px;
  height:29px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u447 {
  border-width:0px;
  position:absolute;
  left:62px;
  top:10px;
  width:372px;
  height:29px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u447 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u447_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u448_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:17px;
}
#u448 {
  border-width:0px;
  position:absolute;
  left:424px;
  top:16px;
  width:19px;
  height:17px;
  display:flex;
}
#u448 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u448_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u449_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u449 {
  border-width:0px;
  position:absolute;
  left:364px;
  top:49px;
  width:32px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u449 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u449_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u450_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:19px;
  height:25px;
}
#u450 {
  border-width:0px;
  position:absolute;
  left:413px;
  top:48px;
  width:19px;
  height:25px;
  display:flex;
}
#u450 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u450_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u452_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u452 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:193px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u452 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u452_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u453_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u453 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:186px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u453 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u453_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u454_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u454 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:214px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u454 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u454_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u455_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:328px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u455 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:186px;
  width:328px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u455 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u455_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u456_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u456 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:242px;
  width:470px;
  height:2px;
  display:flex;
}
#u456 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u456_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u457_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u457 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:216px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u457 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u457_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u458_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u458 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:214px;
  width:200px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u458 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u458_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u460_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u460 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:111px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u460 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u460_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u461_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u461 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:104px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u461 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u461_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u462_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u462 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:132px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u462 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u462_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u463_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:328px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u463 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:104px;
  width:328px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u463 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u463_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u464_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u464 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:160px;
  width:470px;
  height:2px;
  display:flex;
}
#u464 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u464_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u465_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u465 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:134px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u465 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u465_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u466_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u466 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:132px;
  width:200px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u466 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u466_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u468_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u468 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:275px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u468 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u468_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u469_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u469 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:268px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u469 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u469_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u470_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u470 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:296px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u470 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u470_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u471_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:328px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u471 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:268px;
  width:328px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u471 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u471_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u472_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u472 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:324px;
  width:470px;
  height:2px;
  display:flex;
}
#u472 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u472_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u473_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u473 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:298px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u473 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u473_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u474_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u474 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:296px;
  width:200px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u474 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u474_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u476_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:30px;
}
#u476 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:357px;
  width:29px;
  height:30px;
  display:flex;
  text-align:left;
}
#u476 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u476_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u477_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u477 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:350px;
  width:138px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u477 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u477_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u478_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u478 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:378px;
  width:122px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u478 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u478_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u479_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:328px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u479 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:350px;
  width:328px;
  height:19px;
  display:flex;
  font-size:14px;
}
#u479 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u479_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u480_img {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:473px;
  height:5px;
}
#u480 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:406px;
  width:470px;
  height:2px;
  display:flex;
}
#u480 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u480_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u481_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u481 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:380px;
  width:138px;
  height:14px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u481 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u481_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u482_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u482 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:378px;
  width:200px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u482 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u482_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u483_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:207px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#C280FF;
  text-align:right;
}
#u483 {
  border-width:0px;
  position:absolute;
  left:277px;
  top:253px;
  width:207px;
  height:22px;
  display:flex;
  font-size:12px;
  color:#C280FF;
  text-align:right;
}
#u483 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u483_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u485_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:900px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:50px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u485 {
  border-width:0px;
  position:absolute;
  left:626px;
  top:0px;
  width:510px;
  height:900px;
  display:flex;
  opacity:0.49;
}
#u485 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u485_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u486_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u486 {
  border-width:0px;
  position:absolute;
  left:648px;
  top:20px;
  width:51px;
  height:40px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u486 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u486_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u487_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:18px;
}
#u487 {
  border-width:0px;
  position:absolute;
  left:1051px;
  top:19px;
  width:23px;
  height:18px;
  display:flex;
}
#u487 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u487_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u488_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:16px;
}
#u488 {
  border-width:0px;
  position:absolute;
  left:1088px;
  top:21px;
  width:26px;
  height:16px;
  display:flex;
}
#u488 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u488_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u489_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u489 {
  border-width:0px;
  position:absolute;
  left:726px;
  top:50px;
  width:300px;
  height:25px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u489 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u489_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u490 {
  position:fixed;
  left:50%;
  margin-left:-75px;
  top:50%;
  margin-top:-25px;
  visibility:hidden;
}
#u490_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u490_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u491_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u491 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u491 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u491_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u490_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u490_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u492_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u492 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u492 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u492_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u493_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u493 {
  border-width:0px;
  position:absolute;
  left:60px;
  top:16px;
  width:80px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u493 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u493_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u494_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u494 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:10px;
  width:30px;
  height:30px;
  display:flex;
}
#u494 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u494_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u495_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u495 {
  border-width:0px;
  position:absolute;
  left:1089px;
  top:50px;
  width:25px;
  height:25px;
  display:flex;
}
#u495 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u495_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u496_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:25px;
}
#u496 {
  border-width:0px;
  position:absolute;
  left:648px;
  top:50px;
  width:15px;
  height:25px;
  display:flex;
}
#u496 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u496_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u497_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:228px;
  height:11px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  text-align:center;
}
#u497 {
  border-width:0px;
  position:absolute;
  left:762px;
  top:71px;
  width:228px;
  height:11px;
  display:flex;
  font-size:10px;
  text-align:center;
}
#u497 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u497_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u498_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:486px;
  height:172px;
  background:inherit;
  background-color:rgba(255, 204, 255, 1);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#AEAEAE;
  text-align:left;
}
#u498 {
  border-width:0px;
  position:absolute;
  left:638px;
  top:103px;
  width:486px;
  height:172px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#AEAEAE;
  text-align:left;
}
#u498 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u498_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u499_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:216px;
  height:31px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:28px;
  color:#000000;
  text-align:center;
}
#u499 {
  border-width:0px;
  position:absolute;
  left:773px;
  top:166px;
  width:216px;
  height:31px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:28px;
  color:#000000;
  text-align:center;
}
#u499 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u499_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u500_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:207px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#C280FF;
  text-align:right;
}
#u500 {
  border-width:0px;
  position:absolute;
  left:906px;
  top:253px;
  width:207px;
  height:22px;
  display:flex;
  font-size:12px;
  color:#C280FF;
  text-align:right;
}
#u500 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u500_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u501_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:722px;
  height:507px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u501 {
  border-width:0px;
  position:absolute;
  left:1203px;
  top:588px;
  width:722px;
  height:507px;
  display:flex;
}
#u501 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u501_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u502_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:216px;
  height:31px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  color:#000000;
}
#u502 {
  border-width:0px;
  position:absolute;
  left:1203px;
  top:875px;
  width:216px;
  height:31px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  color:#000000;
}
#u502 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u502_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u503_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:608px;
  height:278px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u503 {
  border-width:0px;
  position:absolute;
  left:1221px;
  top:17px;
  width:608px;
  height:278px;
  display:flex;
}
#u503 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u503_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u504_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:608px;
  height:218px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u504 {
  border-width:0px;
  position:absolute;
  left:1221px;
  top:304px;
  width:608px;
  height:218px;
  display:flex;
}
#u504 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u504_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u505_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:608px;
  height:413px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u505 {
  border-width:0px;
  position:absolute;
  left:1203px;
  top:1061px;
  width:608px;
  height:413px;
  display:flex;
}
#u505 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u505_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u506_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:795px;
  height:609px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u506 {
  border-width:0px;
  position:absolute;
  left:1203px;
  top:1460px;
  width:795px;
  height:609px;
  display:flex;
}
#u506 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u506_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u507_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:795px;
  height:333px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u507 {
  border-width:0px;
  position:absolute;
  left:1203px;
  top:2078px;
  width:795px;
  height:333px;
  display:flex;
}
#u507 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u507_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u508_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:351px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u508 {
  border-width:0px;
  position:absolute;
  left:60px;
  top:-51px;
  width:351px;
  height:32px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u508 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u508_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
