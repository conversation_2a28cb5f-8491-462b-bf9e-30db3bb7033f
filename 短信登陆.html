﻿<!DOCTYPE html>
<html>
  <head>
    <title>短信登陆</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/短信登陆/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/短信登陆/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (基础app框架(H5)) -->

      <!-- Unnamed (矩形) -->
      <div id="u2639" class="ax_default box_1">
        <div id="u2639_div" class=""></div>
        <div id="u2639_text" class="text ">
          <p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2640" class="ax_default _二级标题">
        <div id="u2640_div" class=""></div>
        <div id="u2640_text" class="text ">
          <p><span>16:18</span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u2641" class="ax_default icon">
        <img id="u2641_img" class="img " src="images/海融宝签约_个人__f501_f502_/u3.svg"/>
        <div id="u2641_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u2642" class="ax_default icon">
        <img id="u2642_img" class="img " src="images/海融宝签约_个人__f501_f502_/u4.svg"/>
        <div id="u2642_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2643" class="ax_default _文本段落">
        <div id="u2643_div" class=""></div>
        <div id="u2643_text" class="text ">
          <p><span>首页</span></p>
        </div>
      </div>

      <!-- 操作状态 (动态面板) -->
      <div id="u2644" class="ax_default ax_default_hidden" data-label="操作状态" style="display:none; visibility: hidden">
        <div id="u2644_state0" class="panel_state" data-label="操作成功" style="">
          <div id="u2644_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u2645" class="ax_default box_3">
              <div id="u2645_div" class=""></div>
              <div id="u2645_text" class="text ">
                <p><span>操作成功！</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u2644_state1" class="panel_state" data-label="操作失败" style="visibility: hidden;">
          <div id="u2644_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u2646" class="ax_default box_3">
              <div id="u2646_div" class=""></div>
              <div id="u2646_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2647" class="ax_default _文本段落">
              <div id="u2647_div" class=""></div>
              <div id="u2647_text" class="text ">
                <p><span>操作失败！</span></p>
              </div>
            </div>

            <!-- Unnamed (图片 ) -->
            <div id="u2648" class="ax_default _图片_">
              <img id="u2648_img" class="img " src="images/海融宝签约_个人__f501_f502_/u10.png"/>
              <div id="u2648_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u2649" class="ax_default _图片_">
        <img id="u2649_img" class="img " src="images/海融宝签约_个人__f501_f502_/u11.png"/>
        <div id="u2649_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u2650" class="ax_default icon">
        <img id="u2650_img" class="img " src="images/海融宝签约_个人__f501_f502_/u12.svg"/>
        <div id="u2650_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2651" class="ax_default _文本段落">
        <div id="u2651_div" class=""></div>
        <div id="u2651_text" class="text ">
          <p><span>hrypt.com</span></p>
        </div>
      </div>
      <div id="u2638" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (矩形) -->
      <div id="u2652" class="ax_default _二级标题">
        <div id="u2652_div" class=""></div>
        <div id="u2652_text" class="text ">
          <p><span>16:18</span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u2653" class="ax_default icon">
        <img id="u2653_img" class="img " src="images/海融宝签约_个人__f501_f502_/u3.svg"/>
        <div id="u2653_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u2654" class="ax_default icon">
        <img id="u2654_img" class="img " src="images/海融宝签约_个人__f501_f502_/u4.svg"/>
        <div id="u2654_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (项目logo) -->

      <!-- Unnamed (图片 ) -->
      <div id="u2656" class="ax_default _图片_">
        <img id="u2656_img" class="img " src="images/登陆主界面/u2620.svg"/>
        <div id="u2656_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2657" class="ax_default _文本段落">
        <div id="u2657_div" class=""></div>
        <div id="u2657_text" class="text ">
          <p><span>海融宝清算平台</span></p>
        </div>
      </div>
      <div id="u2655" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (矩形) -->
      <div id="u2658" class="ax_default _一级标题">
        <div id="u2658_div" class=""></div>
        <div id="u2658_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2659" class="ax_default _文本段落">
        <div id="u2659_div" class=""></div>
        <div id="u2659_text" class="text ">
          <p><span style="color:#999999;">首次登录会自动进行注册，注册即为同意</span><span style="color:#33CC00;">《用户协议》《隐私政策》</span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u2660" class="ax_default icon">
        <img id="u2660_img" class="img " src="images/登陆主界面/u2624.svg"/>
        <div id="u2660_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2661" class="ax_default box_1">
        <div id="u2661_div" class=""></div>
        <div id="u2661_text" class="text ">
          <p><span>&nbsp; 请输入手机号</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2662" class="ax_default box_1">
        <div id="u2662_div" class=""></div>
        <div id="u2662_text" class="text ">
          <p><span>&nbsp; 输入验证码</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2663" class="ax_default _文本段落">
        <div id="u2663_div" class=""></div>
        <div id="u2663_text" class="text ">
          <p><span>注册登记&gt;</span></p>
        </div>
      </div>

      <!-- 错误提示一 (组合) -->
      <div id="u2664" class="ax_default ax_default_hidden" data-label="错误提示一" style="display:none; visibility: hidden" data-left="164" data-top="249" data-width="200" data-height="138">

        <!-- Unnamed (矩形) -->
        <div id="u2665" class="ax_default box_1">
          <div id="u2665_div" class=""></div>
          <div id="u2665_text" class="text ">
            <p><span>1、账号不存在</span></p><p><span>2、密码错误</span></p><p><span>3、网络错误</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2666" class="ax_default box_1">
          <div id="u2666_div" class=""></div>
          <div id="u2666_text" class="text ">
            <p><span>账号不存在</span></p>
          </div>
        </div>

        <!-- Unnamed (文本框) -->
        <div id="u2667" class="ax_default _文本框">
          <div id="u2667_div" class=""></div>
          <input id="u2667_input" type="text" value="提示" class="u2667_input"/>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2668" class="ax_default primary_button">
          <div id="u2668_div" class=""></div>
          <div id="u2668_text" class="text ">
            <p><span>确认</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2669" class="ax_default primary_button">
        <div id="u2669_div" class=""></div>
        <div id="u2669_text" class="text ">
          <p><span>登录</span></p>
        </div>
      </div>

      <!-- 叫号面板按钮 (动态面板) -->
      <div id="u2670" class="ax_default" data-label="叫号面板按钮">
        <div id="u2670_state0" class="panel_state" data-label="State1" style="">
          <div id="u2670_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u2671" class="ax_default box_3">
              <div id="u2671_div" class=""></div>
              <div id="u2671_text" class="text ">
                <p><span>获取验证码</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u2670_state1" class="panel_state" data-label="State2" style="visibility: hidden;">
          <div id="u2670_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u2672" class="ax_default box_3">
              <div id="u2672_div" class=""></div>
              <div id="u2672_text" class="text ">
                <p><span>s</span></p>
              </div>
            </div>

            <!-- 叫号倒计时 (文本框) -->
            <div id="u2673" class="ax_default text_field ax_default_hidden" data-label="叫号倒计时" style="display:none; visibility: hidden">
              <div id="u2673_div" class=""></div>
              <input id="u2673_input" type="text" value="15" class="u2673_input" readonly/>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
