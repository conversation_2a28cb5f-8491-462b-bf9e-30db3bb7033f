﻿<!DOCTYPE html>
<html>
  <head>
    <title>图片修改</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/图片修改/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/图片修改/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u5399" class="ax_default box_1">
        <div id="u5399_div" class=""></div>
        <div id="u5399_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5400" class="ax_default _形状">
        <div id="u5400_div" class=""></div>
        <div id="u5400_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u5401" class="ax_default _图片_">
        <img id="u5401_img" class="img " src="images/充值方式/u1461.png"/>
        <div id="u5401_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u5402" class="ax_default _文本段落">
        <img id="u5402_img" class="img " src="images/充值方式/u1462.svg"/>
        <div id="u5402_text" class="text ">
          <p><span>图片修改</span></p>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u5403" class="ax_default line">
        <img id="u5403_img" class="img " src="images/充值方式/u1463.svg"/>
        <div id="u5403_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u5404" class="ax_default" data-left="48" data-top="773" data-width="404" data-height="41">

        <!-- Unnamed (矩形) -->
        <div id="u5405" class="ax_default box_1">
          <div id="u5405_div" class=""></div>
          <div id="u5405_text" class="text ">
            <p><span>取消</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u5406" class="ax_default box_1">
          <div id="u5406_div" class=""></div>
          <div id="u5406_text" class="text ">
            <p><span>确定</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5407" class="ax_default box_1">
        <div id="u5407_div" class=""></div>
        <div id="u5407_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5408" class="ax_default _文本段落">
        <div id="u5408_div" class=""></div>
        <div id="u5408_text" class="text ">
          <p><span>选择新图片</span></p>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u5409" class="ax_default _图片_">
        <img id="u5409_img" class="img " src="resources/images/transparent.gif"/>
        <div id="u5409_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- 弹出选图 (动态面板) -->
      <div id="u5410" class="ax_default ax_default_hidden" data-label="弹出选图" style="display:none; visibility: hidden">
        <div id="u5410_state0" class="panel_state" data-label="选择类别" style="">
          <div id="u5410_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u5411" class="ax_default box_1">
              <div id="u5411_div" class=""></div>
              <div id="u5411_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5412" class="ax_default _二级标题">
              <div id="u5412_div" class=""></div>
              <div id="u5412_text" class="text ">
                <p><span>×</span></p>
              </div>
            </div>

            <!-- Unnamed (线段) -->
            <div id="u5413" class="ax_default line">
              <img id="u5413_img" class="img " src="images/图片修改/u5413.svg"/>
              <div id="u5413_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5414" class="ax_default _文本段落">
              <div id="u5414_div" class=""></div>
              <div id="u5414_text" class="text ">
                <p><span>拍照/拍视频</span></p>
              </div>
            </div>

            <!-- Unnamed (线段) -->
            <div id="u5415" class="ax_default line">
              <img id="u5415_img" class="img " src="images/图片修改/u5413.svg"/>
              <div id="u5415_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5416" class="ax_default _文本段落">
              <div id="u5416_div" class=""></div>
              <div id="u5416_text" class="text ">
                <p><span>从相册选取</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5417" class="ax_default primary_button">
              <div id="u5417_div" class=""></div>
              <div id="u5417_text" class="text ">
                <p><span>取消</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
