﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-100px;
  width:1134px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u5344_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:899px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.298039215686275);
  border:none;
  border-radius:50px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u5344 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:94px;
  width:510px;
  height:899px;
  display:flex;
}
#u5344 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5344_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5345_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:198px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u5345 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:402px;
  width:510px;
  height:198px;
  display:flex;
}
#u5345 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5345_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5346_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:287px;
  height:29px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:28px;
  color:#999999;
}
#u5346 {
  border-width:0px;
  position:absolute;
  left:199px;
  top:413px;
  width:287px;
  height:29px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:28px;
  color:#999999;
}
#u5346 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5346_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5347_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:421px;
  height:87px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u5347 {
  border-width:0px;
  position:absolute;
  left:145px;
  top:455px;
  width:421px;
  height:87px;
  display:flex;
  opacity:0.28;
  font-size:16px;
}
#u5347 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5347_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5348_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:899px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.298039215686275);
  border:none;
  border-radius:50px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u5348 {
  border-width:0px;
  position:absolute;
  left:724px;
  top:94px;
  width:510px;
  height:899px;
  display:flex;
}
#u5348 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5348_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5349_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:506px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u5349 {
  border-width:0px;
  position:absolute;
  left:724px;
  top:436px;
  width:510px;
  height:506px;
  display:flex;
}
#u5349 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5349_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5350_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:275px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:28px;
  color:#999999;
  text-align:left;
}
#u5350 {
  border-width:0px;
  position:absolute;
  left:737px;
  top:461px;
  width:275px;
  height:28px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:28px;
  color:#999999;
  text-align:left;
}
#u5350 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5350_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5351 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u5352_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:43px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#000000;
  text-align:center;
}
#u5352 {
  border-width:0px;
  position:absolute;
  left:737px;
  top:584px;
  width:68px;
  height:43px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#000000;
  text-align:center;
}
#u5352 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5352_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5353_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:67px;
}
#u5353 {
  border-width:0px;
  position:absolute;
  left:737px;
  top:510px;
  width:68px;
  height:67px;
  display:flex;
  font-size:16px;
}
#u5353 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5353_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5354 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u5355_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:66px;
  height:43px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#000000;
  text-align:center;
}
#u5355 {
  border-width:0px;
  position:absolute;
  left:843px;
  top:584px;
  width:66px;
  height:43px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#000000;
  text-align:center;
}
#u5355 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5355_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5356_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:67px;
}
#u5356 {
  border-width:0px;
  position:absolute;
  left:842px;
  top:510px;
  width:68px;
  height:67px;
  display:flex;
  font-size:16px;
}
#u5356 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5356_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5357 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u5358_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:43px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#000000;
  text-align:center;
}
#u5358 {
  border-width:0px;
  position:absolute;
  left:945px;
  top:584px;
  width:68px;
  height:43px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#000000;
  text-align:center;
}
#u5358 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5358_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5359_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:67px;
}
#u5359 {
  border-width:0px;
  position:absolute;
  left:945px;
  top:510px;
  width:68px;
  height:67px;
  display:flex;
  font-size:16px;
}
#u5359 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5359_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5360 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u5361 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u5362_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:67px;
}
#u5362 {
  border-width:0px;
  position:absolute;
  left:737px;
  top:638px;
  width:68px;
  height:67px;
  display:flex;
  font-size:16px;
}
#u5362 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5362_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5363_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:41px;
  height:37px;
}
#u5363 {
  border-width:0px;
  position:absolute;
  left:751px;
  top:652px;
  width:41px;
  height:37px;
  display:flex;
  font-size:16px;
}
#u5363 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5363_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5364_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:43px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#000000;
  text-align:center;
}
#u5364 {
  border-width:0px;
  position:absolute;
  left:737px;
  top:711px;
  width:68px;
  height:43px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#000000;
  text-align:center;
}
#u5364 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5364_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5365 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u5366 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u5367_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:67px;
}
#u5367 {
  border-width:0px;
  position:absolute;
  left:842px;
  top:638px;
  width:68px;
  height:67px;
  display:flex;
  font-size:16px;
}
#u5367 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5367_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5368_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:34px;
  height:27px;
}
#u5368 {
  border-width:0px;
  position:absolute;
  left:859px;
  top:658px;
  width:34px;
  height:27px;
  display:flex;
  font-size:16px;
}
#u5368 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5368_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5369_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:43px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#000000;
  text-align:center;
}
#u5369 {
  border-width:0px;
  position:absolute;
  left:842px;
  top:711px;
  width:68px;
  height:43px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#000000;
  text-align:center;
}
#u5369 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5369_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5370 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u5371_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:43px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#000000;
  text-align:center;
}
#u5371 {
  border-width:0px;
  position:absolute;
  left:1049px;
  top:584px;
  width:68px;
  height:43px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#000000;
  text-align:center;
}
#u5371 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5371_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5372_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:67px;
}
#u5372 {
  border-width:0px;
  position:absolute;
  left:1049px;
  top:510px;
  width:68px;
  height:67px;
  display:flex;
  font-size:16px;
}
#u5372 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5372_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5373 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u5374_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:43px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#000000;
  text-align:center;
}
#u5374 {
  border-width:0px;
  position:absolute;
  left:1153px;
  top:584px;
  width:68px;
  height:43px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#000000;
  text-align:center;
}
#u5374 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5374_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5375_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:67px;
}
#u5375 {
  border-width:0px;
  position:absolute;
  left:1153px;
  top:510px;
  width:68px;
  height:67px;
  display:flex;
  font-size:16px;
}
#u5375 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5375_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5376 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u5377_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u5377 {
  border-width:0px;
  position:absolute;
  left:216px;
  top:520px;
  width:57px;
  height:19px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u5377 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5377_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5378_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:55px;
}
#u5378 {
  border-width:0px;
  position:absolute;
  left:216px;
  top:465px;
  width:54px;
  height:55px;
  display:flex;
}
#u5378 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5378_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5379_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:58px;
  height:59px;
}
#u5379 {
  border-width:0px;
  position:absolute;
  left:330px;
  top:463px;
  width:58px;
  height:59px;
  display:flex;
}
#u5379 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5379_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5380_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:49px;
}
#u5380 {
  border-width:0px;
  position:absolute;
  left:460px;
  top:468px;
  width:50px;
  height:49px;
  display:flex;
}
#u5380 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5380_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5381_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:23px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u5381 {
  border-width:0px;
  position:absolute;
  left:330px;
  top:520px;
  width:57px;
  height:23px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u5381 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5381_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5382_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:76px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u5382 {
  border-width:0px;
  position:absolute;
  left:447px;
  top:520px;
  width:76px;
  height:19px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
  text-align:center;
}
#u5382 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5382_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5383_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u5383 {
  border-width:0px;
  position:absolute;
  left:1196px;
  top:450px;
  width:25px;
  height:25px;
  display:flex;
}
#u5383 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5383_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
