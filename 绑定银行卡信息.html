﻿<!DOCTYPE html>
<html>
  <head>
    <title>绑定银行卡信息</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/绑定银行卡信息/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/绑定银行卡信息/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (基础app框架(H5)) -->

      <!-- Unnamed (矩形) -->
      <div id="u2575" class="ax_default box_1">
        <div id="u2575_div" class=""></div>
        <div id="u2575_text" class="text ">
          <p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2576" class="ax_default _二级标题">
        <div id="u2576_div" class=""></div>
        <div id="u2576_text" class="text ">
          <p><span>16:18</span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u2577" class="ax_default icon">
        <img id="u2577_img" class="img " src="images/海融宝签约_个人__f501_f502_/u3.svg"/>
        <div id="u2577_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u2578" class="ax_default icon">
        <img id="u2578_img" class="img " src="images/海融宝签约_个人__f501_f502_/u4.svg"/>
        <div id="u2578_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2579" class="ax_default _文本段落">
        <div id="u2579_div" class=""></div>
        <div id="u2579_text" class="text ">
          <p><span>绑卡银行账户</span></p>
        </div>
      </div>

      <!-- 操作状态 (动态面板) -->
      <div id="u2580" class="ax_default ax_default_hidden" data-label="操作状态" style="display:none; visibility: hidden">
        <div id="u2580_state0" class="panel_state" data-label="操作成功" style="">
          <div id="u2580_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u2581" class="ax_default box_3">
              <div id="u2581_div" class=""></div>
              <div id="u2581_text" class="text ">
                <p><span>操作成功！</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u2580_state1" class="panel_state" data-label="操作失败" style="visibility: hidden;">
          <div id="u2580_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u2582" class="ax_default box_3">
              <div id="u2582_div" class=""></div>
              <div id="u2582_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u2583" class="ax_default _文本段落">
              <div id="u2583_div" class=""></div>
              <div id="u2583_text" class="text ">
                <p><span>操作失败！</span></p>
              </div>
            </div>

            <!-- Unnamed (图片 ) -->
            <div id="u2584" class="ax_default _图片_">
              <img id="u2584_img" class="img " src="images/海融宝签约_个人__f501_f502_/u10.png"/>
              <div id="u2584_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u2585" class="ax_default _图片_">
        <img id="u2585_img" class="img " src="images/海融宝签约_个人__f501_f502_/u11.png"/>
        <div id="u2585_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u2586" class="ax_default icon">
        <img id="u2586_img" class="img " src="images/海融宝签约_个人__f501_f502_/u12.svg"/>
        <div id="u2586_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2587" class="ax_default _文本段落">
        <div id="u2587_div" class=""></div>
        <div id="u2587_text" class="text ">
          <p><span>hrypt.com</span></p>
        </div>
      </div>
      <div id="u2574" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (矩形) -->
      <div id="u2588" class="ax_default box_1">
        <div id="u2588_div" class=""></div>
        <div id="u2588_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u2589" class="ax_default" data-left="31" data-top="142" data-width="458" data-height="33">

        <!-- Unnamed (矩形) -->
        <div id="u2590" class="ax_default _文本段落">
          <div id="u2590_div" class=""></div>
          <div id="u2590_text" class="text ">
            <p><span>中海创科技(福建)集国有限公司</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2591" class="ax_default _文本段落">
          <div id="u2591_div" class=""></div>
          <div id="u2591_text" class="text ">
            <p><span>银行账户：</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2592" class="ax_default primary_button">
          <div id="u2592_div" class=""></div>
          <div id="u2592_text" class="text ">
            <p><span>复制</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u2593" class="ax_default" data-left="31" data-top="174" data-width="458" data-height="28">

        <!-- Unnamed (矩形) -->
        <div id="u2594" class="ax_default _文本段落">
          <div id="u2594_div" class=""></div>
          <div id="u2594_text" class="text ">
            <p><span>935015013000042677</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2595" class="ax_default _文本段落">
          <div id="u2595_div" class=""></div>
          <div id="u2595_text" class="text ">
            <p><span>银行账号：</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2596" class="ax_default primary_button">
          <div id="u2596_div" class=""></div>
          <div id="u2596_text" class="text ">
            <p><span>复制</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u2597" class="ax_default" data-left="31" data-top="208" data-width="458" data-height="28">

        <!-- Unnamed (矩形) -->
        <div id="u2598" class="ax_default _文本段落">
          <div id="u2598_div" class=""></div>
          <div id="u2598_text" class="text ">
            <p><span>403391000103</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2599" class="ax_default _文本段落">
          <div id="u2599_div" class=""></div>
          <div id="u2599_text" class="text ">
            <p><span>银行联行号：</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2600" class="ax_default primary_button">
          <div id="u2600_div" class=""></div>
          <div id="u2600_text" class="text ">
            <p><span>复制</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u2601" class="ax_default" data-left="31" data-top="241" data-width="458" data-height="28">

        <!-- Unnamed (矩形) -->
        <div id="u2602" class="ax_default _文本段落">
          <div id="u2602_div" class=""></div>
          <div id="u2602_text" class="text ">
            <p><span>福建省福州市</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2603" class="ax_default _文本段落">
          <div id="u2603_div" class=""></div>
          <div id="u2603_text" class="text ">
            <p><span>开户地：</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2604" class="ax_default primary_button">
          <div id="u2604_div" class=""></div>
          <div id="u2604_text" class="text ">
            <p><span>复制</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u2605" class="ax_default" data-left="31" data-top="274" data-width="458" data-height="28">

        <!-- Unnamed (矩形) -->
        <div id="u2606" class="ax_default _文本段落">
          <div id="u2606_div" class=""></div>
          <div id="u2606_text" class="text ">
            <p><span>中国邮政储蓄银行股份有限公司</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2607" class="ax_default _文本段落">
          <div id="u2607_div" class=""></div>
          <div id="u2607_text" class="text ">
            <p><span>开户行：</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2608" class="ax_default primary_button">
          <div id="u2608_div" class=""></div>
          <div id="u2608_text" class="text ">
            <p><span>复制</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u2609" class="ax_default" data-left="31" data-top="307" data-width="458" data-height="28">

        <!-- Unnamed (矩形) -->
        <div id="u2610" class="ax_default _文本段落">
          <div id="u2610_div" class=""></div>
          <div id="u2610_text" class="text ">
            <p><span>台江区支行</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2611" class="ax_default _文本段落">
          <div id="u2611_div" class=""></div>
          <div id="u2611_text" class="text ">
            <p><span>开户支行：</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2612" class="ax_default primary_button">
          <div id="u2612_div" class=""></div>
          <div id="u2612_text" class="text ">
            <p><span>复制</span></p>
          </div>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
