﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:510px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u2707_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:896px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:50px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2707 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:896px;
  display:flex;
  opacity:0.49;
}
#u2707 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2707_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2708_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u2708 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:20px;
  width:51px;
  height:40px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u2708 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2708_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2709_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:18px;
}
#u2709 {
  border-width:0px;
  position:absolute;
  left:425px;
  top:19px;
  width:23px;
  height:18px;
  display:flex;
}
#u2709 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2709_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2710_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:16px;
}
#u2710 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:21px;
  width:26px;
  height:16px;
  display:flex;
}
#u2710 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2710_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2711_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u2711 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:49px;
  width:300px;
  height:25px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u2711 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2711_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2712_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:228px;
  height:11px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  text-align:center;
}
#u2712 {
  border-width:0px;
  position:absolute;
  left:136px;
  top:71px;
  width:228px;
  height:11px;
  display:flex;
  font-size:10px;
  text-align:center;
}
#u2712 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2712_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2714_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:400px;
  height:200px;
}
#u2714 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:110px;
  width:400px;
  height:200px;
  display:flex;
}
#u2714 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2714_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2715_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:400px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:28px;
  color:#0000FF;
  text-align:center;
}
#u2715 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:116px;
  width:400px;
  height:32px;
  display:flex;
  font-size:28px;
  color:#0000FF;
  text-align:center;
}
#u2715 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2715_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2716_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:400px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  color:#1296DB;
  text-align:center;
}
#u2716 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:310px;
  width:400px;
  height:50px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  color:#1296DB;
  text-align:center;
}
#u2716 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2716_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2717_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:333px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:150px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  color:#000000;
}
#u2717 {
  border-width:0px;
  position:absolute;
  left:80px;
  top:635px;
  width:333px;
  height:40px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  color:#000000;
}
#u2717 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2717_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2718_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:380px;
  height:67px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u2718 {
  border-width:0px;
  position:absolute;
  left:80px;
  top:713px;
  width:380px;
  height:67px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u2718 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2718_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2719_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u2719 {
  border-width:0px;
  position:absolute;
  left:136px;
  top:645px;
  width:25px;
  height:25px;
  display:flex;
}
#u2719 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2719_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2720_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u2720 {
  border-width:0px;
  position:absolute;
  left:51px;
  top:713px;
  width:20px;
  height:20px;
  display:flex;
}
#u2720 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2720_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2721_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:375px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  color:#999999;
  text-align:left;
}
#u2721 {
  border-width:0px;
  position:absolute;
  left:62px;
  top:387px;
  width:375px;
  height:40px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  color:#999999;
  text-align:left;
}
#u2721 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2721_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2722_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:375px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  color:#999999;
  text-align:left;
}
#u2722 {
  border-width:0px;
  position:absolute;
  left:62px;
  top:449px;
  width:375px;
  height:40px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  color:#999999;
  text-align:left;
}
#u2722 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2722_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2723_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#8400FF;
  text-align:right;
}
#u2723 {
  border-width:0px;
  position:absolute;
  left:337px;
  top:500px;
  width:100px;
  height:25px;
  display:flex;
  font-size:16px;
  color:#8400FF;
  text-align:right;
}
#u2723 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2723_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2724_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:121px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#8400FF;
}
#u2724 {
  border-width:0px;
  position:absolute;
  left:62px;
  top:500px;
  width:121px;
  height:25px;
  display:flex;
  font-size:16px;
  color:#8400FF;
}
#u2724 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2724_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2725_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:333px;
  height:40px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:40px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
}
#u2725 {
  border-width:0px;
  position:absolute;
  left:80px;
  top:582px;
  width:333px;
  height:40px;
  display:flex;
  font-size:20px;
}
#u2725 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2725_div.mouseDown {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:333px;
  height:40px;
  background:inherit;
  background-color:rgba(204, 204, 204, 1);
  border:none;
  border-radius:40px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
}
#u2725.mouseDown {
}
#u2725_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2726_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:67px;
  height:24px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2726 {
  border-width:0px;
  position:absolute;
  left:370px;
  top:455px;
  width:67px;
  height:24px;
  display:flex;
}
#u2726 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2726_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2727 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2728_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:907px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.298039215686275);
  border:none;
  border-radius:50px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2728 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:907px;
  display:flex;
}
#u2728 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2728_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2729 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2730_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:570px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2730 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:465px;
  width:510px;
  height:570px;
  display:flex;
}
#u2730 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2730_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2731_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u2731 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:484px;
  width:25px;
  height:25px;
  display:flex;
}
#u2731 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2731_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2732_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:354px;
  height:25px;
}
#u2732 {
  border-width:0px;
  position:absolute;
  left:71px;
  top:484px;
  width:354px;
  height:25px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u2732 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2732_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2733_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:511px;
  height:2px;
}
#u2733 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:526px;
  width:510px;
  height:1px;
  display:flex;
  font-size:16px;
}
#u2733 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2733_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2734_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:399px;
  height:46px;
  background:inherit;
  background-color:rgba(18, 150, 219, 1);
  border:none;
  border-radius:282px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u2734 {
  border-width:0px;
  position:absolute;
  left:56px;
  top:780px;
  width:399px;
  height:46px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u2734 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2734_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2736_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:448px;
  height:85px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2736 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:554px;
  width:448px;
  height:85px;
  display:flex;
}
#u2736 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2736_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2737_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2737 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:566px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u2737 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2737_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2738 {
  border-width:0px;
  position:absolute;
  left:347px;
  top:566px;
  width:110px;
  height:30px;
}
#u2738_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2738_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u2739_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:111px;
  height:30px;
  background:inherit;
  background-color:rgba(194, 128, 255, 1);
  border:none;
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#000000;
}
#u2739 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:111px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#000000;
}
#u2739 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2739_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2738_state1 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u2738_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u2740_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(128, 128, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u2740 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u2740 .text {
  position:absolute;
  align-self:center;
  padding:2px 20px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2740_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2741_input {
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2741_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2741_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:center;
}
#u2741 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:0px;
  width:60px;
  height:30px;
  display:flex;
  font-size:16px;
  text-align:center;
}
#u2741 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2741_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:center;
}
#u2741.disabled {
}
#u2742_input {
  position:absolute;
  left:0px;
  top:0px;
  width:204px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2742_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:204px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2742_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:204px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2742 {
  border-width:0px;
  position:absolute;
  left:118px;
  top:566px;
  width:204px;
  height:30px;
  display:flex;
}
#u2742 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2742_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:204px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2742.disabled {
}
#u2743_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:178px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u2743 {
  border-width:0px;
  position:absolute;
  left:123px;
  top:566px;
  width:178px;
  height:30px;
  display:flex;
  font-size:16px;
}
#u2743 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2743_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2744_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2744 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:601px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u2744 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2744_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2745_input {
  position:absolute;
  left:0px;
  top:0px;
  width:204px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2745_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:204px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2745_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:204px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2745 {
  border-width:0px;
  position:absolute;
  left:118px;
  top:601px;
  width:204px;
  height:30px;
  display:flex;
}
#u2745 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2745_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:204px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2745.disabled {
}
#u2746_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:194px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#7F7F7F;
}
#u2746 {
  border-width:0px;
  position:absolute;
  left:123px;
  top:601px;
  width:194px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#7F7F7F;
}
#u2746 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2746_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2747_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:448px;
  height:99px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2747 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:649px;
  width:448px;
  height:99px;
  display:flex;
}
#u2747 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2747_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2748_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:left;
}
#u2748 {
  border-width:0px;
  position:absolute;
  left:31px;
  top:656px;
  width:98px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:left;
}
#u2748 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2748_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2749_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:left;
}
#u2749 {
  border-width:0px;
  position:absolute;
  left:31px;
  top:701px;
  width:98px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#7F7F7F;
  text-align:left;
}
#u2749 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2749_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2750_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:308px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AAAAAA;
  text-align:left;
}
#u2750 {
  border-width:0px;
  position:absolute;
  left:119px;
  top:656px;
  width:308px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AAAAAA;
  text-align:left;
}
#u2750 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2750_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2751_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:308px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AAAAAA;
  text-align:left;
}
#u2751 {
  border-width:0px;
  position:absolute;
  left:119px;
  top:701px;
  width:308px;
  height:30px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#AAAAAA;
  text-align:left;
}
#u2751 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2751_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2752_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:39px;
  height:23px;
}
#u2752 {
  border-width:0px;
  position:absolute;
  left:382px;
  top:660px;
  width:39px;
  height:23px;
  display:flex;
}
#u2752 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2752_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2753_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:39px;
  height:23px;
}
#u2753 {
  border-width:0px;
  position:absolute;
  left:382px;
  top:705px;
  width:39px;
  height:23px;
  display:flex;
}
#u2753 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2753_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
