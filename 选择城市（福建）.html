﻿<!DOCTYPE html>
<html>
  <head>
    <title>选择城市（福建）</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/选择城市（福建）/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/选择城市（福建）/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (基础app框架(H5)) -->

      <!-- Unnamed (矩形) -->
      <div id="u6016" class="ax_default box_1">
        <div id="u6016_div" class=""></div>
        <div id="u6016_text" class="text ">
          <p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u6017" class="ax_default _二级标题">
        <div id="u6017_div" class=""></div>
        <div id="u6017_text" class="text ">
          <p><span>16:18</span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u6018" class="ax_default icon">
        <img id="u6018_img" class="img " src="images/海融宝签约_个人__f501_f502_/u3.svg"/>
        <div id="u6018_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u6019" class="ax_default icon">
        <img id="u6019_img" class="img " src="images/海融宝签约_个人__f501_f502_/u4.svg"/>
        <div id="u6019_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u6020" class="ax_default _文本段落">
        <div id="u6020_div" class=""></div>
        <div id="u6020_text" class="text ">
          <p><span>选择城市</span></p>
        </div>
      </div>

      <!-- 操作状态 (动态面板) -->
      <div id="u6021" class="ax_default ax_default_hidden" data-label="操作状态" style="display:none; visibility: hidden">
        <div id="u6021_state0" class="panel_state" data-label="操作成功" style="">
          <div id="u6021_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u6022" class="ax_default box_3">
              <div id="u6022_div" class=""></div>
              <div id="u6022_text" class="text ">
                <p><span>操作成功！</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u6021_state1" class="panel_state" data-label="操作失败" style="visibility: hidden;">
          <div id="u6021_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u6023" class="ax_default box_3">
              <div id="u6023_div" class=""></div>
              <div id="u6023_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u6024" class="ax_default _文本段落">
              <div id="u6024_div" class=""></div>
              <div id="u6024_text" class="text ">
                <p><span>操作失败！</span></p>
              </div>
            </div>

            <!-- Unnamed (图片 ) -->
            <div id="u6025" class="ax_default _图片_">
              <img id="u6025_img" class="img " src="images/海融宝签约_个人__f501_f502_/u10.png"/>
              <div id="u6025_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u6026" class="ax_default _图片_">
        <img id="u6026_img" class="img " src="images/海融宝签约_个人__f501_f502_/u11.png"/>
        <div id="u6026_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u6027" class="ax_default icon">
        <img id="u6027_img" class="img " src="images/海融宝签约_个人__f501_f502_/u12.svg"/>
        <div id="u6027_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u6028" class="ax_default _文本段落">
        <div id="u6028_div" class=""></div>
        <div id="u6028_text" class="text ">
          <p><span>hrypt.com</span></p>
        </div>
      </div>
      <div id="u6015" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (矩形) -->
      <div id="u6029" class="ax_default box_1">
        <div id="u6029_div" class=""></div>
        <div id="u6029_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u6030" class="ax_default box_1">
        <div id="u6030_div" class=""></div>
        <div id="u6030_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u6031" class="ax_default box_1">
        <div id="u6031_div" class=""></div>
        <div id="u6031_text" class="text ">
          <p><span>福州市</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u6032" class="ax_default box_1">
        <div id="u6032_div" class=""></div>
        <div id="u6032_text" class="text ">
          <p><span>全省</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u6033" class="ax_default box_1">
        <div id="u6033_div" class=""></div>
        <div id="u6033_text" class="text ">
          <p><span>莆田市</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u6034" class="ax_default box_1">
        <div id="u6034_div" class=""></div>
        <div id="u6034_text" class="text ">
          <p><span>厦门市</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u6035" class="ax_default box_1">
        <div id="u6035_div" class=""></div>
        <div id="u6035_text" class="text ">
          <p><span>三明市</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u6036" class="ax_default box_1">
        <div id="u6036_div" class=""></div>
        <div id="u6036_text" class="text ">
          <p><span>南平市</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u6037" class="ax_default box_1">
        <div id="u6037_div" class=""></div>
        <div id="u6037_text" class="text ">
          <p><span>龙岩市</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u6038" class="ax_default box_1">
        <div id="u6038_div" class=""></div>
        <div id="u6038_text" class="text ">
          <p><span>宁德市</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u6039" class="ax_default box_1">
        <div id="u6039_div" class=""></div>
        <div id="u6039_text" class="text ">
          <p><span>泉州市</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u6040" class="ax_default" data-left="70" data-top="203" data-width="371" data-height="40">

        <!-- Unnamed (组合) -->
        <div id="u6041" class="ax_default" data-left="70" data-top="203" data-width="371" data-height="40">

          <!-- Unnamed (矩形) -->
          <div id="u6042" class="ax_default box_1">
            <div id="u6042_div" class=""></div>
            <div id="u6042_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (形状) -->
          <div id="u6043" class="ax_default icon">
            <img id="u6043_img" class="img " src="images/选择城市（福建）/u6043.svg"/>
            <div id="u6043_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u6044" class="ax_default _一级标题">
            <div id="u6044_div" class=""></div>
            <div id="u6044_text" class="text ">
              <p><span>请输入省份/城市/市区搜索</span></p>
            </div>
          </div>

          <!-- Unnamed (图片 ) -->
          <div id="u6045" class="ax_default _图片">
            <img id="u6045_img" class="img " src="images/____________f502_f503____f506_f507_f508_f509_/u304.png"/>
            <div id="u6045_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (线段) -->
      <div id="u6046" class="ax_default line">
        <img id="u6046_img" class="img " src="images/选择城市（福建）/u6046.svg"/>
        <div id="u6046_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u6047" class="ax_default box_1">
        <div id="u6047_div" class=""></div>
        <div id="u6047_text" class="text ">
          <p><span>请选择城市</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u6048" class="ax_default box_1">
        <div id="u6048_div" class=""></div>
        <div id="u6048_text" class="text ">
          <p><span>返回上一级 &gt;</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u6049" class="ax_default box_1">
        <div id="u6049_div" class=""></div>
        <div id="u6049_text" class="text ">
          <p><span>漳州市</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u6050" class="ax_default _文本段落">
        <div id="u6050_div" class=""></div>
        <div id="u6050_text" class="text ">
          <p><span>福建&nbsp; 福州市&nbsp; </span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u6051" class="ax_default icon">
        <img id="u6051_img" class="img " src="images/选择城市（福建）/u6051.svg"/>
        <div id="u6051_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u6052" class="ax_default _文本段落">
        <div id="u6052_div" class=""></div>
        <div id="u6052_text" class="text ">
          <p><span>区/县</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u6053" class="ax_default" data-left="119" data-top="613" data-width="300" data-height="33">

        <!-- Unnamed (矩形) -->
        <div id="u6054" class="ax_default box_1">
          <div id="u6054_div" class=""></div>
          <div id="u6054_text" class="text ">
            <p><span>取消</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u6055" class="ax_default box_1">
          <div id="u6055_div" class=""></div>
          <div id="u6055_text" class="text ">
            <p><span>确定</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u6056" class="ax_default _图片_">
        <img id="u6056_img" class="img " src="images/充值方式/u1461.png"/>
        <div id="u6056_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
