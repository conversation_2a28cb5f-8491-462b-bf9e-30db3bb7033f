﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:1185px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u84_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:254px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u84 {
  border-width:0px;
  position:absolute;
  left:674px;
  top:786px;
  width:450px;
  height:254px;
  display:flex;
}
#u84 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u84_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u86_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:900px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:50px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u86 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:900px;
  display:flex;
  opacity:0.49;
}
#u86 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u86_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u87_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u87 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:20px;
  width:51px;
  height:40px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u87 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u87_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u88_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:18px;
}
#u88 {
  border-width:0px;
  position:absolute;
  left:425px;
  top:19px;
  width:23px;
  height:18px;
  display:flex;
}
#u88 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u88_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u89_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:16px;
}
#u89 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:21px;
  width:26px;
  height:16px;
  display:flex;
}
#u89 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u89_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u90_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u90 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:50px;
  width:300px;
  height:25px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u90 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u90_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u91 {
  position:fixed;
  left:50%;
  margin-left:-75px;
  top:50%;
  margin-top:-25px;
  visibility:hidden;
}
#u91_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u91_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u92_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u92 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u92 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u92_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u91_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u91_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u93_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u93 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u93 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u93_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u94_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u94 {
  border-width:0px;
  position:absolute;
  left:60px;
  top:16px;
  width:80px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u94 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u94_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u95_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u95 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:10px;
  width:30px;
  height:30px;
  display:flex;
}
#u95 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u95_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u96_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u96 {
  border-width:0px;
  position:absolute;
  left:463px;
  top:50px;
  width:25px;
  height:25px;
  display:flex;
}
#u96 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u96_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u97_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:25px;
}
#u97 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:50px;
  width:15px;
  height:25px;
  display:flex;
}
#u97 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u97_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u98_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:228px;
  height:11px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  text-align:center;
}
#u98 {
  border-width:0px;
  position:absolute;
  left:136px;
  top:71px;
  width:228px;
  height:11px;
  display:flex;
  font-size:10px;
  text-align:center;
}
#u98 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u98_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u99_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:439px;
  height:50px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u99 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:786px;
  width:439px;
  height:50px;
  display:flex;
  font-size:18px;
}
#u99 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u99_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u100 {
  position:fixed;
  left:50%;
  margin-left:-225px;
  top:50%;
  margin-top:-127px;
  width:450px;
  height:254px;
  visibility:hidden;
}
#u100_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:254px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u100_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u101_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:250px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u101 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:250px;
  display:flex;
}
#u101 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u101_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u102_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:28px;
}
#u102 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:23px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:28px;
}
#u102 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u102_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u103_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:141px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
  text-align:center;
}
#u103 {
  border-width:0px;
  position:absolute;
  left:142px;
  top:15px;
  width:141px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
  text-align:center;
}
#u103 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u103_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u104_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:366px;
  height:63px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u104 {
  border-width:0px;
  position:absolute;
  left:46px;
  top:62px;
  width:366px;
  height:63px;
  display:flex;
  font-size:18px;
}
#u104 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u104_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u105_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:141px;
  height:40px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u105 {
  border-width:0px;
  position:absolute;
  left:155px;
  top:138px;
  width:141px;
  height:40px;
  display:flex;
  font-size:16px;
}
#u105 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u105_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u100_state1 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:254px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u100_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u106_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:250px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u106 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:250px;
  display:flex;
}
#u106 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u106_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u107_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:28px;
}
#u107 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:10px;
  width:23px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:28px;
}
#u107 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u107_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u108_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:410px;
  height:92px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
}
#u108 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:71px;
  width:410px;
  height:92px;
  display:flex;
  font-size:20px;
}
#u108 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u108_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u109_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:40px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u109 {
  border-width:0px;
  position:absolute;
  left:137px;
  top:176px;
  width:162px;
  height:40px;
  display:flex;
  font-size:16px;
}
#u109 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u109_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u110_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:141px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
  text-align:center;
}
#u110 {
  border-width:0px;
  position:absolute;
  left:142px;
  top:15px;
  width:141px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
  text-align:center;
}
#u110 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u110_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u111 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u112 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u113_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:160px;
  height:151px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u113 {
  border-width:0px;
  position:absolute;
  left:147px;
  top:324px;
  width:160px;
  height:151px;
  display:flex;
}
#u113 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u113_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u114_img {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-5px;
  width:43px;
  height:21px;
}
#u114 {
  border-width:0px;
  position:absolute;
  left:147px;
  top:324px;
  width:32px;
  height:10px;
  display:flex;
}
#u114 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u114_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u115_img {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-5px;
  width:43px;
  height:21px;
}
#u115 {
  border-width:0px;
  position:absolute;
  left:275px;
  top:324px;
  width:32px;
  height:10px;
  display:flex;
}
#u115 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u115_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u116_img {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-5px;
  width:43px;
  height:21px;
}
#u116 {
  border-width:0px;
  position:absolute;
  left:147px;
  top:468px;
  width:32px;
  height:10px;
  display:flex;
}
#u116 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u116_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u117_img {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-5px;
  width:43px;
  height:21px;
}
#u117 {
  border-width:0px;
  position:absolute;
  left:275px;
  top:468px;
  width:32px;
  height:10px;
  display:flex;
}
#u117 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u117_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u118_img {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-5px;
  width:41px;
  height:21px;
}
#u118 {
  border-width:0px;
  position:absolute;
  left:135px;
  top:336px;
  width:30px;
  height:10px;
  display:flex;
  -webkit-transform:rotate(89.8063935939328deg);
  -moz-transform:rotate(89.8063935939328deg);
  -ms-transform:rotate(89.8063935939328deg);
  transform:rotate(89.8063935939328deg);
}
#u118 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u118_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u119_img {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-5px;
  width:41px;
  height:21px;
}
#u119 {
  border-width:0px;
  position:absolute;
  left:289px;
  top:336px;
  width:30px;
  height:10px;
  display:flex;
  -webkit-transform:rotate(89.8063935939328deg);
  -moz-transform:rotate(89.8063935939328deg);
  -ms-transform:rotate(89.8063935939328deg);
  transform:rotate(89.8063935939328deg);
}
#u119 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u119_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u120_img {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-5px;
  width:41px;
  height:21px;
}
#u120 {
  border-width:0px;
  position:absolute;
  left:135px;
  top:456px;
  width:30px;
  height:10px;
  display:flex;
  -webkit-transform:rotate(89.8063935939328deg);
  -moz-transform:rotate(89.8063935939328deg);
  -ms-transform:rotate(89.8063935939328deg);
  transform:rotate(89.8063935939328deg);
}
#u120 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u120_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u121_img {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:-5px;
  width:41px;
  height:21px;
}
#u121 {
  border-width:0px;
  position:absolute;
  left:289px;
  top:456px;
  width:30px;
  height:10px;
  display:flex;
  -webkit-transform:rotate(89.8063935939328deg);
  -moz-transform:rotate(89.8063935939328deg);
  -ms-transform:rotate(89.8063935939328deg);
  transform:rotate(89.8063935939328deg);
}
#u121 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u121_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u122_img {
  border-width:0px;
  position:absolute;
  left:-4px;
  top:-4px;
  width:141px;
  height:17px;
}
#u122 {
  border-width:0px;
  position:absolute;
  left:158px;
  top:397px;
  width:132px;
  height:8px;
  display:flex;
  -webkit-transform:rotate(0.66006042406149deg);
  -moz-transform:rotate(0.66006042406149deg);
  -ms-transform:rotate(0.66006042406149deg);
  transform:rotate(0.66006042406149deg);
  opacity:0.7;
  color:#D9001B;
}
#u122 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u122_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u123_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:224px;
  height:211px;
  background:inherit;
  background-color:rgba(22, 155, 213, 0.0470588235294118);
  border:none;
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:28px;
}
#u123 {
  border-width:0px;
  position:absolute;
  left:118px;
  top:287px;
  width:224px;
  height:211px;
  display:flex;
  font-size:28px;
}
#u123 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u123_div.mouseDown {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:224px;
  height:211px;
  background:inherit;
  background-color:rgba(204, 204, 204, 1);
  border:none;
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:28px;
}
#u123.mouseDown {
}
#u123_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u124_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u124 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:111px;
  width:450px;
  height:22px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u124 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u124_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u125_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:121px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u125 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:156px;
  width:450px;
  height:121px;
  display:flex;
}
#u125 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u125_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u126_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:422px;
  height:97px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u126 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:168px;
  width:422px;
  height:97px;
  display:flex;
  font-size:16px;
}
#u126 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u126_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u127_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:256px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#D9001B;
  text-align:right;
}
#u127 {
  border-width:0px;
  position:absolute;
  left:224px;
  top:138px;
  width:256px;
  height:18px;
  display:flex;
  font-size:12px;
  color:#D9001B;
  text-align:right;
}
#u127 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u127_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u128_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:608px;
  height:308px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u128 {
  border-width:0px;
  position:absolute;
  left:577px;
  top:48px;
  width:608px;
  height:308px;
  display:flex;
}
#u128 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u128_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u129_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:370px;
  height:79px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u129 {
  border-width:0px;
  position:absolute;
  left:527px;
  top:373px;
  width:370px;
  height:79px;
  display:flex;
}
#u129 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u129_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u130_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:608px;
  height:278px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u130 {
  border-width:0px;
  position:absolute;
  left:577px;
  top:442px;
  width:608px;
  height:278px;
  display:flex;
}
#u130 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u130_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u131 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u132_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#AAAAAA;
}
#u132 {
  border-width:0px;
  position:absolute;
  left:521px;
  top:10px;
  width:90px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#AAAAAA;
}
#u132 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u132_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u133_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:334px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u133 {
  border-width:0px;
  position:absolute;
  left:613px;
  top:10px;
  width:334px;
  height:18px;
  display:flex;
  font-size:16px;
}
#u133 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u133_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u134_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:28px;
}
#u134 {
  border-width:0px;
  position:absolute;
  left:701px;
  top:803px;
  width:23px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:28px;
}
#u134 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u134_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u135_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:141px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
  text-align:center;
}
#u135 {
  border-width:0px;
  position:absolute;
  left:829px;
  top:818px;
  width:141px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
  text-align:center;
}
#u135 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u135_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u136_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:366px;
  height:63px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u136 {
  border-width:0px;
  position:absolute;
  left:733px;
  top:865px;
  width:366px;
  height:63px;
  display:flex;
  font-size:18px;
}
#u136 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u136_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u137_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:141px;
  height:40px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u137 {
  border-width:0px;
  position:absolute;
  left:842px;
  top:941px;
  width:141px;
  height:40px;
  display:flex;
  font-size:16px;
}
#u137 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u137_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u138_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:254px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u138 {
  border-width:0px;
  position:absolute;
  left:674px;
  top:1073px;
  width:450px;
  height:254px;
  display:flex;
}
#u138 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u138_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u139_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:28px;
}
#u139 {
  border-width:0px;
  position:absolute;
  left:694px;
  top:1096px;
  width:23px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:28px;
}
#u139 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u139_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u140_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:410px;
  height:92px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
}
#u140 {
  border-width:0px;
  position:absolute;
  left:694px;
  top:1157px;
  width:410px;
  height:92px;
  display:flex;
  font-size:20px;
}
#u140 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u140_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u141_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:162px;
  height:40px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u141 {
  border-width:0px;
  position:absolute;
  left:817px;
  top:1262px;
  width:162px;
  height:40px;
  display:flex;
  font-size:16px;
}
#u141 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u141_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u142_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:141px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
  text-align:center;
}
#u142 {
  border-width:0px;
  position:absolute;
  left:822px;
  top:1101px;
  width:141px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
  text-align:center;
}
#u142 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u142_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
