﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:510px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u4649_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:1280px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:50px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4649 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:1280px;
  display:flex;
  opacity:0.49;
}
#u4649 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4649_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4650_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u4650 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:20px;
  width:51px;
  height:18px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u4650 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4650_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4651_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:18px;
}
#u4651 {
  border-width:0px;
  position:absolute;
  left:425px;
  top:19px;
  width:23px;
  height:18px;
  display:flex;
}
#u4651 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4651_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4652_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:16px;
}
#u4652 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:21px;
  width:26px;
  height:16px;
  display:flex;
}
#u4652 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4652_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4653_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:25px;
}
#u4653 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:50px;
  width:24px;
  height:25px;
  display:flex;
}
#u4653 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4653_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4654_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:22px;
}
#u4654 {
  border-width:0px;
  position:absolute;
  left:467px;
  top:56px;
  width:21px;
  height:22px;
  display:flex;
}
#u4654 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4654_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4655_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:252px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u4655 {
  border-width:0px;
  position:absolute;
  left:124px;
  top:49px;
  width:252px;
  height:25px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u4655 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4655_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4656 {
  position:fixed;
  left:50%;
  margin-left:-75px;
  top:50%;
  margin-top:-25px;
  visibility:hidden;
}
#u4656_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4656_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u4657_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u4657 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u4657 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u4657_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4656_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u4656_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u4658_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u4658 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u4658 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u4658_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4659_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u4659 {
  border-width:0px;
  position:absolute;
  left:60px;
  top:16px;
  width:80px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u4659 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4659_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4660_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u4660 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:10px;
  width:30px;
  height:30px;
  display:flex;
}
#u4660 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4660_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4661_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:228px;
  height:11px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  text-align:center;
}
#u4661 {
  border-width:0px;
  position:absolute;
  left:136px;
  top:71px;
  width:228px;
  height:11px;
  display:flex;
  font-size:10px;
  text-align:center;
}
#u4661 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4661_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4662_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:80px;
}
#u4662 {
  border-width:0px;
  position:absolute;
  left:31px;
  top:93px;
  width:80px;
  height:80px;
  display:flex;
}
#u4662 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4662_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4663 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4664_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:367px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u4664 {
  border-width:0px;
  position:absolute;
  left:119px;
  top:97px;
  width:367px;
  height:40px;
  display:flex;
  font-size:16px;
}
#u4664 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4664_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4665_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:344px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u4665 {
  border-width:0px;
  position:absolute;
  left:128px;
  top:105px;
  width:344px;
  height:26px;
  display:flex;
  font-size:16px;
}
#u4665 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4665_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4666_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:480px;
  height:359px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4666 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:192px;
  width:480px;
  height:359px;
  display:flex;
}
#u4666 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4666_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4667 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4668_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:367px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u4668 {
  border-width:0px;
  position:absolute;
  left:117px;
  top:137px;
  width:367px;
  height:40px;
  display:flex;
  font-size:16px;
}
#u4668 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4668_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4669_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u4669 {
  border-width:0px;
  position:absolute;
  left:123px;
  top:144px;
  width:119px;
  height:26px;
  display:flex;
  font-size:16px;
}
#u4669 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4669_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4670_input {
  position:absolute;
  left:0px;
  top:0px;
  width:275px;
  height:26px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#555555;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4670_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:275px;
  height:26px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#555555;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4670_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:275px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#555555;
}
#u4670 {
  border-width:0px;
  position:absolute;
  left:195px;
  top:144px;
  width:275px;
  height:26px;
  display:flex;
  font-size:16px;
  color:#555555;
}
#u4670 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4670_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:275px;
  height:26px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#555555;
}
#u4670.disabled {
}
#u4672_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4672 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:259px;
  width:450px;
  height:54px;
  display:flex;
}
#u4672 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4672_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4673_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u4673 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:271px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u4673 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4673_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4674_input {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4674_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4674_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4674 {
  border-width:0px;
  position:absolute;
  left:139px;
  top:271px;
  width:330px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u4674 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4674_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4674.disabled {
}
#u4675_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:center;
}
#u4675 {
  border-width:0px;
  position:absolute;
  left:139px;
  top:274px;
  width:131px;
  height:24px;
  display:flex;
  font-size:16px;
  text-align:center;
}
#u4675 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4675_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4676_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:26px;
}
#u4676 {
  border-width:0px;
  position:absolute;
  left:439px;
  top:273px;
  width:26px;
  height:26px;
  display:flex;
}
#u4676 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4676_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4677_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:27px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#555555;
  text-align:center;
}
#u4677 {
  border-width:0px;
  position:absolute;
  left:270px;
  top:273px;
  width:68px;
  height:27px;
  display:flex;
  font-size:16px;
  color:#555555;
  text-align:center;
}
#u4677 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4677_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4679_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4679 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:203px;
  width:450px;
  height:54px;
  display:flex;
}
#u4679 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4679_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4680_input {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4680_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4680_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4680 {
  border-width:0px;
  position:absolute;
  left:139px;
  top:215px;
  width:330px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u4680 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4680_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4680.disabled {
}
#u4681_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:266px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4681 {
  border-width:0px;
  position:absolute;
  left:144px;
  top:215px;
  width:266px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u4681 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4681_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4682_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u4682 {
  border-width:0px;
  position:absolute;
  left:415px;
  top:215px;
  width:54px;
  height:30px;
  display:flex;
  font-size:16px;
}
#u4682 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4682_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4683_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u4683 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:215px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u4683 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4683_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4685_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4685 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:315px;
  width:450px;
  height:54px;
  display:flex;
}
#u4685 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4685_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4686_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u4686 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:327px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u4686 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4686_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4687_input {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4687_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4687_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4687 {
  border-width:0px;
  position:absolute;
  left:139px;
  top:327px;
  width:330px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u4687 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4687_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4687.disabled {
}
#u4688_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:294px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4688 {
  border-width:0px;
  position:absolute;
  left:150px;
  top:327px;
  width:294px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u4688 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4688_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4689_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:26px;
}
#u4689 {
  border-width:0px;
  position:absolute;
  left:442px;
  top:329px;
  width:26px;
  height:26px;
  display:flex;
  font-size:16px;
  color:#555555;
}
#u4689 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4689_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4690 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4691_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:42px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#555555;
  text-align:left;
}
#u4691 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:389px;
  width:112px;
  height:42px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#555555;
  text-align:left;
}
#u4691 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4691_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4692_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:336px;
  height:84px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#555555;
  text-align:left;
}
#u4692 {
  border-width:0px;
  position:absolute;
  left:141px;
  top:369px;
  width:336px;
  height:84px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#555555;
  text-align:left;
}
#u4692 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4692_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4693_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#555555;
}
#u4693 {
  border-width:0px;
  position:absolute;
  left:148px;
  top:376px;
  width:260px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#555555;
}
#u4693 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4693_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4694_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#555555;
  text-align:right;
}
#u4694 {
  border-width:0px;
  position:absolute;
  left:355px;
  top:429px;
  width:120px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#555555;
  text-align:right;
}
#u4694 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4694_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4696 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4697_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:304px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u4697 {
  border-width:0px;
  position:absolute;
  left:54px;
  top:455px;
  width:304px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u4697 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4697_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4698 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4699_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:205px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4699 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:486px;
  width:205px;
  height:40px;
  display:flex;
}
#u4699 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4699_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4700_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:205px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4700 {
  border-width:0px;
  position:absolute;
  left:279px;
  top:486px;
  width:205px;
  height:40px;
  display:flex;
}
#u4700 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4700_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4701_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u4701 {
  border-width:0px;
  position:absolute;
  left:194px;
  top:494px;
  width:25px;
  height:25px;
  display:flex;
}
#u4701 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4701_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4702_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u4702 {
  border-width:0px;
  position:absolute;
  left:443px;
  top:494px;
  width:25px;
  height:25px;
  display:flex;
}
#u4702 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4702_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4703_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  color:#7F7F7F;
}
#u4703 {
  border-width:0px;
  position:absolute;
  left:54px;
  top:491px;
  width:108px;
  height:30px;
  display:flex;
  font-size:18px;
  color:#7F7F7F;
}
#u4703 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4703_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4704_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  color:#7F7F7F;
}
#u4704 {
  border-width:0px;
  position:absolute;
  left:304px;
  top:491px;
  width:108px;
  height:30px;
  display:flex;
  font-size:18px;
  color:#7F7F7F;
}
#u4704 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4704_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4705_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:18px;
  height:7px;
}
#u4705 {
  border-width:0px;
  position:absolute;
  left:250px;
  top:500px;
  width:14px;
  height:3px;
  display:flex;
}
#u4705 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4705_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4706_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:480px;
  height:264px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4706 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:1196px;
  width:480px;
  height:264px;
  display:flex;
}
#u4706 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4706_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4707_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u4707 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:1205px;
  width:72px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u4707 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4707_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4709_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4709 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:1299px;
  width:450px;
  height:54px;
  display:flex;
}
#u4709 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4709_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4710_input {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4710_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4710_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4710 {
  border-width:0px;
  position:absolute;
  left:142px;
  top:1311px;
  width:330px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u4710 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4710_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4710.disabled {
}
#u4711_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:266px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4711 {
  border-width:0px;
  position:absolute;
  left:147px;
  top:1311px;
  width:266px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u4711 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4711_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4712_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u4712 {
  border-width:0px;
  position:absolute;
  left:418px;
  top:1311px;
  width:54px;
  height:30px;
  display:flex;
  font-size:16px;
}
#u4712 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4712_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4713_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u4713 {
  border-width:0px;
  position:absolute;
  left:47px;
  top:1311px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u4713 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4713_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4715 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4716_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:304px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u4716 {
  border-width:0px;
  position:absolute;
  left:52px;
  top:1355px;
  width:304px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u4716 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4716_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4717 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4718_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:205px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4718 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:1386px;
  width:205px;
  height:40px;
  display:flex;
}
#u4718 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4718_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4719_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:205px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4719 {
  border-width:0px;
  position:absolute;
  left:277px;
  top:1386px;
  width:205px;
  height:40px;
  display:flex;
}
#u4719 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4719_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4720_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u4720 {
  border-width:0px;
  position:absolute;
  left:192px;
  top:1394px;
  width:25px;
  height:25px;
  display:flex;
}
#u4720 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4720_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4721_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u4721 {
  border-width:0px;
  position:absolute;
  left:441px;
  top:1394px;
  width:25px;
  height:25px;
  display:flex;
}
#u4721 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4721_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4722_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  color:#7F7F7F;
}
#u4722 {
  border-width:0px;
  position:absolute;
  left:52px;
  top:1391px;
  width:108px;
  height:30px;
  display:flex;
  font-size:18px;
  color:#7F7F7F;
}
#u4722 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4722_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4723_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  color:#7F7F7F;
}
#u4723 {
  border-width:0px;
  position:absolute;
  left:302px;
  top:1391px;
  width:108px;
  height:30px;
  display:flex;
  font-size:18px;
  color:#7F7F7F;
}
#u4723 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4723_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4724_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:18px;
  height:7px;
}
#u4724 {
  border-width:0px;
  position:absolute;
  left:248px;
  top:1400px;
  width:14px;
  height:3px;
  display:flex;
}
#u4724 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4724_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4726_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4726 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:1243px;
  width:450px;
  height:54px;
  display:flex;
}
#u4726 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4726_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4727_input {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4727_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4727_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4727 {
  border-width:0px;
  position:absolute;
  left:142px;
  top:1255px;
  width:330px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u4727 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4727_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4727.disabled {
}
#u4728_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:266px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4728 {
  border-width:0px;
  position:absolute;
  left:147px;
  top:1255px;
  width:266px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u4728 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4728_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4729_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u4729 {
  border-width:0px;
  position:absolute;
  left:418px;
  top:1255px;
  width:54px;
  height:30px;
  display:flex;
  font-size:16px;
}
#u4729 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4729_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4730_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u4730 {
  border-width:0px;
  position:absolute;
  left:47px;
  top:1255px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u4730 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4730_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4731_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:480px;
  height:620px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4731 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:560px;
  width:480px;
  height:620px;
  display:flex;
}
#u4731 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4731_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4733_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:448px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4733 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:601px;
  width:448px;
  height:54px;
  display:flex;
}
#u4733 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4733_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4734_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u4734 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:613px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u4734 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4734_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4735_input {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4735_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4735_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4735 {
  border-width:0px;
  position:absolute;
  left:139px;
  top:613px;
  width:330px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u4735 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4735_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4735.disabled {
}
#u4736_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:30px;
}
#u4736 {
  border-width:0px;
  position:absolute;
  left:447px;
  top:613px;
  width:18px;
  height:30px;
  display:flex;
}
#u4736 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4736_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4737_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:294px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4737 {
  border-width:0px;
  position:absolute;
  left:150px;
  top:613px;
  width:294px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u4737 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4737_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4738 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4739_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:42px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#555555;
  text-align:left;
}
#u4739 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:675px;
  width:112px;
  height:42px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#555555;
  text-align:left;
}
#u4739 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4739_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4740_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:336px;
  height:84px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#555555;
  text-align:left;
}
#u4740 {
  border-width:0px;
  position:absolute;
  left:141px;
  top:655px;
  width:336px;
  height:84px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#555555;
  text-align:left;
}
#u4740 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4740_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4741_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#555555;
}
#u4741 {
  border-width:0px;
  position:absolute;
  left:148px;
  top:662px;
  width:260px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#555555;
}
#u4741 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4741_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4742_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#555555;
  text-align:right;
}
#u4742 {
  border-width:0px;
  position:absolute;
  left:355px;
  top:715px;
  width:120px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#555555;
  text-align:right;
}
#u4742 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4742_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4743 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4744_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:42px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#555555;
  text-align:left;
}
#u4744 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:761px;
  width:112px;
  height:42px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#555555;
  text-align:left;
}
#u4744 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4744_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4745_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:336px;
  height:84px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#555555;
  text-align:left;
}
#u4745 {
  border-width:0px;
  position:absolute;
  left:141px;
  top:741px;
  width:336px;
  height:84px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#555555;
  text-align:left;
}
#u4745 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4745_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4746_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#555555;
}
#u4746 {
  border-width:0px;
  position:absolute;
  left:148px;
  top:748px;
  width:260px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#555555;
}
#u4746 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4746_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4747_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#555555;
  text-align:right;
}
#u4747 {
  border-width:0px;
  position:absolute;
  left:355px;
  top:801px;
  width:120px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#555555;
  text-align:right;
}
#u4747 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4747_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4749_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4749 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:828px;
  width:450px;
  height:54px;
  display:flex;
}
#u4749 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4749_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4750_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u4750 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:840px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u4750 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4750_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4751_input {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4751_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4751_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4751 {
  border-width:0px;
  position:absolute;
  left:139px;
  top:840px;
  width:330px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u4751 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4751_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4751.disabled {
}
#u4752_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:30px;
}
#u4752 {
  border-width:0px;
  position:absolute;
  left:447px;
  top:840px;
  width:18px;
  height:30px;
  display:flex;
}
#u4752 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4752_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4753_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:294px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4753 {
  border-width:0px;
  position:absolute;
  left:150px;
  top:840px;
  width:294px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u4753 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4753_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4755_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4755 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:884px;
  width:450px;
  height:54px;
  display:flex;
}
#u4755 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4755_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4756_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u4756 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:896px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u4756 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4756_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4757_input {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4757_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4757_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4757 {
  border-width:0px;
  position:absolute;
  left:139px;
  top:896px;
  width:330px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u4757 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4757_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4757.disabled {
}
#u4758_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:294px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4758 {
  border-width:0px;
  position:absolute;
  left:150px;
  top:896px;
  width:294px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u4758 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4758_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4759_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:26px;
}
#u4759 {
  border-width:0px;
  position:absolute;
  left:442px;
  top:898px;
  width:26px;
  height:26px;
  display:flex;
  font-size:16px;
  color:#555555;
}
#u4759 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4759_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4761_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4761 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:940px;
  width:450px;
  height:54px;
  display:flex;
}
#u4761 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4761_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4762_input {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4762_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4762_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4762 {
  border-width:0px;
  position:absolute;
  left:139px;
  top:952px;
  width:330px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u4762 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4762_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4762.disabled {
}
#u4763_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:266px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4763 {
  border-width:0px;
  position:absolute;
  left:144px;
  top:952px;
  width:266px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u4763 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4763_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4764_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u4764 {
  border-width:0px;
  position:absolute;
  left:415px;
  top:952px;
  width:54px;
  height:30px;
  display:flex;
  font-size:16px;
}
#u4764 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4764_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4765_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u4765 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:952px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u4765 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4765_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4767_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4767 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:996px;
  width:450px;
  height:54px;
  display:flex;
}
#u4767 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4767_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4768_input {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4768_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4768_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4768 {
  border-width:0px;
  position:absolute;
  left:139px;
  top:1008px;
  width:330px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u4768 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4768_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4768.disabled {
}
#u4769_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:266px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4769 {
  border-width:0px;
  position:absolute;
  left:144px;
  top:1008px;
  width:266px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u4769 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4769_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4770_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u4770 {
  border-width:0px;
  position:absolute;
  left:415px;
  top:1008px;
  width:54px;
  height:30px;
  display:flex;
  font-size:16px;
}
#u4770 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4770_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4771_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u4771 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:1008px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u4771 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4771_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4773_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4773 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:1052px;
  width:450px;
  height:54px;
  display:flex;
}
#u4773 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4773_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4774_input {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4774_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4774_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4774 {
  border-width:0px;
  position:absolute;
  left:139px;
  top:1064px;
  width:330px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u4774 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4774_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4774.disabled {
}
#u4775_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:266px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4775 {
  border-width:0px;
  position:absolute;
  left:144px;
  top:1064px;
  width:266px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u4775 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4775_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4776_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u4776 {
  border-width:0px;
  position:absolute;
  left:415px;
  top:1064px;
  width:54px;
  height:30px;
  display:flex;
  font-size:16px;
}
#u4776 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4776_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4777_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u4777 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:1064px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u4777 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4777_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4779_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4779 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:1108px;
  width:450px;
  height:54px;
  display:flex;
}
#u4779 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4779_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4780_input {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4780_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4780_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4780 {
  border-width:0px;
  position:absolute;
  left:139px;
  top:1120px;
  width:330px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u4780 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4780_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4780.disabled {
}
#u4781_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:266px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4781 {
  border-width:0px;
  position:absolute;
  left:144px;
  top:1120px;
  width:266px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u4781 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4781_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4782_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u4782 {
  border-width:0px;
  position:absolute;
  left:415px;
  top:1120px;
  width:54px;
  height:30px;
  display:flex;
  font-size:16px;
}
#u4782 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4782_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4783_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u4783 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:1120px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u4783 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4783_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4784_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:23px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
}
#u4784 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:569px;
  width:120px;
  height:23px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
}
#u4784 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4784_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4785_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:480px;
  height:238px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4785 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:1478px;
  width:480px;
  height:238px;
  display:flex;
}
#u4785 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4785_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4786 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4787_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:225px;
  height:180px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#AEAEAE;
  text-align:left;
}
#u4787 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:1520px;
  width:225px;
  height:180px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#AEAEAE;
  text-align:left;
}
#u4787 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4787_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4789 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4790_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:45px;
}
#u4790 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:1532px;
  width:45px;
  height:45px;
  display:flex;
  color:#7F7F7F;
}
#u4790 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4790_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4791_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:151px;
  height:42px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#7F7F7F;
  text-align:left;
}
#u4791 {
  border-width:0px;
  position:absolute;
  left:99px;
  top:1534px;
  width:151px;
  height:42px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#7F7F7F;
  text-align:left;
}
#u4791 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4791_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4792 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:1532px;
  width:210px;
  height:120px;
  visibility:hidden;
}
#u4792_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:210px;
  height:120px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4792_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u4793_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:120px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u4793 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:120px;
  display:flex;
  font-size:18px;
}
#u4793 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4793_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4794_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u4794 {
  border-width:0px;
  position:absolute;
  left:179px;
  top:2px;
  width:23px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u4794 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4794_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4795_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:2px;
}
#u4795 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:200px;
  height:1px;
  display:flex;
  font-size:18px;
}
#u4795 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4795_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4796_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:165px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  text-align:center;
}
#u4796 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:12px;
  width:165px;
  height:21px;
  display:flex;
  font-size:18px;
  text-align:center;
}
#u4796 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4796_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4797_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:2px;
}
#u4797 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:80px;
  width:200px;
  height:1px;
  display:flex;
  font-size:18px;
}
#u4797 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4797_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4798_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:173px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  text-align:center;
}
#u4798 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:50px;
  width:173px;
  height:21px;
  display:flex;
  font-size:18px;
  text-align:center;
}
#u4798 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4798_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4799_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u4799 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:85px;
  width:120px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u4799 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4799_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4800_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:102px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u4800 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:1487px;
  width:102px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u4800 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4800_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4801 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:43px;
  width:56px;
  height:42px;
  overflow:hidden;
  background-image:url('../../resources/images/transparent.gif');
}
