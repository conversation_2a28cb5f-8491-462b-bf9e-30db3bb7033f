﻿<!DOCTYPE html>
<html>
  <head>
    <title>海融宝签约(个人)(F501\F502)</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/海融宝签约_个人__f501_f502_/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/海融宝签约_个人__f501_f502_/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (基础app框架(H5)) -->

      <!-- Unnamed (矩形) -->
      <div id="u1" class="ax_default box_1">
        <div id="u1_div" class=""></div>
        <div id="u1_text" class="text ">
          <p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u2" class="ax_default _二级标题">
        <div id="u2_div" class=""></div>
        <div id="u2_text" class="text ">
          <p><span>16:18</span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u3" class="ax_default icon">
        <img id="u3_img" class="img " src="images/海融宝签约_个人__f501_f502_/u3.svg"/>
        <div id="u3_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u4" class="ax_default icon">
        <img id="u4_img" class="img " src="images/海融宝签约_个人__f501_f502_/u4.svg"/>
        <div id="u4_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5" class="ax_default _文本段落">
        <div id="u5_div" class=""></div>
        <div id="u5_text" class="text ">
          <p><span>海融宝签约(邮储数字人民币)</span></p>
        </div>
      </div>

      <!-- 操作状态 (动态面板) -->
      <div id="u6" class="ax_default ax_default_hidden" data-label="操作状态" style="display:none; visibility: hidden">
        <div id="u6_state0" class="panel_state" data-label="操作成功" style="">
          <div id="u6_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u7" class="ax_default box_3">
              <div id="u7_div" class=""></div>
              <div id="u7_text" class="text ">
                <p><span>操作成功！</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u6_state1" class="panel_state" data-label="操作失败" style="visibility: hidden;">
          <div id="u6_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u8" class="ax_default box_3">
              <div id="u8_div" class=""></div>
              <div id="u8_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u9" class="ax_default _文本段落">
              <div id="u9_div" class=""></div>
              <div id="u9_text" class="text ">
                <p><span>操作失败！</span></p>
              </div>
            </div>

            <!-- Unnamed (图片 ) -->
            <div id="u10" class="ax_default _图片_">
              <img id="u10_img" class="img " src="images/海融宝签约_个人__f501_f502_/u10.png"/>
              <div id="u10_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u11" class="ax_default _图片_">
        <img id="u11_img" class="img " src="images/海融宝签约_个人__f501_f502_/u11.png"/>
        <div id="u11_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u12" class="ax_default icon">
        <img id="u12_img" class="img " src="images/海融宝签约_个人__f501_f502_/u12.svg"/>
        <div id="u12_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u13" class="ax_default _文本段落">
        <div id="u13_div" class=""></div>
        <div id="u13_text" class="text ">
          <p><span>hrypt.com</span></p>
        </div>
      </div>
      <div id="u0" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (矩形) -->
      <div id="u14" class="ax_default _文本段落">
        <div id="u14_div" class=""></div>
        <div id="u14_text" class="text ">
          <p><span>数字人民币平台结算服务</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u15" class="ax_default _形状">
        <div id="u15_div" class=""></div>
        <div id="u15_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u16" class="ax_default _文本段落">
        <div id="u16_div" class=""></div>
        <div id="u16_text" class="text ">
          <p><span>海融宝平台为实现平台会员提供数字⼈⺠币结算服务，海融宝在邮储银⾏指定渠道开通，授权第三⽅平台通过系统指令对客⼾签约的合约⼦钱包进⾏相关操作的服务。服务内容包括但不限于平台通合约⼦钱包开⽴功能、资⾦划拨服务功能、余额查询功能、电⼦回单功能等。</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u17" class="ax_default _形状">
        <div id="u17_div" class=""></div>
        <div id="u17_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (身份证输入) -->

      <!-- Unnamed (图片 ) -->
      <div id="u19" class="ax_default _图片_">
        <img id="u19_img" class="img " src="images/海融宝签约_个人__f501_f502_/u19.png"/>
        <div id="u19_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u20" class="ax_default _图片_">
        <img id="u20_img" class="img " src="images/海融宝签约_个人__f501_f502_/u20.png"/>
        <div id="u20_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u21" class="ax_default" data-left="74" data-top="671" data-width="142" data-height="97">

        <!-- Unnamed (圆形) -->
        <div id="u22" class="ax_default _形状">
          <img id="u22_img" class="img " src="images/海融宝签约_个人__f501_f502_/u22.svg"/>
          <div id="u22_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (形状) -->
        <div id="u23" class="ax_default icon">
          <img id="u23_img" class="img " src="images/海融宝签约_个人__f501_f502_/u23.svg"/>
          <div id="u23_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u24" class="ax_default _文本段落">
          <div id="u24_div" class=""></div>
          <div id="u24_text" class="text ">
            <p><span>上传人像面</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u25" class="ax_default" data-left="294" data-top="671" data-width="142" data-height="97">

        <!-- Unnamed (圆形) -->
        <div id="u26" class="ax_default _形状">
          <img id="u26_img" class="img " src="images/海融宝签约_个人__f501_f502_/u22.svg"/>
          <div id="u26_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (形状) -->
        <div id="u27" class="ax_default icon">
          <img id="u27_img" class="img " src="images/海融宝签约_个人__f501_f502_/u23.svg"/>
          <div id="u27_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u28" class="ax_default _文本段落">
          <div id="u28_div" class=""></div>
          <div id="u28_text" class="text ">
            <p><span>上传国徽面</span></p>
          </div>
        </div>
      </div>

      <!-- 弹出选图 (动态面板) -->
      <div id="u29" class="ax_default ax_default_hidden" data-label="弹出选图" style="display:none; visibility: hidden">
        <div id="u29_state0" class="panel_state" data-label="选择类别" style="">
          <div id="u29_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u30" class="ax_default box_1">
              <div id="u30_div" class=""></div>
              <div id="u30_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u31" class="ax_default _二级标题">
              <div id="u31_div" class=""></div>
              <div id="u31_text" class="text ">
                <p><span>×</span></p>
              </div>
            </div>

            <!-- Unnamed (线段) -->
            <div id="u32" class="ax_default line">
              <img id="u32_img" class="img " src="images/海融宝签约_个人__f501_f502_/u32.svg"/>
              <div id="u32_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u33" class="ax_default _文本段落">
              <div id="u33_div" class=""></div>
              <div id="u33_text" class="text ">
                <p><span>拍照/拍视频</span></p>
              </div>
            </div>

            <!-- Unnamed (线段) -->
            <div id="u34" class="ax_default line">
              <img id="u34_img" class="img " src="images/海融宝签约_个人__f501_f502_/u32.svg"/>
              <div id="u34_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u35" class="ax_default _文本段落">
              <div id="u35_div" class=""></div>
              <div id="u35_text" class="text ">
                <p><span>从相册选取</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u36" class="ax_default primary_button">
              <div id="u36_div" class=""></div>
              <div id="u36_text" class="text ">
                <p><span>取消</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u37" class="ax_default _文本段落">
        <div id="u37_div" class=""></div>
        <div id="u37_text" class="text ">
          <p><span>上传身份证照片：</span></p>
        </div>
      </div>
      <div id="u18" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (输入基本信息) -->

      <!-- Unnamed (矩形) -->
      <div id="u39" class="ax_default _形状">
        <div id="u39_div" class=""></div>
        <div id="u39_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u40" class="ax_default text_field">
        <div id="u40_div" class=""></div>
        <input id="u40_input" type="text" value="" class="u40_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u41" class="ax_default _文本段落">
        <div id="u41_div" class=""></div>
        <div id="u41_text" class="text ">
          <p><span>请输入身份证号码</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u42" class="ax_default _文本段落">
        <div id="u42_div" class=""></div>
        <div id="u42_text" class="text ">
          <p><span>&nbsp;</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u43" class="ax_default _文本段落">
        <div id="u43_div" class=""></div>
        <div id="u43_text" class="text ">
          <p><span>*身份证号</span></p>
        </div>
      </div>
      <div id="u38" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (地址详细信息) -->

      <!-- Unnamed (矩形) -->
      <div id="u45" class="ax_default _形状">
        <div id="u45_div" class=""></div>
        <div id="u45_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u46" class="ax_default _文本段落">
        <div id="u46_div" class=""></div>
        <div id="u46_text" class="text ">
          <p><span>身份证地址</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u47" class="ax_default text_field">
        <div id="u47_div" class=""></div>
        <input id="u47_input" type="text" value="" class="u47_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u48" class="ax_default _文本段落">
        <div id="u48_div" class=""></div>
        <div id="u48_text" class="text ">
          <p><span>请输入身份证地址</span></p>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u49" class="ax_default _图片_">
        <img id="u49_img" class="img " src="images/海融宝签约_个人__f501_f502_/u49.png"/>
        <div id="u49_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>
      <div id="u44" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (起止日期) -->

      <!-- Unnamed (组合) -->
      <div id="u51" class="ax_default" data-left="25" data-top="531" data-width="450" data-height="71">

        <!-- Unnamed (矩形) -->
        <div id="u52" class="ax_default _文本段落">
          <div id="u52_div" class=""></div>
          <div id="u52_text" class="text ">
            <p><span>&nbsp;*身份证有效时间</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u53" class="ax_default" data-left="25" data-top="562" data-width="450" data-height="40">

          <!-- Unnamed (矩形) -->
          <div id="u54" class="ax_default _形状">
            <div id="u54_div" class=""></div>
            <div id="u54_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u55" class="ax_default _形状">
            <div id="u55_div" class=""></div>
            <div id="u55_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (形状) -->
          <div id="u56" class="ax_default icon">
            <img id="u56_img" class="img " src="images/海融宝签约_个人__f501_f502_/u56.svg"/>
            <div id="u56_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (形状) -->
          <div id="u57" class="ax_default icon">
            <img id="u57_img" class="img " src="images/海融宝签约_个人__f501_f502_/u56.svg"/>
            <div id="u57_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u58" class="ax_default _文本段落">
            <div id="u58_div" class=""></div>
            <div id="u58_text" class="text ">
              <p><span>开始日期</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u59" class="ax_default _文本段落">
            <div id="u59_div" class=""></div>
            <div id="u59_text" class="text ">
              <p><span>结束日期</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u60" class="ax_default _线段">
          <img id="u60_img" class="img " src="images/海融宝签约_个人__f501_f502_/u60.svg"/>
          <div id="u60_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>
      <div id="u50" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (组合) -->
      <div id="u61" class="ax_default" data-left="25" data-top="305" data-width="446" data-height="46">

        <!-- Unnamed (组合) -->
        <div id="u62" class="ax_default" data-left="290" data-top="305" data-width="181" data-height="46">

          <!-- Unnamed (组合) -->
          <div id="u63" class="ax_default" data-left="290" data-top="305" data-width="181" data-height="46">

            <!-- Unnamed (矩形) -->
            <div id="u64" class="ax_default _形状">
              <div id="u64_div" class=""></div>
              <div id="u64_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>

          <!-- Unnamed (单选按钮) -->
          <div id="u65" class="ax_default radio_button selected">
            <label id="u65_input_label" for="u65_input" style="position: absolute; left: 0px;">
              <img id="u65_img" class="img " src="images/海融宝签约_个人__f501_f502_/u65_selected.svg"/>
              <div id="u65_text" class="text ">
                <p><span>&nbsp;男</span></p>
              </div>
            </label>
            <input id="u65_input" type="radio" value="radio" name="u65" checked/>
          </div>

          <!-- Unnamed (单选按钮) -->
          <div id="u66" class="ax_default radio_button">
            <label id="u66_input_label" for="u66_input" style="position: absolute; left: 0px;">
              <img id="u66_img" class="img " src="images/海融宝签约_个人__f501_f502_/u66.svg"/>
              <div id="u66_text" class="text ">
                <p><span>&nbsp; 女</span></p>
              </div>
            </label>
            <input id="u66_input" type="radio" value="radio" name="u66"/>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u67" class="ax_default" data-left="25" data-top="305" data-width="247" data-height="46">

          <!-- Unnamed (矩形) -->
          <div id="u68" class="ax_default _形状">
            <div id="u68_div" class=""></div>
            <div id="u68_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u69" class="ax_default _文本段落">
            <div id="u69_div" class=""></div>
            <div id="u69_text" class="text ">
              <p><span>*姓名</span></p>
            </div>
          </div>

          <!-- Unnamed (文本框) -->
          <div id="u70" class="ax_default text_field">
            <div id="u70_div" class=""></div>
            <input id="u70_input" type="text" value="张建国" class="u70_input"/>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u71" class="ax_default" data-left="25" data-top="353" data-width="446" data-height="46">

        <!-- Unnamed (矩形) -->
        <div id="u72" class="ax_default _形状">
          <div id="u72_div" class=""></div>
          <div id="u72_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u73" class="ax_default _文本段落">
          <div id="u73_div" class=""></div>
          <div id="u73_text" class="text ">
            <p><span>*手机</span></p>
          </div>
        </div>

        <!-- Unnamed (文本框) -->
        <div id="u74" class="ax_default text_field">
          <div id="u74_div" class=""></div>
          <input id="u74_input" type="text" value="134 8250 8234" class="u74_input"/>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u75" class="ax_default _文本段落">
        <div id="u75_div" class=""></div>
        <div id="u75_text" class="text ">
          <p><span>提示：请填写开通邮储银行数字人民币钱包的钱包手机号</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u76" class="ax_default primary_button">
        <div id="u76_div" class=""></div>
        <div id="u76_text" class="text ">
          <p><span>提交签约</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u77" class="ax_default _文本段落">
        <div id="u77_div" class=""></div>
        <div id="u77_text" class="text ">
          <p><span>提示：请务必开通邮储银行数字人民币钱包</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u78" class="ax_default _文本段落">
        <div id="u78_div" class=""></div>
        <div id="u78_text" class="text ">
          <p><span>说明：以上信息自动从个人基本信息中获取，新变更修改内容将更新内容替换原基本信息</span></p><p><span><br></span></p><p><span>传参按邮储要求，相关信息保留平台。</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u79" class="ax_default _文本段落">
        <div id="u79_div" class=""></div>
        <div id="u79_text" class="text ">
          <p style="font-size:20px;"><span>F501 会员签约</span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span>节点属性&nbsp; &nbsp;&nbsp; 中文描述&nbsp; &nbsp;&nbsp; 数据类型&nbsp; &nbsp;&nbsp; 必输&nbsp; &nbsp;&nbsp; 说明</span></p><p style="font-size:13px;"><span>platCustId&nbsp; &nbsp;&nbsp; 平台客户ID&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 平台给会员分配的ID，会员注册登陆平台系统的ID</span></p><p style="font-size:13px;"><span>custIdType&nbsp; &nbsp;&nbsp; 客户证件类型&nbsp; &nbsp;&nbsp; char(4)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 只支持个人.&nbsp; &nbsp; &nbsp;&nbsp; 证件类型：IT01:居民身份证；IT02:军官证；IT03:护照；IT04-户口薄；IT05-士兵证；IT06-港澳往来内地通行证；IT07-台湾同胞来往内地通行证；IT08-临时身份证；IT09-外国人居留证</span></p><p style="font-size:13px;"><span>custIdNo&nbsp; &nbsp;&nbsp; 客户证件号码&nbsp; &nbsp;&nbsp; char(30)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; </span></p><p style="font-size:13px;"><span>custName&nbsp; &nbsp;&nbsp; 客户姓名&nbsp; &nbsp;&nbsp; char(60)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 证件名称</span></p><p style="font-size:13px;"><span>merchantId&nbsp; &nbsp; &nbsp; 合作方编号（商户号）&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 服开商户号，由分行老师提供</span></p><p style="font-size:13px;"><span>appID&nbsp; &nbsp; &nbsp; 应用ID&nbsp; &nbsp;&nbsp; char(30)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 商户在服开平台申请的合约参数 ，由分行老师提供</span></p><p style="font-size:13px;"><span>redirectUrl&nbsp; &nbsp;&nbsp; 回调的业务方地址&nbsp; &nbsp;&nbsp; char(256)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; 流程结束后跳转地址</span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span>节点属性&nbsp; &nbsp;&nbsp; 中文描述&nbsp; &nbsp;&nbsp; 数据类型&nbsp; &nbsp;&nbsp; 必输&nbsp; &nbsp;&nbsp; 说明</span></p><p style="font-size:13px;"><span>code&nbsp; &nbsp;&nbsp; 响应码&nbsp; &nbsp;&nbsp; char(6)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; </span></p><p style="font-size:13px;"><span>message&nbsp; &nbsp;&nbsp; 响应信息&nbsp; &nbsp;&nbsp; char(256)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; </span></p><p style="font-size:13px;"><span>userSignUrl&nbsp; &nbsp;&nbsp; 个人客户签约URL&nbsp; &nbsp;&nbsp; char(500)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; 页面URL</span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span><br></span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u80" class="ax_default _文本段落">
        <div id="u80_div" class=""></div>
        <div id="u80_text" class="text ">
          <p style="font-size:20px;"><span>F502 会员签约查询</span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span>节点属性&nbsp; &nbsp;&nbsp; 中文描述&nbsp; &nbsp;&nbsp; 数据类型&nbsp; &nbsp;&nbsp; 必输&nbsp; &nbsp;&nbsp; 说明</span></p><p style="font-size:13px;"><span>platCustId&nbsp; &nbsp;&nbsp; 平台客户ID&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 平台给会员分配的ID，会员注册登陆平台系统的ID</span></p><p style="font-size:13px;"><span>custIdType&nbsp; &nbsp;&nbsp; 证件类型&nbsp; &nbsp;&nbsp; char(4)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 只支持个人&nbsp; &nbsp; 证件类型：IT01:居民身份证；IT02:军官证；IT03:护照；IT04-户口薄；IT05-士兵证；IT06-港澳往来内地通行证；IT07-台湾同胞来往内地通行证；IT08-临时身份证；IT09-外国人居留证</span></p><p style="font-size:13px;"><span>custIdNo&nbsp; &nbsp;&nbsp; 证件号码&nbsp; &nbsp;&nbsp; char(30)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 证件号码</span></p><p style="font-size:13px;"><span>custName&nbsp; &nbsp;&nbsp; 客户名称&nbsp; &nbsp;&nbsp; char(100)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 证件名称</span></p><p style="font-size:13px;"><span>custType&nbsp; &nbsp;&nbsp; 客户类型&nbsp; &nbsp;&nbsp; char(2)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; A-个人</span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span>节点属性&nbsp; &nbsp;&nbsp; 中文描述&nbsp; &nbsp;&nbsp; 数据类型&nbsp; &nbsp;&nbsp; 必输&nbsp; &nbsp;&nbsp; 说明</span></p><p style="font-size:13px;"><span>platCustId&nbsp; &nbsp;&nbsp; 平台客户ID&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 平台给会员分配的ID，会员注册登陆平台系统的ID</span></p><p style="font-size:13px;"><span>signContStatus&nbsp; &nbsp;&nbsp; 签约状态&nbsp; &nbsp;&nbsp; char(2)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 0-已签约；1-未签约；2-签约处理中；</span></p><p style="font-size:13px;"><span>custId&nbsp; &nbsp;&nbsp; 客户id&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; 个人签约时生成的ID</span></p><p style="font-size:13px;"><span>signAgrNo&nbsp; &nbsp;&nbsp; 签约协议号&nbsp; &nbsp;&nbsp; char(64)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; 银行生成的签约协议号</span></p><p style="font-size:13px;"><span><br></span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u81" class="ax_default" data-left="521" data-top="10" data-width="426" data-height="18">

        <!-- Unnamed (矩形) -->
        <div id="u82" class="ax_default _文本段落">
          <div id="u82_div" class=""></div>
          <div id="u82_text" class="text ">
            <p><span>交易通道</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u83" class="ax_default _文本段落">
          <div id="u83_div" class=""></div>
          <div id="u83_text" class="text ">
            <p><span>数优联/数市联/数蛋联/数药联/数牛联/数菜联</span></p>
          </div>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
