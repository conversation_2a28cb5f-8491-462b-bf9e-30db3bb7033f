﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:876px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u3954_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:1280px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:50px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3954 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:1280px;
  display:flex;
  opacity:0.49;
}
#u3954 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3954_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3955_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u3955 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:20px;
  width:51px;
  height:18px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u3955 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3955_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3956_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:18px;
}
#u3956 {
  border-width:0px;
  position:absolute;
  left:425px;
  top:19px;
  width:23px;
  height:18px;
  display:flex;
}
#u3956 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3956_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3957_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:16px;
}
#u3957 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:21px;
  width:26px;
  height:16px;
  display:flex;
}
#u3957 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3957_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3958_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:25px;
}
#u3958 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:50px;
  width:24px;
  height:25px;
  display:flex;
}
#u3958 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3958_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3959_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:22px;
}
#u3959 {
  border-width:0px;
  position:absolute;
  left:467px;
  top:56px;
  width:21px;
  height:22px;
  display:flex;
}
#u3959 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3959_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3960_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:252px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u3960 {
  border-width:0px;
  position:absolute;
  left:124px;
  top:49px;
  width:252px;
  height:25px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u3960 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3960_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3961 {
  position:fixed;
  left:50%;
  margin-left:-75px;
  top:50%;
  margin-top:-25px;
  visibility:hidden;
}
#u3961_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3961_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u3962_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u3962 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u3962 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u3962_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3961_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u3961_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u3963_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u3963 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u3963 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u3963_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3964_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u3964 {
  border-width:0px;
  position:absolute;
  left:60px;
  top:16px;
  width:80px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u3964 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3964_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3965_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u3965 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:10px;
  width:30px;
  height:30px;
  display:flex;
}
#u3965 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3965_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3966_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:228px;
  height:11px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  text-align:center;
}
#u3966 {
  border-width:0px;
  position:absolute;
  left:136px;
  top:71px;
  width:228px;
  height:11px;
  display:flex;
  font-size:10px;
  text-align:center;
}
#u3966 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3966_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3967_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:480px;
  height:480px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3967 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:448px;
  width:480px;
  height:480px;
  display:flex;
}
#u3967 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3967_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3969_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:219px;
  height:141px;
}
#u3969 {
  border-width:0px;
  position:absolute;
  left:263px;
  top:514px;
  width:219px;
  height:141px;
  display:flex;
}
#u3969 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3969_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3970_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  height:141px;
}
#u3970 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:514px;
  width:221px;
  height:141px;
  display:flex;
}
#u3970 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3970_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3971 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3972_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:59px;
  height:60px;
}
#u3972 {
  border-width:0px;
  position:absolute;
  left:121px;
  top:546px;
  width:59px;
  height:60px;
  display:flex;
}
#u3972 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3972_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3973_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u3973 {
  border-width:0px;
  position:absolute;
  left:136px;
  top:561px;
  width:30px;
  height:30px;
  display:flex;
}
#u3973 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3973_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3974_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:142px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  text-align:center;
}
#u3974 {
  border-width:0px;
  position:absolute;
  left:81px;
  top:622px;
  width:142px;
  height:21px;
  display:flex;
  font-size:18px;
  text-align:center;
}
#u3974 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3974_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3975 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3976_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:59px;
  height:60px;
}
#u3976 {
  border-width:0px;
  position:absolute;
  left:343px;
  top:546px;
  width:59px;
  height:60px;
  display:flex;
}
#u3976 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3976_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3977_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u3977 {
  border-width:0px;
  position:absolute;
  left:358px;
  top:561px;
  width:30px;
  height:30px;
  display:flex;
}
#u3977 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3977_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3978_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:142px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  text-align:center;
}
#u3978 {
  border-width:0px;
  position:absolute;
  left:301px;
  top:622px;
  width:142px;
  height:21px;
  display:flex;
  font-size:18px;
  text-align:center;
}
#u3978 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3978_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3979 {
  border-width:0px;
  position:absolute;
  left:166px;
  top:525px;
  width:218px;
  height:130px;
  visibility:hidden;
}
#u3979_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:218px;
  height:130px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3979_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u3980_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:130px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3980 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:130px;
  display:flex;
}
#u3980 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3980_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3981_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:28px;
}
#u3981 {
  border-width:0px;
  position:absolute;
  left:197px;
  top:0px;
  width:23px;
  height:32px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:28px;
}
#u3981 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3981_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3982_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  height:2px;
}
#u3982 {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:36px;
  width:220px;
  height:1px;
  display:flex;
}
#u3982 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3982_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3983_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:165px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  text-align:center;
}
#u3983 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:11px;
  width:165px;
  height:21px;
  display:flex;
  font-size:18px;
  text-align:center;
}
#u3983 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3983_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3984_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  height:2px;
}
#u3984 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:70px;
  width:220px;
  height:1px;
  display:flex;
}
#u3984 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3984_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3985_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  text-align:center;
}
#u3985 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:45px;
  width:150px;
  height:21px;
  display:flex;
  font-size:18px;
  text-align:center;
}
#u3985 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3985_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3986_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u3986 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:81px;
  width:140px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u3986 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3986_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3987_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u3987 {
  border-width:0px;
  position:absolute;
  left:52px;
  top:492px;
  width:201px;
  height:22px;
  display:flex;
  font-size:18px;
}
#u3987 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3987_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3988_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u3988 {
  border-width:0px;
  position:absolute;
  left:38px;
  top:456px;
  width:72px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u3988 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3988_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3990_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3990 {
  border-width:0px;
  position:absolute;
  left:31px;
  top:783px;
  width:450px;
  height:54px;
  display:flex;
}
#u3990 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3990_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3991_input {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3991_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3991_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u3991 {
  border-width:0px;
  position:absolute;
  left:141px;
  top:795px;
  width:330px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u3991 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3991_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u3991.disabled {
}
#u3992_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:266px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u3992 {
  border-width:0px;
  position:absolute;
  left:146px;
  top:795px;
  width:266px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u3992 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3992_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3993_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u3993 {
  border-width:0px;
  position:absolute;
  left:417px;
  top:795px;
  width:54px;
  height:30px;
  display:flex;
  font-size:16px;
}
#u3993 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3993_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3994_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u3994 {
  border-width:0px;
  position:absolute;
  left:46px;
  top:795px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u3994 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3994_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3996 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3997_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:304px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u3997 {
  border-width:0px;
  position:absolute;
  left:51px;
  top:839px;
  width:304px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u3997 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3997_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3998 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3999_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:205px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3999 {
  border-width:0px;
  position:absolute;
  left:31px;
  top:870px;
  width:205px;
  height:40px;
  display:flex;
}
#u3999 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3999_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4000_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:205px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4000 {
  border-width:0px;
  position:absolute;
  left:276px;
  top:870px;
  width:205px;
  height:40px;
  display:flex;
}
#u4000 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4000_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u4001 {
  border-width:0px;
  position:absolute;
  left:191px;
  top:878px;
  width:25px;
  height:25px;
  display:flex;
}
#u4001 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4001_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u4002 {
  border-width:0px;
  position:absolute;
  left:440px;
  top:878px;
  width:25px;
  height:25px;
  display:flex;
}
#u4002 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4002_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4003_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  color:#7F7F7F;
}
#u4003 {
  border-width:0px;
  position:absolute;
  left:51px;
  top:875px;
  width:108px;
  height:30px;
  display:flex;
  font-size:18px;
  color:#7F7F7F;
}
#u4003 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4003_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4004_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  color:#7F7F7F;
}
#u4004 {
  border-width:0px;
  position:absolute;
  left:301px;
  top:875px;
  width:108px;
  height:30px;
  display:flex;
  font-size:18px;
  color:#7F7F7F;
}
#u4004 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4004_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4005_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:18px;
  height:7px;
}
#u4005 {
  border-width:0px;
  position:absolute;
  left:247px;
  top:884px;
  width:14px;
  height:3px;
  display:flex;
}
#u4005 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4005_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4006_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:480px;
  height:353px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4006 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:88px;
  width:480px;
  height:353px;
  display:flex;
}
#u4006 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4006_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4007 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4008_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:225px;
  height:180px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#AEAEAE;
  text-align:left;
}
#u4008 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:240px;
  width:225px;
  height:180px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#AEAEAE;
  text-align:left;
}
#u4008 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4008_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4010 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4011_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:45px;
}
#u4011 {
  border-width:0px;
  position:absolute;
  left:47px;
  top:252px;
  width:45px;
  height:45px;
  display:flex;
  color:#7F7F7F;
}
#u4011 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4011_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4012_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:151px;
  height:42px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#7F7F7F;
  text-align:left;
}
#u4012 {
  border-width:0px;
  position:absolute;
  left:101px;
  top:254px;
  width:151px;
  height:42px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#7F7F7F;
  text-align:left;
}
#u4012 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4012_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4013 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:252px;
  width:210px;
  height:120px;
  visibility:hidden;
}
#u4013_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:210px;
  height:120px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4013_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u4014_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:120px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u4014 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:120px;
  display:flex;
  font-size:18px;
}
#u4014 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4014_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4015_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u4015 {
  border-width:0px;
  position:absolute;
  left:179px;
  top:2px;
  width:23px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u4015 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4015_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4016_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:2px;
}
#u4016 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:200px;
  height:1px;
  display:flex;
  font-size:18px;
}
#u4016 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4016_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4017_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:165px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  text-align:center;
}
#u4017 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:12px;
  width:165px;
  height:21px;
  display:flex;
  font-size:18px;
  text-align:center;
}
#u4017 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4017_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4018_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:2px;
}
#u4018 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:80px;
  width:200px;
  height:1px;
  display:flex;
  font-size:18px;
}
#u4018 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4018_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4019_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:173px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  text-align:center;
}
#u4019 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:50px;
  width:173px;
  height:21px;
  display:flex;
  font-size:18px;
  text-align:center;
}
#u4019 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4019_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4020_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u4020 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:85px;
  width:120px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u4020 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4020_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4022_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4022 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:122px;
  width:450px;
  height:54px;
  display:flex;
}
#u4022 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4022_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4023_input {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4023_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4023_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4023 {
  border-width:0px;
  position:absolute;
  left:140px;
  top:134px;
  width:330px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u4023 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4023_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4023.disabled {
}
#u4024_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:266px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4024 {
  border-width:0px;
  position:absolute;
  left:145px;
  top:134px;
  width:266px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u4024 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4024_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4025_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u4025 {
  border-width:0px;
  position:absolute;
  left:416px;
  top:134px;
  width:54px;
  height:30px;
  display:flex;
  font-size:16px;
}
#u4025 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4025_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4026_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u4026 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:134px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u4026 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4026_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4028_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4028 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:178px;
  width:450px;
  height:54px;
  display:flex;
}
#u4028 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4028_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4029_input {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4029_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4029_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4029 {
  border-width:0px;
  position:absolute;
  left:140px;
  top:190px;
  width:330px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u4029 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4029_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4029.disabled {
}
#u4030_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:266px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4030 {
  border-width:0px;
  position:absolute;
  left:145px;
  top:190px;
  width:266px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u4030 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4030_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4031_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u4031 {
  border-width:0px;
  position:absolute;
  left:416px;
  top:190px;
  width:54px;
  height:30px;
  display:flex;
  font-size:16px;
}
#u4031 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4031_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4032_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u4032 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:190px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u4032 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4032_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4033_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u4033 {
  border-width:0px;
  position:absolute;
  left:24px;
  top:98px;
  width:72px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u4033 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4033_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4035_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4035 {
  border-width:0px;
  position:absolute;
  left:31px;
  top:671px;
  width:450px;
  height:54px;
  display:flex;
}
#u4035 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4035_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4036_input {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4036_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4036_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4036 {
  border-width:0px;
  position:absolute;
  left:141px;
  top:683px;
  width:330px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u4036 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4036_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4036.disabled {
}
#u4037_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:266px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4037 {
  border-width:0px;
  position:absolute;
  left:146px;
  top:683px;
  width:266px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u4037 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4037_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4038_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u4038 {
  border-width:0px;
  position:absolute;
  left:417px;
  top:683px;
  width:54px;
  height:30px;
  display:flex;
  font-size:16px;
}
#u4038 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4038_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4039_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u4039 {
  border-width:0px;
  position:absolute;
  left:46px;
  top:683px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u4039 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4039_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4041_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4041 {
  border-width:0px;
  position:absolute;
  left:31px;
  top:727px;
  width:450px;
  height:54px;
  display:flex;
}
#u4041 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4041_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4042_input {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4042_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4042_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4042 {
  border-width:0px;
  position:absolute;
  left:141px;
  top:739px;
  width:330px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u4042 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4042_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4042.disabled {
}
#u4043_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:266px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4043 {
  border-width:0px;
  position:absolute;
  left:146px;
  top:739px;
  width:266px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u4043 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4043_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4044_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u4044 {
  border-width:0px;
  position:absolute;
  left:417px;
  top:739px;
  width:54px;
  height:30px;
  display:flex;
  font-size:16px;
}
#u4044 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4044_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4045_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u4045 {
  border-width:0px;
  position:absolute;
  left:46px;
  top:739px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u4045 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4045_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4046_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:480px;
  height:265px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4046 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:943px;
  width:480px;
  height:265px;
  display:flex;
}
#u4046 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4046_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4048_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4048 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:984px;
  width:450px;
  height:54px;
  display:flex;
}
#u4048 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4048_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4049_input {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4049_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4049_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4049 {
  border-width:0px;
  position:absolute;
  left:140px;
  top:996px;
  width:330px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u4049 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4049_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4049.disabled {
}
#u4050_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:266px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4050 {
  border-width:0px;
  position:absolute;
  left:145px;
  top:996px;
  width:266px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u4050 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4050_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4051_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u4051 {
  border-width:0px;
  position:absolute;
  left:416px;
  top:996px;
  width:54px;
  height:30px;
  display:flex;
  font-size:16px;
}
#u4051 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4051_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4052_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u4052 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:996px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u4052 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4052_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4054_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4054 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:1040px;
  width:450px;
  height:54px;
  display:flex;
}
#u4054 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4054_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4055_input {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4055_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4055_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4055 {
  border-width:0px;
  position:absolute;
  left:140px;
  top:1052px;
  width:330px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u4055 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4055_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4055.disabled {
}
#u4056_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:266px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u4056 {
  border-width:0px;
  position:absolute;
  left:145px;
  top:1052px;
  width:266px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u4056 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4056_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4057_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u4057 {
  border-width:0px;
  position:absolute;
  left:416px;
  top:1052px;
  width:54px;
  height:30px;
  display:flex;
  font-size:16px;
}
#u4057 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4057_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4058_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u4058 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:1052px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u4058 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4058_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4059_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u4059 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:953px;
  width:90px;
  height:21px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u4059 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4059_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4060_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:377px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4060 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:1114px;
  width:377px;
  height:15px;
  display:flex;
}
#u4060 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4060_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4061_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4061 {
  border-width:0px;
  position:absolute;
  left:60px;
  top:1152px;
  width:140px;
  height:30px;
  display:flex;
}
#u4061 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4061_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4062_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4062 {
  border-width:0px;
  position:absolute;
  left:300px;
  top:1152px;
  width:140px;
  height:30px;
  display:flex;
}
#u4062 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4062_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4063 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4064_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:225px;
  height:180px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#AEAEAE;
  text-align:left;
}
#u4064 {
  border-width:0px;
  position:absolute;
  left:256px;
  top:240px;
  width:225px;
  height:180px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#AEAEAE;
  text-align:left;
}
#u4064 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4064_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4066 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4067_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:45px;
}
#u4067 {
  border-width:0px;
  position:absolute;
  left:273px;
  top:252px;
  width:45px;
  height:45px;
  display:flex;
  color:#7F7F7F;
}
#u4067 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4067_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4068_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:151px;
  height:42px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#7F7F7F;
  text-align:left;
}
#u4068 {
  border-width:0px;
  position:absolute;
  left:327px;
  top:254px;
  width:151px;
  height:42px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#7F7F7F;
  text-align:left;
}
#u4068 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4068_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4069 {
  border-width:0px;
  position:absolute;
  left:258px;
  top:252px;
  width:210px;
  height:120px;
  visibility:hidden;
}
#u4069_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:210px;
  height:120px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4069_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u4070_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:120px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u4070 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:120px;
  display:flex;
  font-size:18px;
}
#u4070 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4070_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4071_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u4071 {
  border-width:0px;
  position:absolute;
  left:179px;
  top:2px;
  width:23px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u4071 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4071_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4072_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:2px;
}
#u4072 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:200px;
  height:1px;
  display:flex;
  font-size:18px;
}
#u4072 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4072_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4073_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:165px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  text-align:center;
}
#u4073 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:12px;
  width:165px;
  height:21px;
  display:flex;
  font-size:18px;
  text-align:center;
}
#u4073 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4073_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4074_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:2px;
}
#u4074 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:80px;
  width:200px;
  height:1px;
  display:flex;
  font-size:18px;
}
#u4074 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4074_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4075_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:173px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  text-align:center;
}
#u4075 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:50px;
  width:173px;
  height:21px;
  display:flex;
  font-size:18px;
  text-align:center;
}
#u4075 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4075_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4076_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u4076 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:85px;
  width:120px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u4076 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4076_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4077_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4077 {
  border-width:0px;
  position:absolute;
  left:616px;
  top:32px;
  width:260px;
  height:30px;
  display:flex;
}
#u4077 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4077_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4078 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:38px;
  width:50px;
  height:50px;
  overflow:hidden;
  background-image:url('../../resources/images/transparent.gif');
}
