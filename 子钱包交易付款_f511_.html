﻿<!DOCTYPE html>
<html>
  <head>
    <title>子钱包交易付款(F511)</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/子钱包交易付款_f511_/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/子钱包交易付款_f511_/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (基础app框架(H5)) -->

      <!-- Unnamed (矩形) -->
      <div id="u788" class="ax_default box_1">
        <div id="u788_div" class=""></div>
        <div id="u788_text" class="text ">
          <p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u789" class="ax_default _二级标题">
        <div id="u789_div" class=""></div>
        <div id="u789_text" class="text ">
          <p><span>16:18</span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u790" class="ax_default icon">
        <img id="u790_img" class="img " src="images/海融宝签约_个人__f501_f502_/u3.svg"/>
        <div id="u790_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u791" class="ax_default icon">
        <img id="u791_img" class="img " src="images/海融宝签约_个人__f501_f502_/u4.svg"/>
        <div id="u791_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u792" class="ax_default _文本段落">
        <div id="u792_div" class=""></div>
        <div id="u792_text" class="text ">
          <p><span>交易付款</span></p>
        </div>
      </div>

      <!-- 操作状态 (动态面板) -->
      <div id="u793" class="ax_default ax_default_hidden" data-label="操作状态" style="display:none; visibility: hidden">
        <div id="u793_state0" class="panel_state" data-label="操作成功" style="">
          <div id="u793_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u794" class="ax_default box_3">
              <div id="u794_div" class=""></div>
              <div id="u794_text" class="text ">
                <p><span>操作成功！</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u793_state1" class="panel_state" data-label="操作失败" style="visibility: hidden;">
          <div id="u793_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u795" class="ax_default box_3">
              <div id="u795_div" class=""></div>
              <div id="u795_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u796" class="ax_default _文本段落">
              <div id="u796_div" class=""></div>
              <div id="u796_text" class="text ">
                <p><span>操作失败！</span></p>
              </div>
            </div>

            <!-- Unnamed (图片 ) -->
            <div id="u797" class="ax_default _图片_">
              <img id="u797_img" class="img " src="images/海融宝签约_个人__f501_f502_/u10.png"/>
              <div id="u797_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u798" class="ax_default _图片_">
        <img id="u798_img" class="img " src="images/海融宝签约_个人__f501_f502_/u11.png"/>
        <div id="u798_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u799" class="ax_default icon">
        <img id="u799_img" class="img " src="images/海融宝签约_个人__f501_f502_/u12.svg"/>
        <div id="u799_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u800" class="ax_default _文本段落">
        <div id="u800_div" class=""></div>
        <div id="u800_text" class="text ">
          <p><span>hrypt.com</span></p>
        </div>
      </div>
      <div id="u787" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (矩形) -->
      <div id="u801" class="ax_default primary_button">
        <div id="u801_div" class=""></div>
        <div id="u801_text" class="text ">
          <p><span>交易支付</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u802" class="ax_default _形状">
        <div id="u802_div" class=""></div>
        <div id="u802_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u803" class="ax_default _文本段落">
        <div id="u803_div" class=""></div>
        <div id="u803_text" class="text ">
          <p><span>交易时间</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u804" class="ax_default _文本段落">
        <div id="u804_div" class=""></div>
        <div id="u804_text" class="text ">
          <p><span>商品</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u805" class="ax_default _文本段落">
        <div id="u805_div" class=""></div>
        <div id="u805_text" class="text ">
          <p><span>商户全称</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u806" class="ax_default _文本段落">
        <div id="u806_div" class=""></div>
        <div id="u806_text" class="text ">
          <p><span>收单机构</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u807" class="ax_default _文本段落">
        <div id="u807_div" class=""></div>
        <div id="u807_text" class="text ">
          <p><span>商户钱包ID</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u808" class="ax_default" data-left="559" data-top="85" data-width="426" data-height="18">

        <!-- Unnamed (矩形) -->
        <div id="u809" class="ax_default _文本段落">
          <div id="u809_div" class=""></div>
          <div id="u809_text" class="text ">
            <p><span>订单单号</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u810" class="ax_default _文本段落">
          <div id="u810_div" class=""></div>
          <div id="u810_text" class="text ">
            <p><span>4200002674202505182886982550</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u811" class="ax_default _文本段落">
        <div id="u811_div" class=""></div>
        <div id="u811_text" class="text ">
          <p><span>2025年5月18日 14:21:31</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u812" class="ax_default _文本段落">
        <div id="u812_div" class=""></div>
        <div id="u812_text" class="text ">
          <p><span>福建马尾三文鱼刺500g现切(FJSWY-350008)</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u813" class="ax_default _文本段落">
        <div id="u813_div" class=""></div>
        <div id="u813_text" class="text ">
          <p><span>海创未来(福建)科技有限公司</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u814" class="ax_default _文本段落">
        <div id="u814_div" class=""></div>
        <div id="u814_text" class="text ">
          <p><span>中国邮政储蓄银行福建省分行</span></p><p><span><br></span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u815" class="ax_default _文本段落">
        <div id="u815_div" class=""></div>
        <div id="u815_text" class="text ">
          <p><span>邮储银行 数字人民币合约子钱包id（1618）</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u816" class="ax_default" data-left="1031" data-top="18" data-width="439" data-height="36">

        <!-- Unnamed (矩形) -->
        <div id="u817" class="ax_default _文本段落">
          <div id="u817_div" class=""></div>
          <div id="u817_text" class="text ">
            <p><span>交易确认</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u818" class="ax_default" data-left="1123" data-top="18" data-width="347" data-height="36">

          <!-- Unnamed (文本框) -->
          <div id="u819" class="ax_default text_field">
            <div id="u819_div" class=""></div>
            <input id="u819_input" type="text" value="" class="u819_input"/>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u820" class="ax_default" data-left="1135" data-top="22" data-width="321" data-height="30">

            <!-- 叫号面板按钮 (动态面板) -->
            <div id="u821" class="ax_default" data-label="叫号面板按钮">
              <div id="u821_state0" class="panel_state" data-label="State1" style="">
                <div id="u821_state0_content" class="panel_state_content">

                  <!-- Unnamed (矩形) -->
                  <div id="u822" class="ax_default box_3">
                    <div id="u822_div" class=""></div>
                    <div id="u822_text" class="text ">
                      <p><span>获取验证码</span></p>
                    </div>
                  </div>
                </div>
              </div>
              <div id="u821_state1" class="panel_state" data-label="State2" style="visibility: hidden;">
                <div id="u821_state1_content" class="panel_state_content">

                  <!-- Unnamed (矩形) -->
                  <div id="u823" class="ax_default box_3">
                    <div id="u823_div" class=""></div>
                    <div id="u823_text" class="text ">
                      <p><span>s</span></p>
                    </div>
                  </div>

                  <!-- 叫号倒计时 (文本框) -->
                  <div id="u824" class="ax_default text_field ax_default_hidden" data-label="叫号倒计时" style="display:none; visibility: hidden">
                    <div id="u824_div" class=""></div>
                    <input id="u824_input" type="text" value="15" class="u824_input" readonly/>
                  </div>
                </div>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u825" class="ax_default _文本段落">
              <div id="u825_div" class=""></div>
              <div id="u825_text" class="text ">
                <p><span>输入短信验证码</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u826" class="ax_default" data-left="559" data-top="283" data-width="426" data-height="18">

        <!-- Unnamed (矩形) -->
        <div id="u827" class="ax_default _文本段落">
          <div id="u827_div" class=""></div>
          <div id="u827_text" class="text ">
            <p><span>买家钱包ID</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u828" class="ax_default _文本段落">
          <div id="u828_div" class=""></div>
          <div id="u828_text" class="text ">
            <p><span>邮储银行 数字人民币合约子钱包id（0413）</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u829" class="ax_default" data-left="559" data-top="316" data-width="426" data-height="18">

        <!-- Unnamed (矩形) -->
        <div id="u830" class="ax_default _文本段落">
          <div id="u830_div" class=""></div>
          <div id="u830_text" class="text ">
            <p><span>商家编号</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u831" class="ax_default _文本段落">
          <div id="u831_div" class=""></div>
          <div id="u831_text" class="text ">
            <p><span>130233549211125051820080409610</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u832" class="ax_default" data-left="559" data-top="349" data-width="426" data-height="18">

        <!-- Unnamed (矩形) -->
        <div id="u833" class="ax_default _文本段落">
          <div id="u833_div" class=""></div>
          <div id="u833_text" class="text ">
            <p><span>商户电话</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u834" class="ax_default _文本段落">
          <div id="u834_div" class=""></div>
          <div id="u834_text" class="text ">
            <p><span>400-668-7873</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u835" class="ax_default" data-left="559" data-top="52" data-width="426" data-height="18">

        <!-- Unnamed (矩形) -->
        <div id="u836" class="ax_default _文本段落">
          <div id="u836_div" class=""></div>
          <div id="u836_text" class="text ">
            <p><span>交易金额</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u837" class="ax_default _文本段落">
          <div id="u837_div" class=""></div>
          <div id="u837_text" class="text ">
            <p><span>1,688.00</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u838" class="ax_default _文本段落">
        <div id="u838_div" class=""></div>
        <div id="u838_text" class="text ">
          <p><span>交易信息（应用平台接口数据）</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u839" class="ax_default" data-left="559" data-top="382" data-width="426" data-height="18">

        <!-- Unnamed (矩形) -->
        <div id="u840" class="ax_default _文本段落">
          <div id="u840_div" class=""></div>
          <div id="u840_text" class="text ">
            <p><span>操作附言</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u841" class="ax_default _文本段落">
          <div id="u841_div" class=""></div>
          <div id="u841_text" class="text ">
            <p><span>无</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u842" class="ax_default _文本段落">
        <div id="u842_div" class=""></div>
        <div id="u842_text" class="text ">
          <p style="font-size:20px;"><span style="color:#000000;">F511交易结算创单</span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span>节点属性&nbsp; &nbsp;&nbsp; 中文描述&nbsp; &nbsp;&nbsp; 数据类型&nbsp; &nbsp;&nbsp; 必输&nbsp; &nbsp;&nbsp; 说明</span></p><p style="font-size:13px;"><span>paymCustId&nbsp; &nbsp;&nbsp; 付款方客户ID&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 个人会员签约时生成</span></p><p style="font-size:13px;"><span>recpayCustId&nbsp; &nbsp;&nbsp; 收款方客户ID&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 个人会员签约时生成</span></p><p style="font-size:13px;"><span>signAgrNo&nbsp; &nbsp;&nbsp; 签约协议号&nbsp; &nbsp;&nbsp; char(64)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 付款方的签约协议号，个人会员签约时生成</span></p><p style="font-size:13px;"><span>txCurr&nbsp; &nbsp;&nbsp; 交易币种&nbsp; &nbsp;&nbsp; char(3)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 人民币CNY</span></p><p style="font-size:13px;"><span>txAmt&nbsp; &nbsp;&nbsp; 交易金额&nbsp; &nbsp;&nbsp; char(22)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 保留两位小数</span></p><p style="font-size:13px;"><span>platOrderNo&nbsp; &nbsp;&nbsp; 平台结算订单号&nbsp; &nbsp;&nbsp; char(100)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 平台生成的唯一标识,银企客户号+yyyyMMdd+序号</span></p><p style="font-size:13px;"><span>goodsName&nbsp; &nbsp;&nbsp; 商品名称&nbsp; &nbsp;&nbsp; char(70)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; </span></p><p style="font-size:13px;"><span>goodsNum&nbsp; &nbsp;&nbsp; 商品数量&nbsp; &nbsp;&nbsp; char(4)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; </span></p><p style="font-size:13px;"><span>frzFlag&nbsp; &nbsp;&nbsp; 是否冻结标识&nbsp; &nbsp;&nbsp; char(1)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 0-否，1是</span></p><p style="font-size:13px;"><span>txPtscrt&nbsp; &nbsp;&nbsp; 交易附言&nbsp; &nbsp;&nbsp; char(300)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; </span></p><p style="font-size:13px;"><span>platSubOrderNo&nbsp; &nbsp;&nbsp; 平台结算子订单号&nbsp; &nbsp;&nbsp; char(100)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 针对拆单分批次支付场景，如果不分批次则与结算订单号保持一致</span></p><p style="font-size:13px;"><span>merchantId&nbsp; &nbsp; &nbsp; 合作方编号（商户号）&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 服开商户号，由分行老师提供</span></p><p style="font-size:13px;"><span>appID&nbsp; &nbsp; &nbsp; 应用ID&nbsp; &nbsp;&nbsp; char(30)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 商户在服开平台申请的合约参数 ，由分行老师提供</span></p><p style="font-size:13px;"><span>redirectUrl&nbsp; &nbsp;&nbsp; 回调的业务方地址&nbsp; &nbsp;&nbsp; char(256)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; 流程结束后跳转地址</span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span>节点属性&nbsp; &nbsp;&nbsp; 中文描述&nbsp; &nbsp;&nbsp; 数据类型&nbsp; &nbsp;&nbsp; 必输&nbsp; &nbsp;&nbsp; 说明</span></p><p style="font-size:13px;"><span>userLgnUrl&nbsp; &nbsp;&nbsp; 用户登录URL&nbsp; &nbsp;&nbsp; Char(500)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 交易结算创单页面URL</span></p><p style="font-size:13px;"><span><br></span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u843" class="ax_default _形状">
        <div id="u843_div" class=""></div>
        <div id="u843_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u844" class="ax_default" data-left="24" data-top="191" data-width="440" data-height="30">

        <!-- Unnamed (矩形) -->
        <div id="u845" class="ax_default _文本段落">
          <div id="u845_div" class=""></div>
          <div id="u845_text" class="text ">
            <p><span>商品信息</span></p>
          </div>
        </div>

        <!-- Unnamed (文本框) -->
        <div id="u846" class="ax_default text_field">
          <div id="u846_div" class=""></div>
          <input id="u846_input" type="text" value="福建马尾三文鱼刺500g现切(FJSWY-350008)" class="u846_input"/>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u847" class="ax_default" data-left="24" data-top="231" data-width="440" data-height="30">

        <!-- Unnamed (矩形) -->
        <div id="u848" class="ax_default _文本段落">
          <div id="u848_div" class=""></div>
          <div id="u848_text" class="text ">
            <p><span>交易金额</span></p>
          </div>
        </div>

        <!-- Unnamed (文本框) -->
        <div id="u849" class="ax_default text_field">
          <div id="u849_div" class=""></div>
          <input id="u849_input" type="text" value="1,688.00" class="u849_input"/>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u850" class="ax_default _文本段落">
        <div id="u850_div" class=""></div>
        <div id="u850_text" class="text ">
          <p><span>交易订单信息（简）</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u851" class="ax_default" data-left="24" data-top="151" data-width="440" data-height="30">

        <!-- Unnamed (矩形) -->
        <div id="u852" class="ax_default _文本段落">
          <div id="u852_div" class=""></div>
          <div id="u852_text" class="text ">
            <p><span>订单单号</span></p>
          </div>
        </div>

        <!-- Unnamed (文本框) -->
        <div id="u853" class="ax_default text_field">
          <div id="u853_div" class=""></div>
          <input id="u853_input" type="text" value="4200002674202505182886982550" class="u853_input"/>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u854" class="ax_default" data-left="24" data-top="271" data-width="440" data-height="30">

        <!-- Unnamed (矩形) -->
        <div id="u855" class="ax_default _文本段落">
          <div id="u855_div" class=""></div>
          <div id="u855_text" class="text ">
            <p><span>商家名称</span></p>
          </div>
        </div>

        <!-- Unnamed (文本框) -->
        <div id="u856" class="ax_default text_field">
          <div id="u856_div" class=""></div>
          <input id="u856_input" type="text" value="海创未来(福建)科技有限公司" class="u856_input"/>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u857" class="ax_default" data-left="24" data-top="311" data-width="440" data-height="30">

        <!-- Unnamed (矩形) -->
        <div id="u858" class="ax_default _文本段落">
          <div id="u858_div" class=""></div>
          <div id="u858_text" class="text ">
            <p><span>买家名称</span></p>
          </div>
        </div>

        <!-- Unnamed (文本框) -->
        <div id="u859" class="ax_default text_field">
          <div id="u859_div" class=""></div>
          <input id="u859_input" type="text" value="钱多多" class="u859_input"/>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u860" class="ax_default" data-left="24" data-top="351" data-width="440" data-height="30">

        <!-- Unnamed (矩形) -->
        <div id="u861" class="ax_default _文本段落">
          <div id="u861_div" class=""></div>
          <div id="u861_text" class="text ">
            <p><span>交易附言</span></p>
          </div>
        </div>

        <!-- Unnamed (文本框) -->
        <div id="u862" class="ax_default text_field">
          <div id="u862_div" class=""></div>
          <input id="u862_input" type="text" value="" class="u862_input"/>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u863" class="ax_default" data-left="38" data-top="423" data-width="426" data-height="22">

        <!-- Unnamed (矩形) -->
        <div id="u864" class="ax_default _文本段落">
          <div id="u864_div" class=""></div>
          <div id="u864_text" class="text ">
            <p><span>付款单号</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u865" class="ax_default _文本段落">
          <div id="u865_div" class=""></div>
          <div id="u865_text" class="text ">
            <p><span>8869825504200002674202505182</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u866" class="ax_default" data-left="38" data-top="453" data-width="426" data-height="22">

        <!-- Unnamed (矩形) -->
        <div id="u867" class="ax_default _文本段落">
          <div id="u867_div" class=""></div>
          <div id="u867_text" class="text ">
            <p><span>商家编码</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u868" class="ax_default _文本段落">
          <div id="u868_div" class=""></div>
          <div id="u868_text" class="text ">
            <p><span>130233549211125051820080409610</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u869" class="ax_default" data-left="559" data-top="415" data-width="426" data-height="18">

        <!-- Unnamed (矩形) -->
        <div id="u870" class="ax_default _文本段落">
          <div id="u870_div" class=""></div>
          <div id="u870_text" class="text ">
            <p><span>交易通道</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u871" class="ax_default _文本段落">
          <div id="u871_div" class=""></div>
          <div id="u871_text" class="text ">
            <p><span>数优联/数市联/数蛋联/数药联/数牛联/数菜联</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u872" class="ax_default" data-left="38" data-top="393" data-width="426" data-height="22">

        <!-- Unnamed (矩形) -->
        <div id="u873" class="ax_default _文本段落">
          <div id="u873_div" class=""></div>
          <div id="u873_text" class="text ">
            <p><span>交易通道</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u874" class="ax_default _文本段落">
          <div id="u874_div" class=""></div>
          <div id="u874_text" class="text ">
            <p><span>数优联</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u875" class="ax_default" data-left="38" data-top="483" data-width="426" data-height="22">

        <!-- Unnamed (矩形) -->
        <div id="u876" class="ax_default _文本段落">
          <div id="u876_div" class=""></div>
          <div id="u876_text" class="text ">
            <p><span>付款时间</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u877" class="ax_default _文本段落">
          <div id="u877_div" class=""></div>
          <div id="u877_text" class="text ">
            <p><span>2025年5月18日 14:21:31</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u878" class="ax_default _文本段落">
        <div id="u878_div" class=""></div>
        <div id="u878_text" class="text ">
          <p><span>说明：商家、买家前端选择后；所对应的数字人民子钱包获取出来，后台传参。</span></p><p><span>以上灰色信息由前端应用自动生成。</span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u879" class="ax_default icon">
        <img id="u879_img" class="img " src="images/子钱包交易付款_f511_/u879.svg"/>
        <div id="u879_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u880" class="ax_default icon">
        <img id="u880_img" class="img " src="images/子钱包交易付款_f511_/u879.svg"/>
        <div id="u880_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u881" class="ax_default _文本段落">
        <div id="u881_div" class=""></div>
        <div id="u881_text" class="text ">
          <p><span>说明：以上订单传参的基本内容，主要用于支付单的信息查询。</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u882" class="ax_default primary_button">
        <div id="u882_div" class=""></div>
        <div id="u882_text" class="text ">
          <p><span>支付未完成，继续支付</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u883" class="ax_default" data-left="559" data-top="448" data-width="426" data-height="18">

        <!-- Unnamed (矩形) -->
        <div id="u884" class="ax_default _文本段落">
          <div id="u884_div" class=""></div>
          <div id="u884_text" class="text ">
            <p><span>付款单号</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u885" class="ax_default _文本段落">
          <div id="u885_div" class=""></div>
          <div id="u885_text" class="text ">
            <p><span>8869825504200002674202505182</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u886" class="ax_default primary_button">
        <div id="u886_div" class=""></div>
        <div id="u886_text" class="text ">
          <p><span>业务取消，关闭订单</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
