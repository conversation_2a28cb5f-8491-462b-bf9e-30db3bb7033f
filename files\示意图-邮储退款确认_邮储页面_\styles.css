﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:999px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u1102_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:900px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:50px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1102 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:900px;
  display:flex;
  opacity:0.49;
}
#u1102 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1102_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1103_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1103 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:20px;
  width:51px;
  height:40px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1103 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1103_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1104_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:18px;
}
#u1104 {
  border-width:0px;
  position:absolute;
  left:425px;
  top:19px;
  width:23px;
  height:18px;
  display:flex;
}
#u1104 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1104_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1105_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:16px;
}
#u1105 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:21px;
  width:26px;
  height:16px;
  display:flex;
}
#u1105 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1105_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1106_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u1106 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:50px;
  width:300px;
  height:25px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u1106 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1106_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1107 {
  position:fixed;
  left:50%;
  margin-left:-75px;
  top:50%;
  margin-top:-25px;
  visibility:hidden;
}
#u1107_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1107_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1108_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u1108 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u1108 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u1108_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1107_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u1107_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1109_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u1109 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u1109 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u1109_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1110_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u1110 {
  border-width:0px;
  position:absolute;
  left:60px;
  top:16px;
  width:80px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u1110 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1110_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1111_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u1111 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:10px;
  width:30px;
  height:30px;
  display:flex;
}
#u1111 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1111_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1112_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u1112 {
  border-width:0px;
  position:absolute;
  left:463px;
  top:50px;
  width:25px;
  height:25px;
  display:flex;
}
#u1112 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1112_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1113_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:25px;
}
#u1113 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:50px;
  width:15px;
  height:25px;
  display:flex;
}
#u1113 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1113_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1114_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:228px;
  height:11px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  text-align:center;
}
#u1114 {
  border-width:0px;
  position:absolute;
  left:136px;
  top:71px;
  width:228px;
  height:11px;
  display:flex;
  font-size:10px;
  text-align:center;
}
#u1114 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1114_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1115_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:439px;
  height:50px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u1115 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:786px;
  width:439px;
  height:50px;
  display:flex;
  font-size:18px;
}
#u1115 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1115_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1116_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:460px;
  height:152px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u1116 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:107px;
  width:460px;
  height:152px;
  display:flex;
  font-size:16px;
}
#u1116 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1116_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1117_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:28px;
}
#u1117 {
  border-width:0px;
  position:absolute;
  left:175px;
  top:133px;
  width:114px;
  height:32px;
  display:flex;
  font-size:28px;
}
#u1117 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1117_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1118_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u1118 {
  border-width:0px;
  position:absolute;
  left:617px;
  top:26px;
  width:90px;
  height:25px;
  display:flex;
  font-size:16px;
}
#u1118 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1118_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1119 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1120_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:284px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u1120 {
  border-width:0px;
  position:absolute;
  left:715px;
  top:26px;
  width:284px;
  height:25px;
  display:flex;
  font-size:16px;
}
#u1120 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1120_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1121_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u1121 {
  border-width:0px;
  position:absolute;
  left:617px;
  top:51px;
  width:90px;
  height:25px;
  display:flex;
  font-size:16px;
}
#u1121 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1121_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1122 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1123_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:284px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u1123 {
  border-width:0px;
  position:absolute;
  left:715px;
  top:51px;
  width:284px;
  height:25px;
  display:flex;
  font-size:16px;
}
#u1123 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1123_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1124 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1125_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:460px;
  height:128px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u1125 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:313px;
  width:460px;
  height:128px;
  display:flex;
  font-size:16px;
}
#u1125 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1125_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1126_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:225px;
  height:33px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  color:#000000;
}
#u1126 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:325px;
  width:225px;
  height:33px;
  display:flex;
  font-size:20px;
  color:#000000;
}
#u1126 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1126_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1127 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1128_input {
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1128_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1128_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1128 {
  border-width:0px;
  position:absolute;
  left:92px;
  top:370px;
  width:35px;
  height:35px;
  display:flex;
}
#u1128 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1128_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1128.disabled {
}
#u1129_input {
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1129_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1129_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1129 {
  border-width:0px;
  position:absolute;
  left:154px;
  top:370px;
  width:35px;
  height:35px;
  display:flex;
}
#u1129 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1129_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1129.disabled {
}
#u1130_input {
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1130_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1130_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1130 {
  border-width:0px;
  position:absolute;
  left:215px;
  top:370px;
  width:35px;
  height:35px;
  display:flex;
}
#u1130 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1130_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1130.disabled {
}
#u1131_input {
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1131_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1131_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1131 {
  border-width:0px;
  position:absolute;
  left:277px;
  top:370px;
  width:35px;
  height:35px;
  display:flex;
}
#u1131 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1131_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1131.disabled {
}
#u1132_input {
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1132_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1132_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1132 {
  border-width:0px;
  position:absolute;
  left:338px;
  top:370px;
  width:35px;
  height:35px;
  display:flex;
}
#u1132 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1132_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1132.disabled {
}
#u1133_input {
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1133_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1133_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1133 {
  border-width:0px;
  position:absolute;
  left:400px;
  top:370px;
  width:35px;
  height:35px;
  display:flex;
}
#u1133 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1133_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1133.disabled {
}
#u1134_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:460px;
  height:132px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u1134 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:531px;
  width:460px;
  height:132px;
  display:flex;
  font-size:16px;
}
#u1134 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1134_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1135_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:137px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u1135 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:540px;
  width:137px;
  height:25px;
  display:flex;
  font-size:18px;
}
#u1135 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1135_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1136_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:447px;
  height:79px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1136 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:568px;
  width:447px;
  height:79px;
  display:flex;
}
#u1136 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1136_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1137 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1138_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#AAAAAA;
}
#u1138 {
  border-width:0px;
  position:absolute;
  left:43px;
  top:196px;
  width:90px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#AAAAAA;
}
#u1138 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1138_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1139_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:334px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u1139 {
  border-width:0px;
  position:absolute;
  left:135px;
  top:196px;
  width:334px;
  height:18px;
  display:flex;
  font-size:16px;
}
#u1139 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1139_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
