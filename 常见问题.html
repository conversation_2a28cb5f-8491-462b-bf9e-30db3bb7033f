﻿<!DOCTYPE html>
<html>
  <head>
    <title>常见问题</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/常见问题/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/常见问题/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (基础APP框架) -->

      <!-- Unnamed (矩形) -->
      <div id="u5006" class="ax_default box_1">
        <div id="u5006_div" class=""></div>
        <div id="u5006_text" class="text ">
          <p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u5007" class="ax_default" data-left="22" data-top="834" data-width="456" data-height="41">

        <!-- Unnamed (组合) -->
        <div id="u5008" class="ax_default" data-left="22" data-top="834" data-width="26" data-height="41">

          <!-- Unnamed (图片 ) -->
          <div id="u5009" class="ax_default _图片">
            <img id="u5009_img" class="img " src="images/平台首页/u2789.png"/>
            <div id="u5009_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u5010" class="ax_default _文本段落">
            <div id="u5010_div" class=""></div>
            <div id="u5010_text" class="text ">
              <p><span>首页</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u5011" class="ax_default" data-left="130" data-top="834" data-width="26" data-height="41">

          <!-- Unnamed (图片 ) -->
          <div id="u5012" class="ax_default _图片">
            <img id="u5012_img" class="img " src="images/平台首页/u2792.png"/>
            <div id="u5012_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u5013" class="ax_default _文本段落">
            <div id="u5013_div" class=""></div>
            <div id="u5013_text" class="text ">
              <p><span>融资</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u5014" class="ax_default" data-left="345" data-top="834" data-width="26" data-height="41">

          <!-- Unnamed (图片 ) -->
          <div id="u5015" class="ax_default _图片">
            <img id="u5015_img" class="img " src="images/平台首页/u2795.png"/>
            <div id="u5015_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u5016" class="ax_default _文本段落">
            <div id="u5016_div" class=""></div>
            <div id="u5016_text" class="text ">
              <p><span>消息</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u5017" class="ax_default" data-left="452" data-top="834" data-width="26" data-height="41">

          <!-- Unnamed (图片 ) -->
          <div id="u5018" class="ax_default _图片">
            <img id="u5018_img" class="img " src="images/平台首页/u2798.png"/>
            <div id="u5018_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u5019" class="ax_default _文本段落">
            <div id="u5019_div" class=""></div>
            <div id="u5019_text" class="text ">
              <p><span>我的</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u5020" class="ax_default" data-left="228" data-top="834" data-width="44" data-height="41">

          <!-- Unnamed (图片 ) -->
          <div id="u5021" class="ax_default _图片">
            <img id="u5021_img" class="img " src="images/平台首页/u2801.png"/>
            <div id="u5021_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u5022" class="ax_default _文本段落">
            <div id="u5022_div" class=""></div>
            <div id="u5022_text" class="text ">
              <p><span>发现</span></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5023" class="ax_default box_1">
        <div id="u5023_div" class=""></div>
        <div id="u5023_text" class="text ">
          <p><span>3</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5024" class="ax_default _二级标题">
        <div id="u5024_div" class=""></div>
        <div id="u5024_text" class="text ">
          <p><span>16:18</span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u5025" class="ax_default icon">
        <img id="u5025_img" class="img " src="images/海融宝签约_个人__f501_f502_/u3.svg"/>
        <div id="u5025_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u5026" class="ax_default icon">
        <img id="u5026_img" class="img " src="images/海融宝签约_个人__f501_f502_/u4.svg"/>
        <div id="u5026_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u5027" class="ax_default _图片">
        <img id="u5027_img" class="img " src="images/个人开结算账户（申请）/u2269.png"/>
        <div id="u5027_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5028" class="ax_default _文本段落">
        <div id="u5028_div" class=""></div>
        <div id="u5028_text" class="text ">
          <p><span>常见问题</span></p>
        </div>
      </div>

      <!-- 操作状态 (动态面板) -->
      <div id="u5029" class="ax_default ax_default_hidden" data-label="操作状态" style="display:none; visibility: hidden">
        <div id="u5029_state0" class="panel_state" data-label="操作成功" style="">
          <div id="u5029_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u5030" class="ax_default box_3">
              <div id="u5030_div" class=""></div>
              <div id="u5030_text" class="text ">
                <p><span>操作成功！</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u5029_state1" class="panel_state" data-label="操作失败" style="visibility: hidden;">
          <div id="u5029_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u5031" class="ax_default box_3">
              <div id="u5031_div" class=""></div>
              <div id="u5031_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u5032" class="ax_default _文本段落">
              <div id="u5032_div" class=""></div>
              <div id="u5032_text" class="text ">
                <p><span>操作失败！</span></p>
              </div>
            </div>

            <!-- Unnamed (图片 ) -->
            <div id="u5033" class="ax_default _图片_">
              <img id="u5033_img" class="img " src="images/海融宝签约_个人__f501_f502_/u10.png"/>
              <div id="u5033_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5034" class="ax_default _文本段落">
        <div id="u5034_div" class=""></div>
        <div id="u5034_text" class="text ">
          <p><span>hrypt.com</span></p>
        </div>
      </div>
      <div id="u5005" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (文本域) -->
      <div id="u5035" class="ax_default text_area">
        <div id="u5035_div" class=""></div>
        <textarea id="u5035_input" class="u5035_input">&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp; 常见问题
一、业务问题
&nbsp; &nbsp; &nbsp;&nbsp; 联系中海创科技(福建)集国有限公司

二、技术问题
&nbsp; &nbsp; &nbsp;&nbsp; 联系华阁供应链

三、问题反馈
&nbsp; &nbsp; &nbsp;&nbsp; 联系中海创科技(福建)集国有限公司


致电400 0123 756</textarea>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5036" class="ax_default _文本段落">
        <div id="u5036_div" class=""></div>
        <div id="u5036_text" class="text ">
          <p><span>在线客服&gt;</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
