﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:510px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u2381_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:1280px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:50px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2381 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:1280px;
  display:flex;
  opacity:0.49;
}
#u2381 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2381_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2382_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u2382 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:20px;
  width:51px;
  height:18px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u2382 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2382_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2383_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:18px;
}
#u2383 {
  border-width:0px;
  position:absolute;
  left:425px;
  top:19px;
  width:23px;
  height:18px;
  display:flex;
}
#u2383 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2383_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2384_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:16px;
}
#u2384 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:21px;
  width:26px;
  height:16px;
  display:flex;
}
#u2384 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2384_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2385_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:25px;
}
#u2385 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:50px;
  width:24px;
  height:25px;
  display:flex;
}
#u2385 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2385_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2386_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:22px;
}
#u2386 {
  border-width:0px;
  position:absolute;
  left:467px;
  top:56px;
  width:21px;
  height:22px;
  display:flex;
}
#u2386 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2386_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2387_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:252px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u2387 {
  border-width:0px;
  position:absolute;
  left:124px;
  top:49px;
  width:252px;
  height:25px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u2387 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2387_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2388 {
  position:fixed;
  left:50%;
  margin-left:-75px;
  top:50%;
  margin-top:-25px;
  visibility:hidden;
}
#u2388_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2388_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u2389_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u2389 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u2389 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2389_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2388_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u2388_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u2390_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u2390 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u2390 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2390_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2391_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u2391 {
  border-width:0px;
  position:absolute;
  left:60px;
  top:16px;
  width:80px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u2391 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2391_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2392_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u2392 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:10px;
  width:30px;
  height:30px;
  display:flex;
}
#u2392 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2392_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2393_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:228px;
  height:11px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  text-align:center;
}
#u2393 {
  border-width:0px;
  position:absolute;
  left:136px;
  top:71px;
  width:228px;
  height:11px;
  display:flex;
  font-size:10px;
  text-align:center;
}
#u2393 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2393_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2394 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2395_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:480px;
  height:756px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2395 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:95px;
  width:480px;
  height:756px;
  display:flex;
}
#u2395 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2395_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2396_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:218px;
  height:23px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
}
#u2396 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:103px;
  width:218px;
  height:23px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
}
#u2396 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2396_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2398_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2398 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:244px;
  width:450px;
  height:54px;
  display:flex;
}
#u2398 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2398_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2399_input {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2399_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2399_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u2399 {
  border-width:0px;
  position:absolute;
  left:144px;
  top:256px;
  width:330px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u2399 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2399_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u2399.disabled {
}
#u2400_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:266px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u2400 {
  border-width:0px;
  position:absolute;
  left:149px;
  top:256px;
  width:266px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u2400 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2400_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2401_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u2401 {
  border-width:0px;
  position:absolute;
  left:420px;
  top:256px;
  width:54px;
  height:30px;
  display:flex;
  font-size:16px;
}
#u2401 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2401_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2402_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2402 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:256px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u2402 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2402_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2404_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2404 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:356px;
  width:450px;
  height:54px;
  display:flex;
}
#u2404 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2404_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2405_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2405 {
  border-width:0px;
  position:absolute;
  left:56px;
  top:368px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u2405 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2405_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2406_input {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2406_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2406_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u2406 {
  border-width:0px;
  position:absolute;
  left:146px;
  top:368px;
  width:330px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u2406 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2406_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u2406.disabled {
}
#u2407_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:294px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u2407 {
  border-width:0px;
  position:absolute;
  left:157px;
  top:368px;
  width:294px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u2407 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2407_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2408_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:26px;
}
#u2408 {
  border-width:0px;
  position:absolute;
  left:449px;
  top:370px;
  width:26px;
  height:26px;
  display:flex;
  font-size:16px;
  color:#555555;
}
#u2408 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2408_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2409 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2410_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:42px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#555555;
  text-align:left;
}
#u2410 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:430px;
  width:112px;
  height:42px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#555555;
  text-align:left;
}
#u2410 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2410_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2411_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:336px;
  height:84px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#555555;
  text-align:left;
}
#u2411 {
  border-width:0px;
  position:absolute;
  left:148px;
  top:410px;
  width:336px;
  height:84px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#555555;
  text-align:left;
}
#u2411 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2411_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2412_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:260px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#555555;
}
#u2412 {
  border-width:0px;
  position:absolute;
  left:155px;
  top:417px;
  width:260px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#555555;
}
#u2412 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2412_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2413_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#555555;
  text-align:right;
}
#u2413 {
  border-width:0px;
  position:absolute;
  left:362px;
  top:470px;
  width:120px;
  height:22px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#555555;
  text-align:right;
}
#u2413 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2413_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2415 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2416_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:304px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2416 {
  border-width:0px;
  position:absolute;
  left:54px;
  top:496px;
  width:304px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u2416 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2416_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2417 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2418_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:205px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2418 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:527px;
  width:205px;
  height:40px;
  display:flex;
}
#u2418 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2418_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2419_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:205px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2419 {
  border-width:0px;
  position:absolute;
  left:279px;
  top:527px;
  width:205px;
  height:40px;
  display:flex;
}
#u2419 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2419_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2420_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u2420 {
  border-width:0px;
  position:absolute;
  left:194px;
  top:535px;
  width:25px;
  height:25px;
  display:flex;
}
#u2420 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2420_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2421_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u2421 {
  border-width:0px;
  position:absolute;
  left:443px;
  top:535px;
  width:25px;
  height:25px;
  display:flex;
}
#u2421 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2421_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2422_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  color:#7F7F7F;
}
#u2422 {
  border-width:0px;
  position:absolute;
  left:54px;
  top:532px;
  width:108px;
  height:30px;
  display:flex;
  font-size:18px;
  color:#7F7F7F;
}
#u2422 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2422_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2423_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  color:#7F7F7F;
}
#u2423 {
  border-width:0px;
  position:absolute;
  left:304px;
  top:532px;
  width:108px;
  height:30px;
  display:flex;
  font-size:18px;
  color:#7F7F7F;
}
#u2423 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2423_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2424_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:18px;
  height:7px;
}
#u2424 {
  border-width:0px;
  position:absolute;
  left:250px;
  top:541px;
  width:14px;
  height:3px;
  display:flex;
}
#u2424 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2424_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2425 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2426_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:448px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2426 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:132px;
  width:448px;
  height:54px;
  display:flex;
}
#u2426 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2426_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2427_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:326px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u2427 {
  border-width:0px;
  position:absolute;
  left:142px;
  top:144px;
  width:326px;
  height:30px;
  display:flex;
  font-size:16px;
}
#u2427 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2427_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2428_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2428 {
  border-width:0px;
  position:absolute;
  left:53px;
  top:144px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u2428 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2428_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2429 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2430_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:448px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2430 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:188px;
  width:448px;
  height:54px;
  display:flex;
}
#u2430 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2430_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2431_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:119px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2431 {
  border-width:0px;
  position:absolute;
  left:53px;
  top:200px;
  width:119px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u2431 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2431_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2432_input {
  position:absolute;
  left:0px;
  top:0px;
  width:296px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#555555;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2432_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:296px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#555555;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2432_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:296px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#555555;
}
#u2432 {
  border-width:0px;
  position:absolute;
  left:172px;
  top:200px;
  width:296px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#555555;
}
#u2432 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2432_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:296px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#555555;
}
#u2432.disabled {
}
#u2433 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2434_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:448px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2434 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:633px;
  width:448px;
  height:54px;
  display:flex;
}
#u2434 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2434_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2435_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2435 {
  border-width:0px;
  position:absolute;
  left:53px;
  top:645px;
  width:145px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u2435 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2435_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2436_input {
  position:absolute;
  left:0px;
  top:0px;
  width:336px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#555555;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2436_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:336px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#555555;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2436_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:336px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#555555;
}
#u2436 {
  border-width:0px;
  position:absolute;
  left:138px;
  top:645px;
  width:336px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#555555;
}
#u2436 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2436_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:336px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#555555;
}
#u2436.disabled {
}
#u2437_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:189px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#D9001B;
}
#u2437 {
  border-width:0px;
  position:absolute;
  left:293px;
  top:669px;
  width:189px;
  height:18px;
  display:flex;
  font-size:12px;
  color:#D9001B;
}
#u2437 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2437_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2439_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2439 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:300px;
  width:450px;
  height:54px;
  display:flex;
}
#u2439 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2439_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2440_input {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2440_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2440_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u2440 {
  border-width:0px;
  position:absolute;
  left:144px;
  top:312px;
  width:330px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u2440 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2440_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u2440.disabled {
}
#u2441_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:266px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u2441 {
  border-width:0px;
  position:absolute;
  left:149px;
  top:312px;
  width:266px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u2441 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2441_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2442_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u2442 {
  border-width:0px;
  position:absolute;
  left:420px;
  top:312px;
  width:54px;
  height:30px;
  display:flex;
  font-size:16px;
}
#u2442 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2442_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2443_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2443 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:312px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u2443 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2443_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2445_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:448px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2445 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:579px;
  width:448px;
  height:54px;
  display:flex;
}
#u2445 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2445_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2446_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2446 {
  border-width:0px;
  position:absolute;
  left:54px;
  top:591px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u2446 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2446_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2447_input {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2447_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2447_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u2447 {
  border-width:0px;
  position:absolute;
  left:144px;
  top:591px;
  width:330px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u2447 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2447_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u2447.disabled {
}
#u2448_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:30px;
}
#u2448 {
  border-width:0px;
  position:absolute;
  left:452px;
  top:591px;
  width:18px;
  height:30px;
  display:flex;
}
#u2448 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2448_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2449_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:294px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u2449 {
  border-width:0px;
  position:absolute;
  left:155px;
  top:591px;
  width:294px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u2449 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2449_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2450_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:189px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#D9001B;
}
#u2450 {
  border-width:0px;
  position:absolute;
  left:306px;
  top:565px;
  width:189px;
  height:18px;
  display:flex;
  font-size:12px;
  color:#D9001B;
}
#u2450 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2450_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2451 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2452_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:225px;
  height:144px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#AEAEAE;
  text-align:left;
}
#u2452 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:694px;
  width:225px;
  height:144px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:24px;
  color:#AEAEAE;
  text-align:left;
}
#u2452 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2452_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2454 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2455_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:45px;
  height:45px;
}
#u2455 {
  border-width:0px;
  position:absolute;
  left:51px;
  top:706px;
  width:45px;
  height:45px;
  display:flex;
  color:#7F7F7F;
}
#u2455 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2455_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2456_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:151px;
  height:42px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#7F7F7F;
  text-align:left;
}
#u2456 {
  border-width:0px;
  position:absolute;
  left:105px;
  top:708px;
  width:151px;
  height:42px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#7F7F7F;
  text-align:left;
}
#u2456 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2456_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2457 {
  border-width:0px;
  position:absolute;
  left:36px;
  top:706px;
  width:210px;
  height:120px;
  visibility:hidden;
}
#u2457_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:210px;
  height:120px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2457_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u2458_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:120px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2458 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:120px;
  display:flex;
  font-size:18px;
}
#u2458 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2458_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2459_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u2459 {
  border-width:0px;
  position:absolute;
  left:179px;
  top:2px;
  width:23px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u2459 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2459_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2460_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:2px;
}
#u2460 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:200px;
  height:1px;
  display:flex;
  font-size:18px;
}
#u2460 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2460_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2461_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:165px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  text-align:center;
}
#u2461 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:12px;
  width:165px;
  height:21px;
  display:flex;
  font-size:18px;
  text-align:center;
}
#u2461 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2461_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2462_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:2px;
}
#u2462 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:80px;
  width:200px;
  height:1px;
  display:flex;
  font-size:18px;
}
#u2462 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2462_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2463_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:173px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  text-align:center;
}
#u2463 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:50px;
  width:173px;
  height:21px;
  display:flex;
  font-size:18px;
  text-align:center;
}
#u2463 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2463_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2464_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2464 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:85px;
  width:120px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u2464 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2464_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2465_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:480px;
  height:425px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2465 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:851px;
  width:480px;
  height:425px;
  display:flex;
}
#u2465 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2465_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2467_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:219px;
  height:141px;
}
#u2467 {
  border-width:0px;
  position:absolute;
  left:259px;
  top:908px;
  width:219px;
  height:141px;
  display:flex;
}
#u2467 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2467_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2468_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  height:141px;
}
#u2468 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:908px;
  width:221px;
  height:141px;
  display:flex;
}
#u2468 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2468_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2469 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2470_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:59px;
  height:60px;
}
#u2470 {
  border-width:0px;
  position:absolute;
  left:117px;
  top:940px;
  width:59px;
  height:60px;
  display:flex;
}
#u2470 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2470_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2471_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u2471 {
  border-width:0px;
  position:absolute;
  left:132px;
  top:955px;
  width:30px;
  height:30px;
  display:flex;
}
#u2471 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2471_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2472_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:142px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  text-align:center;
}
#u2472 {
  border-width:0px;
  position:absolute;
  left:77px;
  top:1016px;
  width:142px;
  height:21px;
  display:flex;
  font-size:18px;
  text-align:center;
}
#u2472 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2472_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2473 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2474_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:59px;
  height:60px;
}
#u2474 {
  border-width:0px;
  position:absolute;
  left:339px;
  top:940px;
  width:59px;
  height:60px;
  display:flex;
}
#u2474 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2474_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2475_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u2475 {
  border-width:0px;
  position:absolute;
  left:354px;
  top:955px;
  width:30px;
  height:30px;
  display:flex;
}
#u2475 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2475_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2476_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:142px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  text-align:center;
}
#u2476 {
  border-width:0px;
  position:absolute;
  left:297px;
  top:1016px;
  width:142px;
  height:21px;
  display:flex;
  font-size:18px;
  text-align:center;
}
#u2476 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2476_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2477 {
  border-width:0px;
  position:absolute;
  left:162px;
  top:919px;
  width:218px;
  height:130px;
  visibility:hidden;
}
#u2477_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:218px;
  height:130px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2477_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u2478_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:130px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2478 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:220px;
  height:130px;
  display:flex;
}
#u2478 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2478_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2479_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:28px;
}
#u2479 {
  border-width:0px;
  position:absolute;
  left:197px;
  top:0px;
  width:23px;
  height:32px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:28px;
}
#u2479 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2479_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2480_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  height:2px;
}
#u2480 {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:36px;
  width:220px;
  height:1px;
  display:flex;
}
#u2480 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2480_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2481_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:165px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  text-align:center;
}
#u2481 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:11px;
  width:165px;
  height:21px;
  display:flex;
  font-size:18px;
  text-align:center;
}
#u2481 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2481_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2482_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:221px;
  height:2px;
}
#u2482 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:70px;
  width:220px;
  height:1px;
  display:flex;
}
#u2482 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2482_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2483_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  text-align:center;
}
#u2483 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:45px;
  width:150px;
  height:21px;
  display:flex;
  font-size:18px;
  text-align:center;
}
#u2483 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2483_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2484_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:30px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2484 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:81px;
  width:140px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u2484 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2484_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2485_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:201px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2485 {
  border-width:0px;
  position:absolute;
  left:48px;
  top:886px;
  width:201px;
  height:22px;
  display:flex;
  font-size:18px;
}
#u2485 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2485_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2486_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:218px;
  height:23px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
}
#u2486 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:858px;
  width:218px;
  height:23px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
}
#u2486 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2486_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2488_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2488 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:1024px;
  width:450px;
  height:54px;
  display:flex;
}
#u2488 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2488_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2489_input {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2489_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2489_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u2489 {
  border-width:0px;
  position:absolute;
  left:138px;
  top:1036px;
  width:330px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u2489 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2489_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u2489.disabled {
}
#u2490_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:266px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u2490 {
  border-width:0px;
  position:absolute;
  left:143px;
  top:1036px;
  width:266px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u2490 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2490_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2491_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u2491 {
  border-width:0px;
  position:absolute;
  left:414px;
  top:1036px;
  width:54px;
  height:30px;
  display:flex;
  font-size:16px;
}
#u2491 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2491_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2492_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2492 {
  border-width:0px;
  position:absolute;
  left:43px;
  top:1036px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u2492 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2492_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2494_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2494 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:1080px;
  width:450px;
  height:54px;
  display:flex;
}
#u2494 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2494_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2495_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2495 {
  border-width:0px;
  position:absolute;
  left:48px;
  top:1092px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u2495 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2495_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2496_input {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2496_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2496_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u2496 {
  border-width:0px;
  position:absolute;
  left:138px;
  top:1092px;
  width:330px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u2496 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2496_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u2496.disabled {
}
#u2497_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:294px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u2497 {
  border-width:0px;
  position:absolute;
  left:149px;
  top:1092px;
  width:294px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u2497 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2497_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2498_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:26px;
}
#u2498 {
  border-width:0px;
  position:absolute;
  left:441px;
  top:1094px;
  width:26px;
  height:26px;
  display:flex;
  font-size:16px;
  color:#555555;
}
#u2498 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2498_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2500 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2501_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:304px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2501 {
  border-width:0px;
  position:absolute;
  left:50px;
  top:1136px;
  width:304px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u2501 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2501_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2502 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2503_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:205px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2503 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:1167px;
  width:205px;
  height:40px;
  display:flex;
}
#u2503 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2503_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2504_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:205px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2504 {
  border-width:0px;
  position:absolute;
  left:275px;
  top:1167px;
  width:205px;
  height:40px;
  display:flex;
}
#u2504 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2504_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2505_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u2505 {
  border-width:0px;
  position:absolute;
  left:190px;
  top:1175px;
  width:25px;
  height:25px;
  display:flex;
}
#u2505 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2505_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2506_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u2506 {
  border-width:0px;
  position:absolute;
  left:439px;
  top:1175px;
  width:25px;
  height:25px;
  display:flex;
}
#u2506 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2506_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2507_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  color:#7F7F7F;
}
#u2507 {
  border-width:0px;
  position:absolute;
  left:50px;
  top:1172px;
  width:108px;
  height:30px;
  display:flex;
  font-size:18px;
  color:#7F7F7F;
}
#u2507 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2507_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2508_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  color:#7F7F7F;
}
#u2508 {
  border-width:0px;
  position:absolute;
  left:300px;
  top:1172px;
  width:108px;
  height:30px;
  display:flex;
  font-size:18px;
  color:#7F7F7F;
}
#u2508 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2508_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2509_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:18px;
  height:7px;
}
#u2509 {
  border-width:0px;
  position:absolute;
  left:246px;
  top:1181px;
  width:14px;
  height:3px;
  display:flex;
}
#u2509 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2509_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2510_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:480px;
  height:270px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2510 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:1629px;
  width:480px;
  height:270px;
  display:flex;
}
#u2510 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2510_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2511_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:218px;
  height:23px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
}
#u2511 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:1637px;
  width:218px;
  height:23px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
}
#u2511 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2511_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2513_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2513 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:1718px;
  width:450px;
  height:54px;
  display:flex;
}
#u2513 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2513_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2514_input {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2514_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2514_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u2514 {
  border-width:0px;
  position:absolute;
  left:144px;
  top:1730px;
  width:330px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u2514 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2514_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u2514.disabled {
}
#u2515_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:266px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u2515 {
  border-width:0px;
  position:absolute;
  left:149px;
  top:1730px;
  width:266px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u2515 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2515_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2516_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u2516 {
  border-width:0px;
  position:absolute;
  left:420px;
  top:1730px;
  width:54px;
  height:30px;
  display:flex;
  font-size:16px;
}
#u2516 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2516_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2517_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2517 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:1730px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u2517 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2517_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2519_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2519 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:1774px;
  width:450px;
  height:54px;
  display:flex;
}
#u2519 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2519_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2520_input {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2520_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2520_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u2520 {
  border-width:0px;
  position:absolute;
  left:144px;
  top:1786px;
  width:330px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u2520 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2520_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u2520.disabled {
}
#u2521_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:266px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u2521 {
  border-width:0px;
  position:absolute;
  left:149px;
  top:1786px;
  width:266px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u2521 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2521_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2522_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u2522 {
  border-width:0px;
  position:absolute;
  left:420px;
  top:1786px;
  width:54px;
  height:30px;
  display:flex;
  font-size:16px;
}
#u2522 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2522_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2523_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2523 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:1786px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u2523 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2523_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2525_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:448px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2525 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:1662px;
  width:448px;
  height:54px;
  display:flex;
}
#u2525 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2525_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2526_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2526 {
  border-width:0px;
  position:absolute;
  left:54px;
  top:1674px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u2526 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2526_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2527_input {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2527_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2527_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u2527 {
  border-width:0px;
  position:absolute;
  left:144px;
  top:1674px;
  width:330px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u2527 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2527_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u2527.disabled {
}
#u2528_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:30px;
}
#u2528 {
  border-width:0px;
  position:absolute;
  left:452px;
  top:1674px;
  width:18px;
  height:30px;
  display:flex;
}
#u2528 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2528_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2529_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:294px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u2529 {
  border-width:0px;
  position:absolute;
  left:155px;
  top:1674px;
  width:294px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u2529 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2529_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2530_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:189px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#D9001B;
}
#u2530 {
  border-width:0px;
  position:absolute;
  left:293px;
  top:1800px;
  width:189px;
  height:18px;
  display:flex;
  font-size:12px;
  color:#D9001B;
}
#u2530 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2530_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2532_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2532 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:1830px;
  width:450px;
  height:54px;
  display:flex;
}
#u2532 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2532_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2533_input {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2533_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2533_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u2533 {
  border-width:0px;
  position:absolute;
  left:144px;
  top:1842px;
  width:330px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u2533 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2533_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u2533.disabled {
}
#u2534_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:266px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u2534 {
  border-width:0px;
  position:absolute;
  left:149px;
  top:1842px;
  width:266px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u2534 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2534_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2535_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u2535 {
  border-width:0px;
  position:absolute;
  left:420px;
  top:1842px;
  width:54px;
  height:30px;
  display:flex;
  font-size:16px;
}
#u2535 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2535_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2536_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2536 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:1842px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u2536 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2536_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2537_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:439px;
  height:50px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2537 {
  border-width:0px;
  position:absolute;
  left:43px;
  top:1917px;
  width:439px;
  height:50px;
  display:flex;
  font-size:18px;
}
#u2537 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2537_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2539_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:450px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2539 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:1219px;
  width:450px;
  height:54px;
  display:flex;
}
#u2539 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2539_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2540_input {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2540_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2540_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u2540 {
  border-width:0px;
  position:absolute;
  left:140px;
  top:1231px;
  width:330px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u2540 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2540_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u2540.disabled {
}
#u2541_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:266px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D7D7D7;
}
#u2541 {
  border-width:0px;
  position:absolute;
  left:145px;
  top:1231px;
  width:266px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#D7D7D7;
}
#u2541 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2541_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2542_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u2542 {
  border-width:0px;
  position:absolute;
  left:416px;
  top:1231px;
  width:54px;
  height:30px;
  display:flex;
  font-size:16px;
}
#u2542 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2542_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2543_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2543 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:1231px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u2543 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2543_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2544_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:480px;
  height:353px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2544 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:1276px;
  width:480px;
  height:353px;
  display:flex;
}
#u2544 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2544_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2545_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:218px;
  height:23px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
}
#u2545 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:1290px;
  width:218px;
  height:23px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
}
#u2545 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2545_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2546 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2547_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:448px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2547 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:1320px;
  width:448px;
  height:54px;
  display:flex;
}
#u2547 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2547_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2548_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2548 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:1332px;
  width:145px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u2548 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2548_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2549_input {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2549_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2549_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u2549 {
  border-width:0px;
  position:absolute;
  left:168px;
  top:1332px;
  width:300px;
  height:30px;
  display:flex;
  font-size:16px;
}
#u2549 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2549_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u2549.disabled {
}
.u2549_input_option {
  font-size:16px;
}
#u2550 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2551_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:448px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2551 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:1374px;
  width:448px;
  height:54px;
  display:flex;
}
#u2551 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2551_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2552_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2552 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:1386px;
  width:145px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u2552 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2552_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2553_input {
  position:absolute;
  left:0px;
  top:0px;
  width:302px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2553_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:302px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2553_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:302px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#AAAAAA;
}
#u2553 {
  border-width:0px;
  position:absolute;
  left:168px;
  top:1386px;
  width:302px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#AAAAAA;
}
#u2553 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2553_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:302px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#AAAAAA;
}
#u2553.disabled {
}
#u2554 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2555_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:448px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2555 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:1482px;
  width:448px;
  height:54px;
  display:flex;
}
#u2555 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2555_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2556_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2556 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:1494px;
  width:145px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u2556 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2556_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2557_input {
  position:absolute;
  left:0px;
  top:0px;
  width:302px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2557_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:302px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2557_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:302px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#AAAAAA;
}
#u2557 {
  border-width:0px;
  position:absolute;
  left:168px;
  top:1494px;
  width:302px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#AAAAAA;
}
#u2557 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2557_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:302px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#AAAAAA;
}
#u2557.disabled {
}
#u2558 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2559_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:448px;
  height:54px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2559 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:1428px;
  width:448px;
  height:54px;
  display:flex;
}
#u2559 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2559_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2560_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:145px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2560 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:1440px;
  width:145px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u2560 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2560_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2561_input {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2561_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u2561_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u2561 {
  border-width:0px;
  position:absolute;
  left:168px;
  top:1440px;
  width:300px;
  height:30px;
  display:flex;
  font-size:16px;
}
#u2561 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2561_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u2561.disabled {
}
.u2561_input_option {
  font-size:16px;
}
#u2563 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2564_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:304px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u2564 {
  border-width:0px;
  position:absolute;
  left:50px;
  top:1538px;
  width:304px;
  height:30px;
  display:flex;
  font-size:18px;
}
#u2564 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2564_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2565 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2566_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:205px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2566 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:1569px;
  width:205px;
  height:40px;
  display:flex;
}
#u2566 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2566_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2567_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:205px;
  height:40px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2567 {
  border-width:0px;
  position:absolute;
  left:275px;
  top:1569px;
  width:205px;
  height:40px;
  display:flex;
}
#u2567 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2567_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2568_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u2568 {
  border-width:0px;
  position:absolute;
  left:190px;
  top:1577px;
  width:25px;
  height:25px;
  display:flex;
}
#u2568 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2568_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2569_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u2569 {
  border-width:0px;
  position:absolute;
  left:439px;
  top:1577px;
  width:25px;
  height:25px;
  display:flex;
}
#u2569 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2569_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2570_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  color:#7F7F7F;
}
#u2570 {
  border-width:0px;
  position:absolute;
  left:50px;
  top:1574px;
  width:108px;
  height:30px;
  display:flex;
  font-size:18px;
  color:#7F7F7F;
}
#u2570 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2570_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2571_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  color:#7F7F7F;
}
#u2571 {
  border-width:0px;
  position:absolute;
  left:300px;
  top:1574px;
  width:108px;
  height:30px;
  display:flex;
  font-size:18px;
  color:#7F7F7F;
}
#u2571 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2571_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2572_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:18px;
  height:7px;
}
#u2572 {
  border-width:0px;
  position:absolute;
  left:246px;
  top:1583px;
  width:14px;
  height:3px;
  display:flex;
}
#u2572 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2572_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2573_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:189px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#D9001B;
}
#u2573 {
  border-width:0px;
  position:absolute;
  left:302px;
  top:1611px;
  width:189px;
  height:18px;
  display:flex;
  font-size:12px;
  color:#D9001B;
}
#u2573 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2573_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
