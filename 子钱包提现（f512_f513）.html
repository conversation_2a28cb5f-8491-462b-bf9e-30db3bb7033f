﻿<!DOCTYPE html>
<html>
  <head>
    <title>子钱包提现（F512\F513）</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/子钱包提现（f512_f513）/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/子钱包提现（f512_f513）/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (基础app框架(H5)) -->

      <!-- Unnamed (矩形) -->
      <div id="u682" class="ax_default box_1">
        <div id="u682_div" class=""></div>
        <div id="u682_text" class="text ">
          <p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u683" class="ax_default _二级标题">
        <div id="u683_div" class=""></div>
        <div id="u683_text" class="text ">
          <p><span>16:18</span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u684" class="ax_default icon">
        <img id="u684_img" class="img " src="images/海融宝签约_个人__f501_f502_/u3.svg"/>
        <div id="u684_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u685" class="ax_default icon">
        <img id="u685_img" class="img " src="images/海融宝签约_个人__f501_f502_/u4.svg"/>
        <div id="u685_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u686" class="ax_default _文本段落">
        <div id="u686_div" class=""></div>
        <div id="u686_text" class="text ">
          <p><span>提现</span></p>
        </div>
      </div>

      <!-- 操作状态 (动态面板) -->
      <div id="u687" class="ax_default ax_default_hidden" data-label="操作状态" style="display:none; visibility: hidden">
        <div id="u687_state0" class="panel_state" data-label="操作成功" style="">
          <div id="u687_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u688" class="ax_default box_3">
              <div id="u688_div" class=""></div>
              <div id="u688_text" class="text ">
                <p><span>操作成功！</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u687_state1" class="panel_state" data-label="操作失败" style="visibility: hidden;">
          <div id="u687_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u689" class="ax_default box_3">
              <div id="u689_div" class=""></div>
              <div id="u689_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u690" class="ax_default _文本段落">
              <div id="u690_div" class=""></div>
              <div id="u690_text" class="text ">
                <p><span>操作失败！</span></p>
              </div>
            </div>

            <!-- Unnamed (图片 ) -->
            <div id="u691" class="ax_default _图片_">
              <img id="u691_img" class="img " src="images/海融宝签约_个人__f501_f502_/u10.png"/>
              <div id="u691_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u692" class="ax_default _图片_">
        <img id="u692_img" class="img " src="images/海融宝签约_个人__f501_f502_/u11.png"/>
        <div id="u692_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u693" class="ax_default icon">
        <img id="u693_img" class="img " src="images/海融宝签约_个人__f501_f502_/u12.svg"/>
        <div id="u693_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u694" class="ax_default _文本段落">
        <div id="u694_div" class=""></div>
        <div id="u694_text" class="text ">
          <p><span>hrypt.com</span></p>
        </div>
      </div>
      <div id="u681" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (矩形) -->
      <div id="u695" class="ax_default primary_button">
        <div id="u695_div" class=""></div>
        <div id="u695_text" class="text ">
          <p><span>提交提现</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u696" class="ax_default _形状">
        <div id="u696_div" class=""></div>
        <div id="u696_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u697" class="ax_default" data-left="85" data-top="180" data-width="317" data-height="36">

        <!-- Unnamed (文本框) -->
        <div id="u698" class="ax_default text_field">
          <div id="u698_div" class=""></div>
          <input id="u698_input" type="text" value="" class="u698_input"/>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u699" class="ax_default _文本段落">
        <div id="u699_div" class=""></div>
        <div id="u699_text" class="text ">
          <p><span>提现金额</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u700" class="ax_default _文本段落">
        <div id="u700_div" class=""></div>
        <div id="u700_text" class="text ">
          <p><span>￥</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u701" class="ax_default _文本段落">
        <div id="u701_div" class=""></div>
        <div id="u701_text" class="text ">
          <p><span>说明：</span></p><p><span>1、只能提现可用余额</span></p><p><span>2、提交提现，跳转邮储数字人民里输入密码</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u702" class="ax_default primary_button">
        <div id="u702_div" class=""></div>
        <div id="u702_text" class="text ">
          <p><span>全部提现</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u703" class="ax_default _文本段落">
        <div id="u703_div" class=""></div>
        <div id="u703_text" class="text ">
          <p><span>请输入金额</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u704" class="ax_default _文本段落">
        <div id="u704_div" class=""></div>
        <div id="u704_text" class="text ">
          <p><span>可用余额为：21,165.00</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u705" class="ax_default" data-left="85" data-top="247" data-width="317" data-height="36">

        <!-- Unnamed (文本框) -->
        <div id="u706" class="ax_default text_field">
          <div id="u706_div" class=""></div>
          <input id="u706_input" type="text" value="" class="u706_input"/>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u707" class="ax_default" data-left="96" data-top="251" data-width="294" data-height="30">

          <!-- 叫号面板按钮 (动态面板) -->
          <div id="u708" class="ax_default" data-label="叫号面板按钮">
            <div id="u708_state0" class="panel_state" data-label="State1" style="">
              <div id="u708_state0_content" class="panel_state_content">

                <!-- Unnamed (矩形) -->
                <div id="u709" class="ax_default box_3">
                  <div id="u709_div" class=""></div>
                  <div id="u709_text" class="text ">
                    <p><span>获取验证码</span></p>
                  </div>
                </div>
              </div>
            </div>
            <div id="u708_state1" class="panel_state" data-label="State2" style="visibility: hidden;">
              <div id="u708_state1_content" class="panel_state_content">

                <!-- Unnamed (矩形) -->
                <div id="u710" class="ax_default box_3">
                  <div id="u710_div" class=""></div>
                  <div id="u710_text" class="text ">
                    <p><span>s</span></p>
                  </div>
                </div>

                <!-- 叫号倒计时 (文本框) -->
                <div id="u711" class="ax_default text_field ax_default_hidden" data-label="叫号倒计时" style="display:none; visibility: hidden">
                  <div id="u711_div" class=""></div>
                  <input id="u711_input" type="text" value="15" class="u711_input" readonly/>
                </div>
              </div>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u712" class="ax_default _文本段落">
            <div id="u712_div" class=""></div>
            <div id="u712_text" class="text ">
              <p><span>输入短信验证码</span></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u713" class="ax_default _文本段落">
        <div id="u713_div" class=""></div>
        <div id="u713_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u714" class="ax_default primary_button">
        <div id="u714_div" class=""></div>
        <div id="u714_text" class="text ">
          <p><span>支付未完成，继续支付</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u715" class="ax_default _文本段落">
        <div id="u715_div" class=""></div>
        <div id="u715_text" class="text ">
          <p style="font-size:20px;"><span style="color:#000000;">F512出金创单</span></p><p style="font-size:20px;"><span style="color:#000000;"><br></span></p><p style="font-size:13px;"><span>节点属性&nbsp; &nbsp;&nbsp; 中文描述&nbsp; &nbsp;&nbsp; 数据类型&nbsp; &nbsp;&nbsp; 必输&nbsp; &nbsp;&nbsp; 说明</span></p><p style="font-size:13px;"><span>platOrderNo&nbsp; &nbsp;&nbsp; 平台出金订单号&nbsp; &nbsp;&nbsp; char(100)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 平台生成的唯一标识,银企客户号+yyyyMMdd+序号</span></p><p style="font-size:13px;"><span>signAgrNo&nbsp; &nbsp;&nbsp; 签约协议号&nbsp; &nbsp;&nbsp; char(64)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 个人会员签约时生成</span></p><p style="font-size:13px;"><span>custId&nbsp; &nbsp;&nbsp; 客户Id&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 个人会员签约时生成</span></p><p style="font-size:13px;"><span>txAmt&nbsp; &nbsp;&nbsp; 交易金额&nbsp; &nbsp;&nbsp; char(22)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 保留两位小数</span></p><p style="font-size:13px;"><span>txCurr&nbsp; &nbsp;&nbsp; 交易币种&nbsp; &nbsp;&nbsp; char(3)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 人民币CNY</span></p><p style="font-size:13px;"><span>vrfCdSendFlag&nbsp; &nbsp;&nbsp; 是否发送验证码&nbsp; &nbsp;&nbsp; char(1)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 0-不发送，1-发送</span></p><p style="font-size:13px;"><span>txnRmrk&nbsp; &nbsp;&nbsp; 交易备注&nbsp; &nbsp;&nbsp; char(300)&nbsp; &nbsp;&nbsp; N&nbsp; </span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span>&nbsp;&nbsp; </span></p><p style="font-size:13px;"><span>节点属性&nbsp; &nbsp;&nbsp; 中文描述&nbsp; &nbsp;&nbsp; 数据类型&nbsp; &nbsp;&nbsp; 必输&nbsp; &nbsp;&nbsp; 说明</span></p><p style="font-size:13px;"><span>txOrderNo&nbsp; &nbsp;&nbsp; 交易订单号&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; </span></p><p style="font-size:13px;"><span>platOrderNo&nbsp; &nbsp;&nbsp; 平台订单号&nbsp; &nbsp;&nbsp; char(100)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 可用于查询交易详情</span></p><p style="font-size:13px;"><span>custId&nbsp; &nbsp;&nbsp; 客户Id&nbsp; &nbsp;&nbsp; char(100)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; </span></p><p style="font-size:13px;"><span>platfNo&nbsp; &nbsp;&nbsp; 平台编号&nbsp; &nbsp;&nbsp; char(48)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; </span></p><p style="font-size:13px;"><span>txAmt&nbsp; &nbsp;&nbsp; 交易金额&nbsp; &nbsp;&nbsp; char(22)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 保留两位小数</span></p><p style="font-size:13px;"><span>txCurr&nbsp; &nbsp;&nbsp; 交易币种&nbsp; &nbsp;&nbsp; char(3)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; CNY</span></p><p style="font-size:13px;"><span>mbno&nbsp; &nbsp;&nbsp; 手机号&nbsp; &nbsp;&nbsp; char(11)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; &quot;是否发送验证码&quot;为1时返回</span></p><p style="font-size:13px;"><span>txCrtTime&nbsp; &nbsp;&nbsp; 交易创建时间&nbsp; &nbsp;&nbsp; char(14)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; yyyyMMddHHmmss</span></p><p style="font-size:13px;"><span>sessNo&nbsp; &nbsp;&nbsp; 会话编号&nbsp; &nbsp;&nbsp; char(64)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; &quot;是否发送验证码&quot;为1时返回。</span></p><p style="font-size:13px;"><span>和验证码一一对应，F513出金结算时需要上送</span></p><p style="font-size:13px;"><span>bdlStaCd&nbsp; &nbsp;&nbsp; 业务处理状态代码&nbsp; &nbsp;&nbsp; char(4)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; PR00-成功，PR01-失败，PR02-处理中</span></p><p style="font-size:13px;"><span>failReason&nbsp; &nbsp;&nbsp; 失败原因&nbsp; &nbsp;&nbsp; char(500)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; 当业务处理状态代码为PR01-失败时返回</span></p><p style="font-size:20px;"><span style="color:#000000;"><br></span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u716" class="ax_default _文本段落">
        <div id="u716_div" class=""></div>
        <div id="u716_text" class="text ">
          <p style="font-size:20px;"><span style="color:#000000;">F513出金结算创单</span></p><p style="font-size:20px;"><span style="color:#000000;"><br></span></p><p style="font-size:13px;"><span>节点属性&nbsp; &nbsp;&nbsp; 中文描述&nbsp; &nbsp;&nbsp; 数据类型&nbsp; &nbsp;&nbsp; 必输&nbsp; &nbsp;&nbsp; 说明</span></p><p style="font-size:13px;"><span>txOrderNo&nbsp; &nbsp;&nbsp; 交易订单号&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 使用出金创单响应的交易订单号</span></p><p style="font-size:13px;"><span>platOrderNo&nbsp; &nbsp;&nbsp; 平台出金订单号&nbsp; &nbsp;&nbsp; char(100)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 平台生成的唯一标识,银企客户号+yyyyMMdd+序号.</span></p><p style="font-size:13px;"><span>需要和出金创单的平台出金订单号一致</span></p><p style="font-size:13px;"><span>custId&nbsp; &nbsp;&nbsp; 客户Id&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 个人签约时生成</span></p><p style="font-size:13px;"><span>sessNo&nbsp; &nbsp;&nbsp; 会话编号&nbsp; &nbsp;&nbsp; char(64)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 出金创单返回</span></p><p style="font-size:13px;"><span>verifyCode&nbsp; &nbsp;&nbsp; 短信验证码&nbsp; &nbsp;&nbsp; char(6)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 调用出金创单时会发送短信到客户手机</span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span>&nbsp;&nbsp; </span></p><p style="font-size:13px;"><span>节点属性&nbsp; &nbsp;&nbsp; 中文描述&nbsp; &nbsp;&nbsp; 数据类型&nbsp; &nbsp;&nbsp; 必输&nbsp; &nbsp;&nbsp; 说明</span></p><p style="font-size:13px;"><span>txOrderNo&nbsp; &nbsp;&nbsp; 交易订单号&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; </span></p><p style="font-size:13px;"><span>platOrderNo&nbsp; &nbsp;&nbsp; 平台订单号&nbsp; &nbsp;&nbsp; char(100)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; </span></p><p style="font-size:13px;"><span>txAmt&nbsp; &nbsp;&nbsp; 交易金额&nbsp; &nbsp;&nbsp; char(22)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 保留两位小数</span></p><p style="font-size:13px;"><span>txCurr&nbsp; &nbsp;&nbsp; 交易币种&nbsp; &nbsp;&nbsp; char(3)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 人民币CNY</span></p><p style="font-size:13px;"><span>txDealTime&nbsp; &nbsp;&nbsp; 交易处理时间&nbsp; &nbsp;&nbsp; char(14)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; yyyyMMddHHmmss</span></p><p style="font-size:13px;"><span>bdlStaCd&nbsp; &nbsp;&nbsp; 业务处理状态代码&nbsp; &nbsp;&nbsp; char(4)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; PR00-成功，PR01-失败，PR02-处理中</span></p><p style="font-size:13px;"><span>failReason&nbsp; &nbsp;&nbsp; 失败原因&nbsp; &nbsp;&nbsp; char(500)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; 当业务处理状态代码为PR01-失败时返回</span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:20px;"><span style="color:#000000;"><br></span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u717" class="ax_default primary_button">
        <div id="u717_div" class=""></div>
        <div id="u717_text" class="text ">
          <p><span>业务取消，关闭订单</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u718" class="ax_default _文本段落">
        <div id="u718_div" class=""></div>
        <div id="u718_text" class="text ">
          <p><span>说明：资金将从您的合约子钱包提现至您的邮储银行数字人民币钱包</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u719" class="ax_default _文本段落">
        <div id="u719_div" class=""></div>
        <div id="u719_text" class="text ">
          <p><span>数字人民币合约子钱包用户ID</span></p><p><span>3100 1234 9876 8888</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
