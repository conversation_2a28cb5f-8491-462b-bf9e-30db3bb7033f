﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:510px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u5838_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:900px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:50px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u5838 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:900px;
  display:flex;
  opacity:0.49;
}
#u5838 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5838_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5839_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u5839 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:20px;
  width:51px;
  height:40px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u5839 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5839_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5840_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:18px;
}
#u5840 {
  border-width:0px;
  position:absolute;
  left:425px;
  top:19px;
  width:23px;
  height:18px;
  display:flex;
}
#u5840 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5840_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5841_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:16px;
}
#u5841 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:21px;
  width:26px;
  height:16px;
  display:flex;
}
#u5841 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5841_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5842_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u5842 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:50px;
  width:300px;
  height:25px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u5842 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5842_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5843 {
  position:fixed;
  left:50%;
  margin-left:-75px;
  top:50%;
  margin-top:-25px;
  visibility:hidden;
}
#u5843_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u5843_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u5844_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u5844 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u5844 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u5844_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5843_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u5843_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u5845_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u5845 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u5845 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u5845_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5846_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u5846 {
  border-width:0px;
  position:absolute;
  left:60px;
  top:16px;
  width:80px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u5846 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5846_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u5847_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u5847 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:10px;
  width:30px;
  height:30px;
  display:flex;
}
#u5847 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5847_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5848_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u5848 {
  border-width:0px;
  position:absolute;
  left:463px;
  top:50px;
  width:25px;
  height:25px;
  display:flex;
}
#u5848 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5848_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5849_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:25px;
}
#u5849 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:50px;
  width:15px;
  height:25px;
  display:flex;
}
#u5849 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5849_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5850_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:228px;
  height:11px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  text-align:center;
}
#u5850 {
  border-width:0px;
  position:absolute;
  left:136px;
  top:71px;
  width:228px;
  height:11px;
  display:flex;
  font-size:10px;
  text-align:center;
}
#u5850 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5850_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5851_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:896px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.298039215686275);
  border:none;
  border-radius:50px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u5851 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:896px;
  display:flex;
}
#u5851 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5851_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5852_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:490px;
  height:737px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u5852 {
  border-width:0px;
  position:absolute;
  left:10px;
  top:118px;
  width:490px;
  height:737px;
  display:flex;
}
#u5852 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5852_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5853_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:40px;
  background:inherit;
  background-color:rgba(18, 150, 219, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(153, 153, 153, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u5853 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:303px;
  width:110px;
  height:40px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u5853 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5853_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5854_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(153, 153, 153, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u5854 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:365px;
  width:110px;
  height:40px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u5854 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5854_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5855_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(153, 153, 153, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u5855 {
  border-width:0px;
  position:absolute;
  left:381px;
  top:303px;
  width:110px;
  height:40px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u5855 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5855_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5856_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(153, 153, 153, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u5856 {
  border-width:0px;
  position:absolute;
  left:261px;
  top:303px;
  width:110px;
  height:40px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u5856 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5856_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5857_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(153, 153, 153, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u5857 {
  border-width:0px;
  position:absolute;
  left:140px;
  top:365px;
  width:110px;
  height:40px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u5857 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5857_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5858_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(153, 153, 153, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u5858 {
  border-width:0px;
  position:absolute;
  left:140px;
  top:303px;
  width:110px;
  height:40px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u5858 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5858_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5859_img {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-3px;
  width:463px;
  height:11px;
}
#u5859 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:211px;
  width:457px;
  height:5px;
  display:flex;
}
#u5859 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5859_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u5860 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u5861_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:141px;
  height:33px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(153, 153, 153, 1);
  border-radius:282px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#999999;
}
#u5861 {
  border-width:0px;
  position:absolute;
  left:102px;
  top:700px;
  width:141px;
  height:33px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#999999;
}
#u5861 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5861_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5862_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:141px;
  height:33px;
  background:inherit;
  background-color:rgba(18, 150, 219, 1);
  border:none;
  border-radius:282px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#FFFFFF;
}
#u5862 {
  border-width:0px;
  position:absolute;
  left:261px;
  top:700px;
  width:141px;
  height:33px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#FFFFFF;
}
#u5862 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5862_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5863_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(153, 153, 153, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u5863 {
  border-width:0px;
  position:absolute;
  left:381px;
  top:365px;
  width:110px;
  height:40px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u5863 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5863_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5864_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(153, 153, 153, 1);
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u5864 {
  border-width:0px;
  position:absolute;
  left:261px;
  top:365px;
  width:110px;
  height:40px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#999999;
}
#u5864 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5864_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5865_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:405px;
  height:42px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  color:#999999;
}
#u5865 {
  border-width:0px;
  position:absolute;
  left:50px;
  top:147px;
  width:405px;
  height:42px;
  display:flex;
  font-family:'PingFang SC ', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:20px;
  color:#999999;
}
#u5865 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5865_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5866_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:142px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  color:#7F7F7F;
  text-align:center;
}
#u5866 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:622px;
  width:142px;
  height:40px;
  display:flex;
  font-size:20px;
  color:#7F7F7F;
  text-align:center;
}
#u5866 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u5866_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5867_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:330px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#AAAAAA;
  text-align:left;
}
#u5867 {
  border-width:0px;
  position:absolute;
  left:158px;
  top:622px;
  width:330px;
  height:40px;
  display:flex;
  font-size:16px;
  color:#AAAAAA;
  text-align:left;
}
#u5867 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5867_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u5868_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u5868 {
  border-width:0px;
  position:absolute;
  left:466px;
  top:127px;
  width:25px;
  height:25px;
  display:flex;
}
#u5868 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u5868_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
