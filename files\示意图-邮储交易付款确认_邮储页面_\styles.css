﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:1793px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u888_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:900px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:50px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u888 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:900px;
  display:flex;
  opacity:0.49;
}
#u888 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u888_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u889_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u889 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:20px;
  width:51px;
  height:40px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u889 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u889_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u890_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:18px;
}
#u890 {
  border-width:0px;
  position:absolute;
  left:425px;
  top:19px;
  width:23px;
  height:18px;
  display:flex;
}
#u890 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u890_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u891_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:16px;
}
#u891 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:21px;
  width:26px;
  height:16px;
  display:flex;
}
#u891 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u891_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u892_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u892 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:50px;
  width:300px;
  height:25px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u892 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u892_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u893 {
  position:fixed;
  left:50%;
  margin-left:-75px;
  top:50%;
  margin-top:-25px;
  visibility:hidden;
}
#u893_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u893_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u894_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u894 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u894 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u894_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u893_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u893_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u895_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u895 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u895 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u895_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u896_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u896 {
  border-width:0px;
  position:absolute;
  left:60px;
  top:16px;
  width:80px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u896 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u896_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u897_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u897 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:10px;
  width:30px;
  height:30px;
  display:flex;
}
#u897 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u897_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u898_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u898 {
  border-width:0px;
  position:absolute;
  left:463px;
  top:50px;
  width:25px;
  height:25px;
  display:flex;
}
#u898 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u898_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u899_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:25px;
}
#u899 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:50px;
  width:15px;
  height:25px;
  display:flex;
}
#u899 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u899_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u900_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:228px;
  height:11px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  text-align:center;
}
#u900 {
  border-width:0px;
  position:absolute;
  left:136px;
  top:71px;
  width:228px;
  height:11px;
  display:flex;
  font-size:10px;
  text-align:center;
}
#u900 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u900_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u901_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:439px;
  height:50px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u901 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:786px;
  width:439px;
  height:50px;
  display:flex;
  font-size:18px;
}
#u901 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u901_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u902_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:460px;
  height:152px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u902 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:107px;
  width:460px;
  height:152px;
  display:flex;
  font-size:16px;
}
#u902 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u902_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u903_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:137px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:28px;
}
#u903 {
  border-width:0px;
  position:absolute;
  left:172px;
  top:151px;
  width:137px;
  height:32px;
  display:flex;
  font-size:28px;
}
#u903 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u903_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u904_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u904 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:226px;
  width:90px;
  height:25px;
  display:flex;
  font-size:16px;
}
#u904 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u904_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u905 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u906_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:284px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u906 {
  border-width:0px;
  position:absolute;
  left:133px;
  top:226px;
  width:284px;
  height:25px;
  display:flex;
  font-size:16px;
}
#u906 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u906_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u907 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u908_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:460px;
  height:128px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u908 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:313px;
  width:460px;
  height:128px;
  display:flex;
  font-size:16px;
}
#u908 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u908_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u909_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:225px;
  height:33px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  color:#000000;
}
#u909 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:325px;
  width:225px;
  height:33px;
  display:flex;
  font-size:20px;
  color:#000000;
}
#u909 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u909_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u910 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u911_input {
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u911_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u911_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u911 {
  border-width:0px;
  position:absolute;
  left:92px;
  top:370px;
  width:35px;
  height:35px;
  display:flex;
}
#u911 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u911_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u911.disabled {
}
#u912_input {
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u912_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u912_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u912 {
  border-width:0px;
  position:absolute;
  left:154px;
  top:370px;
  width:35px;
  height:35px;
  display:flex;
}
#u912 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u912_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u912.disabled {
}
#u913_input {
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u913_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u913_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u913 {
  border-width:0px;
  position:absolute;
  left:215px;
  top:370px;
  width:35px;
  height:35px;
  display:flex;
}
#u913 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u913_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u913.disabled {
}
#u914_input {
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u914_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u914_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u914 {
  border-width:0px;
  position:absolute;
  left:277px;
  top:370px;
  width:35px;
  height:35px;
  display:flex;
}
#u914 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u914_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u914.disabled {
}
#u915_input {
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u915_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u915_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u915 {
  border-width:0px;
  position:absolute;
  left:338px;
  top:370px;
  width:35px;
  height:35px;
  display:flex;
}
#u915 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u915_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u915.disabled {
}
#u916_input {
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u916_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u916_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u916 {
  border-width:0px;
  position:absolute;
  left:400px;
  top:370px;
  width:35px;
  height:35px;
  display:flex;
}
#u916 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u916_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u916.disabled {
}
#u917_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:460px;
  height:126px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u917 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:531px;
  width:460px;
  height:126px;
  display:flex;
  font-size:16px;
}
#u917 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u917_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u918_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:137px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u918 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:540px;
  width:137px;
  height:25px;
  display:flex;
  font-size:18px;
}
#u918 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u918_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u919_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:447px;
  height:79px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u919 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:568px;
  width:447px;
  height:79px;
  display:flex;
}
#u919 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u919_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u920_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:314px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#8400FF;
  text-align:right;
}
#u920 {
  border-width:0px;
  position:absolute;
  left:158px;
  top:114px;
  width:314px;
  height:16px;
  display:flex;
  font-size:14px;
  color:#8400FF;
  text-align:right;
}
#u920 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u920_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u921 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u922_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#AAAAAA;
}
#u922 {
  border-width:0px;
  position:absolute;
  left:1031px;
  top:28px;
  width:90px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#AAAAAA;
}
#u922 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u922_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u923 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u924_input {
  position:absolute;
  left:0px;
  top:0px;
  width:347px;
  height:36px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u924_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:347px;
  height:36px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u924_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:347px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u924 {
  border-width:0px;
  position:absolute;
  left:1123px;
  top:18px;
  width:347px;
  height:36px;
  display:flex;
}
#u924 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u924_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:347px;
  height:36px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u924.disabled {
}
#u925 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u926 {
  border-width:0px;
  position:absolute;
  left:1336px;
  top:22px;
  width:120px;
  height:30px;
}
#u926_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u926_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u927_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:111px;
  height:30px;
  background:inherit;
  background-color:rgba(194, 128, 255, 1);
  border:none;
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#000000;
}
#u927 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:111px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#000000;
}
#u927 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u927_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u926_state1 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:30px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u926_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u928_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  background:inherit;
  background-color:rgba(128, 128, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u928 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:30px;
  display:flex;
  font-size:18px;
  color:#000000;
  text-align:right;
}
#u928 .text {
  position:absolute;
  align-self:center;
  padding:2px 20px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u928_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u929_input {
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u929_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:30px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:center;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u929_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:center;
}
#u929 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:0px;
  width:60px;
  height:30px;
  display:flex;
  font-size:16px;
  text-align:center;
}
#u929 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u929_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:30px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:center;
}
#u929.disabled {
}
#u930_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:175px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#7F7F7F;
}
#u930 {
  border-width:0px;
  position:absolute;
  left:1135px;
  top:22px;
  width:175px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#7F7F7F;
}
#u930 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u930_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u931_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:756px;
  height:338px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u931 {
  border-width:0px;
  position:absolute;
  left:1037px;
  top:91px;
  width:756px;
  height:338px;
  display:flex;
}
#u931 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u931_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u932_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:460px;
  height:511px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u932 {
  border-width:0px;
  position:absolute;
  left:538px;
  top:0px;
  width:460px;
  height:511px;
  display:flex;
  font-size:16px;
}
#u932 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u932_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u933_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#AAAAAA;
}
#u933 {
  border-width:0px;
  position:absolute;
  left:559px;
  top:118px;
  width:90px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#AAAAAA;
}
#u933 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u933_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u934_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#AAAAAA;
}
#u934 {
  border-width:0px;
  position:absolute;
  left:559px;
  top:151px;
  width:90px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#AAAAAA;
}
#u934 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u934_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u935_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#AAAAAA;
}
#u935 {
  border-width:0px;
  position:absolute;
  left:559px;
  top:184px;
  width:90px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#AAAAAA;
}
#u935 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u935_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u936_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#AAAAAA;
}
#u936 {
  border-width:0px;
  position:absolute;
  left:559px;
  top:217px;
  width:90px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#AAAAAA;
}
#u936 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u936_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u937_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#AAAAAA;
}
#u937 {
  border-width:0px;
  position:absolute;
  left:559px;
  top:250px;
  width:90px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#AAAAAA;
}
#u937 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u937_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u938 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u939_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#AAAAAA;
}
#u939 {
  border-width:0px;
  position:absolute;
  left:559px;
  top:85px;
  width:90px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#AAAAAA;
}
#u939 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u939_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u940_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:334px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u940 {
  border-width:0px;
  position:absolute;
  left:651px;
  top:85px;
  width:334px;
  height:18px;
  display:flex;
  font-size:16px;
}
#u940 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u940_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u941_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:334px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u941 {
  border-width:0px;
  position:absolute;
  left:651px;
  top:118px;
  width:334px;
  height:18px;
  display:flex;
  font-size:16px;
}
#u941 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u941_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u942_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:334px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u942 {
  border-width:0px;
  position:absolute;
  left:651px;
  top:151px;
  width:334px;
  height:18px;
  display:flex;
  font-size:16px;
}
#u942 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u942_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u943_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:334px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u943 {
  border-width:0px;
  position:absolute;
  left:651px;
  top:184px;
  width:334px;
  height:18px;
  display:flex;
  font-size:16px;
}
#u943 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u943_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u944_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:334px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u944 {
  border-width:0px;
  position:absolute;
  left:651px;
  top:217px;
  width:334px;
  height:36px;
  display:flex;
  font-size:16px;
}
#u944 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u944_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u945_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:334px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u945 {
  border-width:0px;
  position:absolute;
  left:651px;
  top:250px;
  width:334px;
  height:18px;
  display:flex;
  font-size:16px;
}
#u945 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u945_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u946 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u947_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#AAAAAA;
}
#u947 {
  border-width:0px;
  position:absolute;
  left:559px;
  top:283px;
  width:90px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#AAAAAA;
}
#u947 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u947_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u948_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:334px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u948 {
  border-width:0px;
  position:absolute;
  left:651px;
  top:283px;
  width:334px;
  height:18px;
  display:flex;
  font-size:16px;
}
#u948 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u948_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u949 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u950_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#AAAAAA;
}
#u950 {
  border-width:0px;
  position:absolute;
  left:559px;
  top:316px;
  width:90px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#AAAAAA;
}
#u950 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u950_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u951_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:334px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u951 {
  border-width:0px;
  position:absolute;
  left:651px;
  top:316px;
  width:334px;
  height:18px;
  display:flex;
  font-size:16px;
}
#u951 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u951_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u952 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u953_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#AAAAAA;
}
#u953 {
  border-width:0px;
  position:absolute;
  left:559px;
  top:349px;
  width:90px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#AAAAAA;
}
#u953 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u953_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u954_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:334px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u954 {
  border-width:0px;
  position:absolute;
  left:651px;
  top:349px;
  width:334px;
  height:18px;
  display:flex;
  font-size:16px;
}
#u954 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u954_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u955 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u956_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#AAAAAA;
}
#u956 {
  border-width:0px;
  position:absolute;
  left:559px;
  top:52px;
  width:90px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#AAAAAA;
}
#u956 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u956_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u957_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:334px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u957 {
  border-width:0px;
  position:absolute;
  left:651px;
  top:52px;
  width:334px;
  height:18px;
  display:flex;
  font-size:16px;
}
#u957 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u957_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u958_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:399px;
  height:33px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  color:#000000;
}
#u958 {
  border-width:0px;
  position:absolute;
  left:559px;
  top:9px;
  width:399px;
  height:33px;
  display:flex;
  font-size:20px;
  color:#000000;
}
#u958 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u958_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u959 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u960_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#AAAAAA;
}
#u960 {
  border-width:0px;
  position:absolute;
  left:559px;
  top:382px;
  width:90px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#AAAAAA;
}
#u960 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u960_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u961_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:334px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u961 {
  border-width:0px;
  position:absolute;
  left:651px;
  top:382px;
  width:334px;
  height:18px;
  display:flex;
  font-size:16px;
}
#u961 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u961_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u962 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u963_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#AAAAAA;
}
#u963 {
  border-width:0px;
  position:absolute;
  left:559px;
  top:415px;
  width:90px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#AAAAAA;
}
#u963 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u963_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u964_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:334px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u964 {
  border-width:0px;
  position:absolute;
  left:651px;
  top:415px;
  width:334px;
  height:18px;
  display:flex;
  font-size:16px;
}
#u964 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u964_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u965_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:426px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Nunito Sans';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#D9001B;
}
#u965 {
  border-width:0px;
  position:absolute;
  left:559px;
  top:482px;
  width:426px;
  height:16px;
  display:flex;
  font-family:'Nunito Sans';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#D9001B;
}
#u965 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u965_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u966 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u967_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#AAAAAA;
}
#u967 {
  border-width:0px;
  position:absolute;
  left:559px;
  top:448px;
  width:90px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#AAAAAA;
}
#u967 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u967_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u968_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:334px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#000000;
}
#u968 {
  border-width:0px;
  position:absolute;
  left:651px;
  top:448px;
  width:334px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#000000;
}
#u968 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u968_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
