﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:1367px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u1423_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:900px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:50px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1423 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:900px;
  display:flex;
  opacity:0.49;
}
#u1423 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1423_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1424_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1424 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:20px;
  width:51px;
  height:40px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1424 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1424_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1425_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:18px;
}
#u1425 {
  border-width:0px;
  position:absolute;
  left:425px;
  top:19px;
  width:23px;
  height:18px;
  display:flex;
}
#u1425 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1425_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1426_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:16px;
}
#u1426 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:21px;
  width:26px;
  height:16px;
  display:flex;
}
#u1426 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1426_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1427_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u1427 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:50px;
  width:300px;
  height:25px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u1427 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1427_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1428 {
  position:fixed;
  left:50%;
  margin-left:-75px;
  top:50%;
  margin-top:-25px;
  visibility:hidden;
}
#u1428_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1428_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1429_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u1429 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u1429 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u1429_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1428_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u1428_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1430_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u1430 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u1430 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u1430_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1431_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u1431 {
  border-width:0px;
  position:absolute;
  left:60px;
  top:16px;
  width:80px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u1431 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1431_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1432_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u1432 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:10px;
  width:30px;
  height:30px;
  display:flex;
}
#u1432 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1432_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1433_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u1433 {
  border-width:0px;
  position:absolute;
  left:463px;
  top:50px;
  width:25px;
  height:25px;
  display:flex;
}
#u1433 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1433_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1434_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:25px;
}
#u1434 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:50px;
  width:15px;
  height:25px;
  display:flex;
}
#u1434 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1434_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1435_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:228px;
  height:11px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  text-align:center;
}
#u1435 {
  border-width:0px;
  position:absolute;
  left:136px;
  top:71px;
  width:228px;
  height:11px;
  display:flex;
  font-size:10px;
  text-align:center;
}
#u1435 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1435_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1436_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:137px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u1436 {
  border-width:0px;
  position:absolute;
  left:38px;
  top:143px;
  width:137px;
  height:25px;
  display:flex;
  font-size:18px;
}
#u1436 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1436_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1437_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:262px;
  height:40px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u1437 {
  border-width:0px;
  position:absolute;
  left:154px;
  top:764px;
  width:262px;
  height:40px;
  display:flex;
  font-size:18px;
}
#u1437 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1437_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1438_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:195px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1438 {
  border-width:0px;
  position:absolute;
  left:294px;
  top:816px;
  width:195px;
  height:15px;
  display:flex;
}
#u1438 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1438_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1439_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:240px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#7F7F7F;
  text-align:right;
}
#u1439 {
  border-width:0px;
  position:absolute;
  left:219px;
  top:168px;
  width:240px;
  height:18px;
  display:flex;
  font-size:12px;
  color:#7F7F7F;
  text-align:right;
}
#u1439 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1439_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1440_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:460px;
  height:128px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u1440 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:234px;
  width:460px;
  height:128px;
  display:flex;
  font-size:16px;
}
#u1440 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1440_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1441 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1442_input {
  position:absolute;
  left:0px;
  top:0px;
  width:317px;
  height:36px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1442_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:317px;
  height:36px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1442_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:317px;
  height:36px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1442 {
  border-width:0px;
  position:absolute;
  left:99px;
  top:295px;
  width:317px;
  height:36px;
  display:flex;
}
#u1442 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1442_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:317px;
  height:36px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1442.disabled {
}
#u1443_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:138px;
  height:33px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#AAAAAA;
}
#u1443 {
  border-width:0px;
  position:absolute;
  left:110px;
  top:297px;
  width:138px;
  height:33px;
  display:flex;
  font-size:16px;
  color:#AAAAAA;
}
#u1443 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1443_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1444_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:225px;
  height:33px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  color:#000000;
}
#u1444 {
  border-width:0px;
  position:absolute;
  left:48px;
  top:246px;
  width:225px;
  height:33px;
  display:flex;
  font-size:20px;
  color:#000000;
}
#u1444 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1444_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1445_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:42px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:36px;
}
#u1445 {
  border-width:0px;
  position:absolute;
  left:63px;
  top:294px;
  width:36px;
  height:42px;
  display:flex;
  font-size:36px;
}
#u1445 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1445_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1446 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1447_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:284px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:right;
}
#u1447 {
  border-width:0px;
  position:absolute;
  left:175px;
  top:143px;
  width:284px;
  height:25px;
  display:flex;
  font-size:16px;
  text-align:right;
}
#u1447 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1447_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1448_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:20px;
}
#u1448 {
  border-width:0px;
  position:absolute;
  left:465px;
  top:146px;
  width:15px;
  height:20px;
  display:flex;
  color:#555555;
}
#u1448 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1448_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1449_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:460px;
  height:128px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u1449 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:405px;
  width:460px;
  height:128px;
  display:flex;
  font-size:16px;
}
#u1449 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1449_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1450_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:225px;
  height:33px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  color:#000000;
}
#u1450 {
  border-width:0px;
  position:absolute;
  left:52px;
  top:417px;
  width:225px;
  height:33px;
  display:flex;
  font-size:20px;
  color:#000000;
}
#u1450 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1450_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1451 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1452_input {
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1452_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1452_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1452 {
  border-width:0px;
  position:absolute;
  left:126px;
  top:460px;
  width:35px;
  height:35px;
  display:flex;
}
#u1452 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1452_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1452.disabled {
}
#u1453_input {
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1453_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1453_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1453 {
  border-width:0px;
  position:absolute;
  left:188px;
  top:460px;
  width:35px;
  height:35px;
  display:flex;
}
#u1453 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1453_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1453.disabled {
}
#u1454_input {
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1454_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1454_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1454 {
  border-width:0px;
  position:absolute;
  left:249px;
  top:460px;
  width:35px;
  height:35px;
  display:flex;
}
#u1454 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1454_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1454.disabled {
}
#u1455_input {
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1455_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1455_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1455 {
  border-width:0px;
  position:absolute;
  left:311px;
  top:460px;
  width:35px;
  height:35px;
  display:flex;
}
#u1455 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1455_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1455.disabled {
}
#u1456_input {
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1456_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1456_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1456 {
  border-width:0px;
  position:absolute;
  left:372px;
  top:460px;
  width:35px;
  height:35px;
  display:flex;
}
#u1456 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1456_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1456.disabled {
}
#u1457_input {
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1457_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1457_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1457 {
  border-width:0px;
  position:absolute;
  left:434px;
  top:460px;
  width:35px;
  height:35px;
  display:flex;
}
#u1457 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1457_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:35px;
  height:35px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1457.disabled {
}
#u1458_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:371px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u1458 {
  border-width:0px;
  position:absolute;
  left:75px;
  top:538px;
  width:371px;
  height:16px;
  display:flex;
  font-size:14px;
}
#u1458 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1458_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1459_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:895px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.298039215686275);
  border:none;
  border-radius:50px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1459 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:895px;
  display:flex;
}
#u1459 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1459_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1460_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:562px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1460 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:374px;
  width:510px;
  height:562px;
  display:flex;
}
#u1460 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1460_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1461_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u1461 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:392px;
  width:25px;
  height:25px;
  display:flex;
}
#u1461 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1461_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1462_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:354px;
  height:25px;
}
#u1462 {
  border-width:0px;
  position:absolute;
  left:71px;
  top:392px;
  width:354px;
  height:25px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u1462 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1462_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1463_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:511px;
  height:2px;
}
#u1463 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:434px;
  width:510px;
  height:1px;
  display:flex;
  font-size:16px;
}
#u1463 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1463_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1464 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1465 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1466_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:302px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u1466 {
  border-width:0px;
  position:absolute;
  left:89px;
  top:513px;
  width:302px;
  height:18px;
  display:flex;
  font-size:16px;
}
#u1466 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1466_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1467_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:302px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#7F7F7F;
}
#u1467 {
  border-width:0px;
  position:absolute;
  left:89px;
  top:531px;
  width:302px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#7F7F7F;
}
#u1467 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1467_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1468_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u1468 {
  border-width:0px;
  position:absolute;
  left:50px;
  top:516px;
  width:30px;
  height:30px;
  display:flex;
}
#u1468 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1468_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1469_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u1469 {
  border-width:0px;
  position:absolute;
  left:439px;
  top:516px;
  width:30px;
  height:30px;
  display:flex;
}
#u1469 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1469_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1470 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1471 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1472_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:304px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u1472 {
  border-width:0px;
  position:absolute;
  left:89px;
  top:450px;
  width:304px;
  height:18px;
  display:flex;
  font-size:16px;
}
#u1472 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1472_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1473_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:304px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#7F7F7F;
}
#u1473 {
  border-width:0px;
  position:absolute;
  left:89px;
  top:468px;
  width:304px;
  height:15px;
  display:flex;
  color:#7F7F7F;
}
#u1473 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1473_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1474_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:30px;
}
#u1474 {
  border-width:0px;
  position:absolute;
  left:50px;
  top:452px;
  width:32px;
  height:30px;
  display:flex;
}
#u1474 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1474_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1475_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u1475 {
  border-width:0px;
  position:absolute;
  left:439px;
  top:452px;
  width:30px;
  height:30px;
  display:flex;
}
#u1475 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1475_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1476 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1477 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1478_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:302px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u1478 {
  border-width:0px;
  position:absolute;
  left:89px;
  top:576px;
  width:302px;
  height:18px;
  display:flex;
  font-size:16px;
}
#u1478 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1478_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1479_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:302px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#7F7F7F;
}
#u1479 {
  border-width:0px;
  position:absolute;
  left:89px;
  top:594px;
  width:302px;
  height:15px;
  display:flex;
  color:#7F7F7F;
}
#u1479 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1479_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1480_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u1480 {
  border-width:0px;
  position:absolute;
  left:50px;
  top:578px;
  width:30px;
  height:30px;
  display:flex;
}
#u1480 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1480_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1481_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u1481 {
  border-width:0px;
  position:absolute;
  left:439px;
  top:578px;
  width:30px;
  height:30px;
  display:flex;
}
#u1481 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1481_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1482 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1483 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1484_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:302px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u1484 {
  border-width:0px;
  position:absolute;
  left:89px;
  top:642px;
  width:302px;
  height:18px;
  display:flex;
  font-size:16px;
}
#u1484 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1484_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1485_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:302px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#7F7F7F;
}
#u1485 {
  border-width:0px;
  position:absolute;
  left:89px;
  top:660px;
  width:302px;
  height:15px;
  display:flex;
  color:#7F7F7F;
}
#u1485 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1485_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1486_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u1486 {
  border-width:0px;
  position:absolute;
  left:50px;
  top:644px;
  width:30px;
  height:30px;
  display:flex;
}
#u1486 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1486_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1487_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u1487 {
  border-width:0px;
  position:absolute;
  left:439px;
  top:644px;
  width:30px;
  height:30px;
  display:flex;
}
#u1487 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1487_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1488_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:35px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1488 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:695px;
  width:510px;
  height:35px;
  display:flex;
}
#u1488 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1488_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1489 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1490_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:302px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u1490 {
  border-width:0px;
  position:absolute;
  left:89px;
  top:703px;
  width:302px;
  height:21px;
  display:flex;
  font-size:18px;
}
#u1490 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1490_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1491_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:25px;
}
#u1491 {
  border-width:0px;
  position:absolute;
  left:447px;
  top:701px;
  width:15px;
  height:25px;
  display:flex;
  color:#555555;
}
#u1491 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1491_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1492_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u1492 {
  border-width:0px;
  position:absolute;
  left:50px;
  top:698px;
  width:30px;
  height:30px;
  display:flex;
}
#u1492 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1492_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1493 {
  position:fixed;
  left:50%;
  margin-left:-243px;
  top:50%;
  margin-top:-164px;
  width:486px;
  height:328px;
  visibility:hidden;
}
#u1493_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:486px;
  height:328px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1493_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1494_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:486px;
  height:320px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1494 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:486px;
  height:320px;
  display:flex;
}
#u1494 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1494_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1495_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u1495 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:11px;
  width:25px;
  height:25px;
  display:flex;
}
#u1495 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1495_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1496_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:354px;
  height:25px;
}
#u1496 {
  border-width:0px;
  position:absolute;
  left:72px;
  top:11px;
  width:354px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
  text-align:center;
}
#u1496 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1496_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1497_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:452px;
  height:200px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1497 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:47px;
  width:452px;
  height:200px;
  display:flex;
}
#u1497 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1497_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1498_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:203px;
  height:40px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u1498 {
  border-width:0px;
  position:absolute;
  left:137px;
  top:253px;
  width:203px;
  height:40px;
  display:flex;
  font-size:18px;
}
#u1498 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1498_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1499 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1500_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:349px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#AAAAAA;
}
#u1500 {
  border-width:0px;
  position:absolute;
  left:114px;
  top:55px;
  width:349px;
  height:26px;
  display:flex;
  font-size:16px;
  color:#AAAAAA;
}
#u1500 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1500_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1501_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:31px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:right;
}
#u1501 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:55px;
  width:100px;
  height:31px;
  display:flex;
  font-size:16px;
  text-align:right;
}
#u1501 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1501_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1502 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1503_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:349px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u1503 {
  border-width:0px;
  position:absolute;
  left:114px;
  top:85px;
  width:349px;
  height:26px;
  display:flex;
  font-size:16px;
}
#u1503 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1503_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1504_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:right;
}
#u1504 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:85px;
  width:100px;
  height:26px;
  display:flex;
  font-size:16px;
  text-align:right;
}
#u1504 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1504_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1505 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1506_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:349px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#000000;
}
#u1506 {
  border-width:0px;
  position:absolute;
  left:114px;
  top:116px;
  width:349px;
  height:26px;
  display:flex;
  font-size:16px;
  color:#000000;
}
#u1506 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1506_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1507_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:right;
}
#u1507 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:116px;
  width:100px;
  height:26px;
  display:flex;
  font-size:16px;
  text-align:right;
}
#u1507 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1507_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1508 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1509_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:349px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u1509 {
  border-width:0px;
  position:absolute;
  left:114px;
  top:147px;
  width:349px;
  height:26px;
  display:flex;
  font-size:16px;
}
#u1509 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1509_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1510_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:right;
}
#u1510 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:147px;
  width:100px;
  height:22px;
  display:flex;
  font-size:16px;
  text-align:right;
}
#u1510 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1510_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1511 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1512_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:349px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#000000;
}
#u1512 {
  border-width:0px;
  position:absolute;
  left:114px;
  top:177px;
  width:349px;
  height:26px;
  display:flex;
  font-size:16px;
  color:#000000;
}
#u1512 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1512_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1513_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:right;
}
#u1513 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:177px;
  width:100px;
  height:26px;
  display:flex;
  font-size:16px;
  text-align:right;
}
#u1513 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1513_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1514 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1515_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:349px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#000000;
}
#u1515 {
  border-width:0px;
  position:absolute;
  left:114px;
  top:208px;
  width:349px;
  height:26px;
  display:flex;
  font-size:16px;
  color:#000000;
}
#u1515 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1515_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1516_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:right;
}
#u1516 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:208px;
  width:100px;
  height:26px;
  display:flex;
  font-size:16px;
  text-align:right;
}
#u1516 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1516_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1517_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:299px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1517 {
  border-width:0px;
  position:absolute;
  left:39px;
  top:304px;
  width:299px;
  height:15px;
  display:flex;
}
#u1517 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1517_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1518_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:486px;
  height:320px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:15px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1518 {
  border-width:0px;
  position:absolute;
  left:881px;
  top:844px;
  width:486px;
  height:320px;
  display:flex;
}
#u1518 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1518_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1519_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u1519 {
  border-width:0px;
  position:absolute;
  left:895px;
  top:855px;
  width:25px;
  height:25px;
  display:flex;
}
#u1519 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1519_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1520_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:354px;
  height:25px;
}
#u1520 {
  border-width:0px;
  position:absolute;
  left:953px;
  top:855px;
  width:354px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
  text-align:center;
}
#u1520 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1520_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1521_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:452px;
  height:200px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1521 {
  border-width:0px;
  position:absolute;
  left:895px;
  top:891px;
  width:452px;
  height:200px;
  display:flex;
}
#u1521 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1521_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1522_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:203px;
  height:40px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:8px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
}
#u1522 {
  border-width:0px;
  position:absolute;
  left:1018px;
  top:1097px;
  width:203px;
  height:40px;
  display:flex;
  font-size:18px;
}
#u1522 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1522_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1523 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1524_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:349px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#AAAAAA;
}
#u1524 {
  border-width:0px;
  position:absolute;
  left:995px;
  top:899px;
  width:349px;
  height:26px;
  display:flex;
  font-size:16px;
  color:#AAAAAA;
}
#u1524 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1524_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1525_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:31px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:right;
}
#u1525 {
  border-width:0px;
  position:absolute;
  left:895px;
  top:899px;
  width:100px;
  height:31px;
  display:flex;
  font-size:16px;
  text-align:right;
}
#u1525 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1525_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1526 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1527_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:349px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u1527 {
  border-width:0px;
  position:absolute;
  left:995px;
  top:929px;
  width:349px;
  height:26px;
  display:flex;
  font-size:16px;
}
#u1527 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1527_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1528_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:right;
}
#u1528 {
  border-width:0px;
  position:absolute;
  left:895px;
  top:929px;
  width:100px;
  height:26px;
  display:flex;
  font-size:16px;
  text-align:right;
}
#u1528 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1528_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1529 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1530_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:349px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#000000;
}
#u1530 {
  border-width:0px;
  position:absolute;
  left:995px;
  top:960px;
  width:349px;
  height:26px;
  display:flex;
  font-size:16px;
  color:#000000;
}
#u1530 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1530_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1531_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:right;
}
#u1531 {
  border-width:0px;
  position:absolute;
  left:895px;
  top:960px;
  width:100px;
  height:26px;
  display:flex;
  font-size:16px;
  text-align:right;
}
#u1531 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1531_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1532 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1533_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:349px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u1533 {
  border-width:0px;
  position:absolute;
  left:995px;
  top:991px;
  width:349px;
  height:26px;
  display:flex;
  font-size:16px;
}
#u1533 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1533_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1534_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:right;
}
#u1534 {
  border-width:0px;
  position:absolute;
  left:895px;
  top:991px;
  width:100px;
  height:22px;
  display:flex;
  font-size:16px;
  text-align:right;
}
#u1534 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1534_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1535 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1536_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:349px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#000000;
}
#u1536 {
  border-width:0px;
  position:absolute;
  left:995px;
  top:1021px;
  width:349px;
  height:26px;
  display:flex;
  font-size:16px;
  color:#000000;
}
#u1536 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1536_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1537_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:right;
}
#u1537 {
  border-width:0px;
  position:absolute;
  left:895px;
  top:1021px;
  width:100px;
  height:26px;
  display:flex;
  font-size:16px;
  text-align:right;
}
#u1537 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1537_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1538 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1539_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:349px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#000000;
}
#u1539 {
  border-width:0px;
  position:absolute;
  left:995px;
  top:1052px;
  width:349px;
  height:26px;
  display:flex;
  font-size:16px;
  color:#000000;
}
#u1539 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1539_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1540_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:26px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  text-align:right;
}
#u1540 {
  border-width:0px;
  position:absolute;
  left:895px;
  top:1052px;
  width:100px;
  height:26px;
  display:flex;
  font-size:16px;
  text-align:right;
}
#u1540 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1540_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1541_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:299px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1541 {
  border-width:0px;
  position:absolute;
  left:920px;
  top:1148px;
  width:299px;
  height:15px;
  display:flex;
}
#u1541 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1541_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
