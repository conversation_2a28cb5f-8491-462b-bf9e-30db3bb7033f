﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-75px;
  width:1154px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u6394 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u6395 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u6396_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:276px;
  height:685px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6396 {
  border-width:0px;
  position:absolute;
  left:124px;
  top:82px;
  width:276px;
  height:685px;
  display:flex;
}
#u6396 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6396_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6397_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:276px;
  height:685px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6397 {
  border-width:0px;
  position:absolute;
  left:400px;
  top:82px;
  width:276px;
  height:685px;
  display:flex;
}
#u6397 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6397_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6398_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:276px;
  height:685px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6398 {
  border-width:0px;
  position:absolute;
  left:677px;
  top:82px;
  width:276px;
  height:685px;
  display:flex;
}
#u6398 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6398_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6399_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:276px;
  height:685px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6399 {
  border-width:0px;
  position:absolute;
  left:953px;
  top:82px;
  width:276px;
  height:685px;
  display:flex;
}
#u6399 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6399_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6400_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:64px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u6400 {
  border-width:0px;
  position:absolute;
  left:198px;
  top:95px;
  width:130px;
  height:64px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u6400 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u6400_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6401_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:64px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u6401 {
  border-width:0px;
  position:absolute;
  left:474px;
  top:95px;
  width:108px;
  height:64px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u6401 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u6401_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6402_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:188px;
  height:64px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u6402 {
  border-width:0px;
  position:absolute;
  left:721px;
  top:95px;
  width:188px;
  height:64px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u6402 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u6402_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6403_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:174px;
  height:64px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u6403 {
  border-width:0px;
  position:absolute;
  left:1004px;
  top:95px;
  width:174px;
  height:64px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u6403 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u6403_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6404_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:45px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6404 {
  border-width:0px;
  position:absolute;
  left:142px;
  top:159px;
  width:104px;
  height:45px;
  display:flex;
}
#u6404 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6404_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6405_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:384px;
  height:23px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
}
#u6405 {
  border-width:0px;
  position:absolute;
  left:75px;
  top:42px;
  width:384px;
  height:23px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
}
#u6405 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u6405_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6406_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:91px;
  height:45px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6406 {
  border-width:0px;
  position:absolute;
  left:279px;
  top:159px;
  width:91px;
  height:45px;
  display:flex;
}
#u6406 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6406_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6407 {
  border-width:0px;
  position:absolute;
  left:246px;
  top:182px;
  width:0px;
  height:0px;
}
#u6407_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:38px;
  height:10px;
}
#u6407_seg1 {
  border-width:0px;
  position:absolute;
  left:21px;
  top:-10px;
  width:20px;
  height:20px;
}
#u6407_text {
  border-width:0px;
  position:absolute;
  left:-34px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6408_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:45px;
  background:inherit;
  background-color:rgba(128, 128, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6408 {
  border-width:0px;
  position:absolute;
  left:464px;
  top:159px;
  width:193px;
  height:45px;
  display:flex;
}
#u6408 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6408_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6409 {
  border-width:0px;
  position:absolute;
  left:370px;
  top:182px;
  width:0px;
  height:0px;
}
#u6409_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:99px;
  height:10px;
}
#u6409_seg1 {
  border-width:0px;
  position:absolute;
  left:82px;
  top:-10px;
  width:20px;
  height:20px;
}
#u6409_text {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6410_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:198px;
  height:49px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6410 {
  border-width:0px;
  position:absolute;
  left:464px;
  top:270px;
  width:198px;
  height:49px;
  display:flex;
}
#u6410 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6410_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6411 {
  border-width:0px;
  position:absolute;
  left:851px;
  top:204px;
  width:0px;
  height:0px;
}
#u6411_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:71px;
}
#u6411_seg1 {
  border-width:0px;
  position:absolute;
  left:-10px;
  top:54px;
  width:20px;
  height:20px;
}
#u6411_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:25px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6412_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:49px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6412 {
  border-width:0px;
  position:absolute;
  left:224px;
  top:270px;
  width:78px;
  height:49px;
  display:flex;
}
#u6412 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6412_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6413 {
  border-width:0px;
  position:absolute;
  left:464px;
  top:295px;
  width:0px;
  height:0px;
}
#u6413_seg0 {
  border-width:0px;
  position:absolute;
  left:-162px;
  top:-5px;
  width:167px;
  height:10px;
}
#u6413_seg1 {
  border-width:0px;
  position:absolute;
  left:-170px;
  top:-10px;
  width:20px;
  height:20px;
}
#u6413_text {
  border-width:0px;
  position:absolute;
  left:-131px;
  top:-6px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
#u6414_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:170px;
  height:45px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6414 {
  border-width:0px;
  position:absolute;
  left:766px;
  top:159px;
  width:170px;
  height:45px;
  display:flex;
}
#u6414 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6414_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6415 {
  border-width:0px;
  position:absolute;
  left:657px;
  top:182px;
  width:0px;
  height:0px;
}
#u6415_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:114px;
  height:10px;
}
#u6415_seg1 {
  border-width:0px;
  position:absolute;
  left:97px;
  top:-10px;
  width:20px;
  height:20px;
}
#u6415_text {
  border-width:0px;
  position:absolute;
  left:4px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6416 {
  border-width:0px;
  position:absolute;
  left:766px;
  top:295px;
  width:0px;
  height:0px;
}
#u6416_seg0 {
  border-width:0px;
  position:absolute;
  left:-104px;
  top:-5px;
  width:109px;
  height:10px;
}
#u6416_seg1 {
  border-width:0px;
  position:absolute;
  left:-112px;
  top:-10px;
  width:20px;
  height:20px;
}
#u6416_text {
  border-width:0px;
  position:absolute;
  left:-102px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6417 {
  border-width:0px;
  position:absolute;
  left:194px;
  top:204px;
  width:0px;
  height:0px;
}
#u6417_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:211px;
}
#u6417_seg1 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:201px;
  width:38px;
  height:10px;
}
#u6417_seg2 {
  border-width:0px;
  position:absolute;
  left:21px;
  top:196px;
  width:20px;
  height:20px;
}
#u6417_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:112px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6418_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:170px;
  height:49px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6418 {
  border-width:0px;
  position:absolute;
  left:766px;
  top:270px;
  width:170px;
  height:49px;
  display:flex;
}
#u6418 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6418_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6419_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:47px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6419 {
  border-width:0px;
  position:absolute;
  left:227px;
  top:386px;
  width:104px;
  height:47px;
  display:flex;
}
#u6419 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6419_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6420_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:194px;
  height:47px;
  background:inherit;
  background-color:rgba(128, 128, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6420 {
  border-width:0px;
  position:absolute;
  left:464px;
  top:386px;
  width:194px;
  height:47px;
  display:flex;
}
#u6420 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6420_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6421 {
  border-width:0px;
  position:absolute;
  left:331px;
  top:410px;
  width:0px;
  height:0px;
}
#u6421_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:138px;
  height:10px;
}
#u6421_seg1 {
  border-width:0px;
  position:absolute;
  left:121px;
  top:-10px;
  width:20px;
  height:20px;
}
#u6421_text {
  border-width:0px;
  position:absolute;
  left:16px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6422_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:194px;
  height:46px;
  background:inherit;
  background-color:rgba(128, 128, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6422 {
  border-width:0px;
  position:absolute;
  left:464px;
  top:478px;
  width:194px;
  height:46px;
  display:flex;
}
#u6422 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6422_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6423 {
  border-width:0px;
  position:absolute;
  left:561px;
  top:433px;
  width:0px;
  height:0px;
}
#u6423_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:50px;
}
#u6423_seg1 {
  border-width:0px;
  position:absolute;
  left:-10px;
  top:33px;
  width:20px;
  height:20px;
}
#u6423_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:14px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6424_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:189px;
  height:39px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6424 {
  border-width:0px;
  position:absolute;
  left:1015px;
  top:481px;
  width:189px;
  height:39px;
  display:flex;
}
#u6424 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6424_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6425 {
  border-width:0px;
  position:absolute;
  left:658px;
  top:501px;
  width:0px;
  height:0px;
}
#u6425_seg0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-5px;
  width:362px;
  height:10px;
}
#u6425_seg1 {
  border-width:0px;
  position:absolute;
  left:345px;
  top:-10px;
  width:20px;
  height:20px;
}
#u6425_text {
  border-width:0px;
  position:absolute;
  left:128px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6426_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:189px;
  height:38px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6426 {
  border-width:0px;
  position:absolute;
  left:1015px;
  top:580px;
  width:189px;
  height:38px;
  display:flex;
}
#u6426 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6426_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6427_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:38px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6427 {
  border-width:0px;
  position:absolute;
  left:224px;
  top:580px;
  width:104px;
  height:38px;
  display:flex;
}
#u6427 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6427_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6428_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:194px;
  height:38px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u6428 {
  border-width:0px;
  position:absolute;
  left:468px;
  top:580px;
  width:194px;
  height:38px;
  display:flex;
}
#u6428 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u6428_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u6429 {
  border-width:0px;
  position:absolute;
  left:1110px;
  top:520px;
  width:0px;
  height:0px;
}
#u6429_seg0 {
  border-width:0px;
  position:absolute;
  left:-5px;
  top:0px;
  width:10px;
  height:65px;
}
#u6429_seg1 {
  border-width:0px;
  position:absolute;
  left:-10px;
  top:48px;
  width:20px;
  height:20px;
}
#u6429_text {
  border-width:0px;
  position:absolute;
  left:-50px;
  top:22px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6430 {
  border-width:0px;
  position:absolute;
  left:1015px;
  top:599px;
  width:0px;
  height:0px;
}
#u6430_seg0 {
  border-width:0px;
  position:absolute;
  left:-353px;
  top:-5px;
  width:358px;
  height:10px;
}
#u6430_seg1 {
  border-width:0px;
  position:absolute;
  left:-361px;
  top:-10px;
  width:20px;
  height:20px;
}
#u6430_text {
  border-width:0px;
  position:absolute;
  left:-226px;
  top:-8px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u6431 {
  border-width:0px;
  position:absolute;
  left:468px;
  top:599px;
  width:0px;
  height:0px;
}
#u6431_seg0 {
  border-width:0px;
  position:absolute;
  left:-140px;
  top:-5px;
  width:145px;
  height:10px;
}
#u6431_seg1 {
  border-width:0px;
  position:absolute;
  left:-148px;
  top:-10px;
  width:20px;
  height:20px;
}
#u6431_text {
  border-width:0px;
  position:absolute;
  left:-120px;
  top:-11px;
  width:100px;
  word-wrap:break-word;
  text-transform:none;
}
