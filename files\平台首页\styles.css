﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:510px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u2786_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:900px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:50px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2786 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:900px;
  display:flex;
  opacity:0.49;
}
#u2786 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2786_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2787 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2788 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2789_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u2789 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:834px;
  width:26px;
  height:25px;
  display:flex;
}
#u2789 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2789_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2790_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u2790 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:860px;
  width:26px;
  height:14px;
  display:flex;
  text-align:center;
}
#u2790 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2790_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2791 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2792_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u2792 {
  border-width:0px;
  position:absolute;
  left:130px;
  top:834px;
  width:26px;
  height:25px;
  display:flex;
}
#u2792 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2792_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2793_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u2793 {
  border-width:0px;
  position:absolute;
  left:130px;
  top:860px;
  width:26px;
  height:14px;
  display:flex;
  text-align:center;
}
#u2793 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2793_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2794 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2795_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u2795 {
  border-width:0px;
  position:absolute;
  left:345px;
  top:834px;
  width:26px;
  height:25px;
  display:flex;
}
#u2795 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2795_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2796_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  background-image:url('../../images/平台首页/u2795.png');
  background-repeat:no-repeat;
  background-size:200px 200px;
  background-position: left top;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u2796 {
  border-width:0px;
  position:absolute;
  left:345px;
  top:860px;
  width:26px;
  height:14px;
  display:flex;
  text-align:center;
}
#u2796 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2796_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2797 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2798_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u2798 {
  border-width:0px;
  position:absolute;
  left:452px;
  top:834px;
  width:26px;
  height:25px;
  display:flex;
}
#u2798 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2798_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2799_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u2799 {
  border-width:0px;
  position:absolute;
  left:452px;
  top:860px;
  width:26px;
  height:14px;
  display:flex;
  text-align:center;
}
#u2799 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2799_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2800 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2801_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u2801 {
  border-width:0px;
  position:absolute;
  left:237px;
  top:834px;
  width:26px;
  height:25px;
  display:flex;
}
#u2801 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2801_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2802_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:44px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u2802 {
  border-width:0px;
  position:absolute;
  left:228px;
  top:860px;
  width:44px;
  height:14px;
  display:flex;
  text-align:center;
}
#u2802 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2802_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2803_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 0, 0, 1);
  box-sizing:border-box;
  border-width:2px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:75px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u2803 {
  border-width:0px;
  position:absolute;
  left:363px;
  top:834px;
  width:21px;
  height:15px;
  display:flex;
  color:#FFFFFF;
}
#u2803 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2803_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2804_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u2804 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:20px;
  width:51px;
  height:40px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u2804 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2804_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2805_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:18px;
}
#u2805 {
  border-width:0px;
  position:absolute;
  left:425px;
  top:19px;
  width:23px;
  height:18px;
  display:flex;
}
#u2805 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2805_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2806_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:16px;
}
#u2806 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:21px;
  width:26px;
  height:16px;
  display:flex;
}
#u2806 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2806_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2807_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:25px;
}
#u2807 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:50px;
  width:24px;
  height:25px;
  display:flex;
}
#u2807 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2807_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2808_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u2808 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:49px;
  width:300px;
  height:25px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u2808 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2808_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2809 {
  position:fixed;
  left:50%;
  margin-left:-75px;
  top:50%;
  margin-top:-25px;
  visibility:hidden;
}
#u2809_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2809_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u2810_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u2810 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u2810 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2810_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2809_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u2809_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u2811_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u2811 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u2811 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u2811_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2812_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u2812 {
  border-width:0px;
  position:absolute;
  left:60px;
  top:16px;
  width:80px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u2812 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2812_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u2813_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u2813 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:10px;
  width:30px;
  height:30px;
  display:flex;
}
#u2813 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2813_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2814_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:228px;
  height:11px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  text-align:center;
}
#u2814 {
  border-width:0px;
  position:absolute;
  left:136px;
  top:71px;
  width:228px;
  height:11px;
  display:flex;
  font-size:10px;
  text-align:center;
}
#u2814 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2814_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2815_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:220px;
}
#u2815 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:47px;
  width:510px;
  height:220px;
  display:flex;
}
#u2815 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2815_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2816_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:474px;
  height:118px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2816 {
  border-width:0px;
  position:absolute;
  left:18px;
  top:202px;
  width:474px;
  height:118px;
  display:flex;
}
#u2816 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2816_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2817_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:24px;
  color:#FFFFFF;
  text-align:center;
}
#u2817 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:101px;
  width:510px;
  height:28px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:24px;
  color:#FFFFFF;
  text-align:center;
}
#u2817 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2817_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2818 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2819_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:484px;
  height:192px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2819 {
  border-width:0px;
  position:absolute;
  left:12px;
  top:340px;
  width:484px;
  height:192px;
  display:flex;
}
#u2819 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2819_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2820_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:179px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u2820 {
  border-width:0px;
  position:absolute;
  left:56px;
  top:353px;
  width:179px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u2820 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2820_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2821_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:214px;
  height:146px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2821 {
  border-width:0px;
  position:absolute;
  left:21px;
  top:377px;
  width:214px;
  height:146px;
  display:flex;
}
#u2821 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2821_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2822_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:214px;
  height:146px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u2822 {
  border-width:0px;
  position:absolute;
  left:274px;
  top:377px;
  width:214px;
  height:146px;
  display:flex;
}
#u2822 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2822_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2823_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:173px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#C280FF;
}
#u2823 {
  border-width:0px;
  position:absolute;
  left:39px;
  top:391px;
  width:173px;
  height:22px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#C280FF;
}
#u2823 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2823_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2824_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:187px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#C280FF;
}
#u2824 {
  border-width:0px;
  position:absolute;
  left:285px;
  top:391px;
  width:187px;
  height:24px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
  color:#C280FF;
}
#u2824 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2824_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2825_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:214px;
  height:110px;
}
#u2825 {
  border-width:0px;
  position:absolute;
  left:21px;
  top:415px;
  width:214px;
  height:110px;
  display:flex;
}
#u2825 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2825_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2826_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:214px;
  height:110px;
}
#u2826 {
  border-width:0px;
  position:absolute;
  left:274px;
  top:415px;
  width:214px;
  height:110px;
  display:flex;
}
#u2826 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2826_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2827_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:179px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
}
#u2827 {
  border-width:0px;
  position:absolute;
  left:56px;
  top:545px;
  width:179px;
  height:19px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
}
#u2827 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2827_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2828_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:21px;
}
#u2828 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:351px;
  width:25px;
  height:21px;
  display:flex;
}
#u2828 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2828_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2829_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:21px;
}
#u2829 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:545px;
  width:25px;
  height:21px;
  display:flex;
}
#u2829 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2829_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2830 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2831_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:214px;
  height:67px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u2831 {
  border-width:0px;
  position:absolute;
  left:274px;
  top:647px;
  width:214px;
  height:67px;
  display:flex;
  font-size:16px;
}
#u2831 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2831_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2832_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:113px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
  color:#C280FF;
}
#u2832 {
  border-width:0px;
  position:absolute;
  left:281px;
  top:652px;
  width:113px;
  height:22px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
  color:#C280FF;
}
#u2832 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2832_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2833_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D9001B;
  text-align:right;
}
#u2833 {
  border-width:0px;
  position:absolute;
  left:427px;
  top:652px;
  width:57px;
  height:14px;
  display:flex;
  font-size:16px;
  color:#D9001B;
  text-align:right;
}
#u2833 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2833_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2834_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u2834 {
  border-width:0px;
  position:absolute;
  left:277px;
  top:690px;
  width:57px;
  height:21px;
  display:flex;
  font-size:14px;
}
#u2834 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2834_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2835_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u2835 {
  border-width:0px;
  position:absolute;
  left:337px;
  top:690px;
  width:80px;
  height:21px;
  display:flex;
  font-size:14px;
}
#u2835 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2835_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2836_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:13px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u2836 {
  border-width:0px;
  position:absolute;
  left:427px;
  top:698px;
  width:57px;
  height:13px;
  display:flex;
  font-size:12px;
}
#u2836 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2836_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2837_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:13px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u2837 {
  border-width:0px;
  position:absolute;
  left:378px;
  top:668px;
  width:106px;
  height:13px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u2837 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2837_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2838_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:13px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  text-align:right;
}
#u2838 {
  border-width:0px;
  position:absolute;
  left:398px;
  top:683px;
  width:85px;
  height:13px;
  display:flex;
  font-size:12px;
  text-align:right;
}
#u2838 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2838_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2839_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#0000FF;
  text-align:right;
}
#u2839 {
  border-width:0px;
  position:absolute;
  left:414px;
  top:731px;
  width:73px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#0000FF;
  text-align:right;
}
#u2839 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2839_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2840 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2841 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2842 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2843_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:214px;
  height:64px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u2843 {
  border-width:0px;
  position:absolute;
  left:21px;
  top:572px;
  width:214px;
  height:64px;
  display:flex;
  font-size:16px;
}
#u2843 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2843_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2844_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
  color:#C280FF;
}
#u2844 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:577px;
  width:106px;
  height:22px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
  color:#C280FF;
}
#u2844 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2844_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2845_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u2845 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:616px;
  width:57px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u2845 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2845_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2846_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u2846 {
  border-width:0px;
  position:absolute;
  left:91px;
  top:616px;
  width:68px;
  height:18px;
  display:flex;
  font-size:14px;
}
#u2846 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2846_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2847_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D9001B;
  text-align:right;
}
#u2847 {
  border-width:0px;
  position:absolute;
  left:172px;
  top:577px;
  width:57px;
  height:14px;
  display:flex;
  font-size:16px;
  color:#D9001B;
  text-align:right;
}
#u2847 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2847_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2848_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:13px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#8400FF;
  text-align:right;
}
#u2848 {
  border-width:0px;
  position:absolute;
  left:124px;
  top:590px;
  width:106px;
  height:13px;
  display:flex;
  font-size:14px;
  color:#8400FF;
  text-align:right;
}
#u2848 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2848_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2849_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:13px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u2849 {
  border-width:0px;
  position:absolute;
  left:172px;
  top:621px;
  width:57px;
  height:13px;
  display:flex;
  font-size:12px;
}
#u2849 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2849_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2850_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:11px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  text-align:right;
}
#u2850 {
  border-width:0px;
  position:absolute;
  left:139px;
  top:605px;
  width:90px;
  height:11px;
  display:flex;
  font-size:12px;
  text-align:right;
}
#u2850 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2850_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2851 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2852_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:19px;
}
#u2852 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:595px;
  width:21px;
  height:19px;
  display:flex;
  font-size:16px;
}
#u2852 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2852_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2853_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:113px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u2853 {
  border-width:0px;
  position:absolute;
  left:51px;
  top:598px;
  width:113px;
  height:14px;
  display:flex;
  font-size:16px;
}
#u2853 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2853_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2854 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2855 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2856_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:214px;
  height:67px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u2856 {
  border-width:0px;
  position:absolute;
  left:274px;
  top:572px;
  width:214px;
  height:67px;
  display:flex;
  font-size:16px;
}
#u2856 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2856_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2857_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:128px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
  color:#C280FF;
}
#u2857 {
  border-width:0px;
  position:absolute;
  left:281px;
  top:577px;
  width:128px;
  height:22px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
  color:#C280FF;
}
#u2857 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2857_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2858_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D9001B;
  text-align:right;
}
#u2858 {
  border-width:0px;
  position:absolute;
  left:427px;
  top:577px;
  width:57px;
  height:14px;
  display:flex;
  font-size:16px;
  color:#D9001B;
  text-align:right;
}
#u2858 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2858_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2859_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u2859 {
  border-width:0px;
  position:absolute;
  left:277px;
  top:617px;
  width:57px;
  height:20px;
  display:flex;
  font-size:14px;
}
#u2859 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2859_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2860_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:73px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u2860 {
  border-width:0px;
  position:absolute;
  left:344px;
  top:617px;
  width:73px;
  height:20px;
  display:flex;
  font-size:14px;
}
#u2860 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2860_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2861_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:13px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u2861 {
  border-width:0px;
  position:absolute;
  left:427px;
  top:624px;
  width:57px;
  height:13px;
  display:flex;
  font-size:12px;
}
#u2861 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2861_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2862_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:13px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u2862 {
  border-width:0px;
  position:absolute;
  left:378px;
  top:595px;
  width:106px;
  height:13px;
  display:flex;
  font-size:12px;
  color:#8400FF;
  text-align:right;
}
#u2862 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2862_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2863_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:95px;
  height:11px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  text-align:right;
}
#u2863 {
  border-width:0px;
  position:absolute;
  left:389px;
  top:611px;
  width:95px;
  height:11px;
  display:flex;
  font-size:12px;
  text-align:right;
}
#u2863 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2863_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2864 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2865_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:19px;
}
#u2865 {
  border-width:0px;
  position:absolute;
  left:281px;
  top:595px;
  width:21px;
  height:19px;
  display:flex;
  font-size:16px;
}
#u2865 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2865_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2866_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:113px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u2866 {
  border-width:0px;
  position:absolute;
  left:304px;
  top:598px;
  width:113px;
  height:14px;
  display:flex;
  font-size:16px;
}
#u2866 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2866_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2867 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2868 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2869_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:214px;
  height:64px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u2869 {
  border-width:0px;
  position:absolute;
  left:21px;
  top:647px;
  width:214px;
  height:64px;
  display:flex;
  font-size:16px;
}
#u2869 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2869_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2870_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
  color:#C280FF;
}
#u2870 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:652px;
  width:106px;
  height:22px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
  color:#C280FF;
}
#u2870 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2870_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2871_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u2871 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:688px;
  width:57px;
  height:21px;
  display:flex;
  font-size:14px;
}
#u2871 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2871_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2872_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
}
#u2872 {
  border-width:0px;
  position:absolute;
  left:91px;
  top:688px;
  width:68px;
  height:21px;
  display:flex;
  font-size:14px;
}
#u2872 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2872_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2873_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#D9001B;
  text-align:right;
}
#u2873 {
  border-width:0px;
  position:absolute;
  left:172px;
  top:652px;
  width:57px;
  height:14px;
  display:flex;
  font-size:16px;
  color:#D9001B;
  text-align:right;
}
#u2873 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2873_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2874_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:13px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#8400FF;
  text-align:right;
}
#u2874 {
  border-width:0px;
  position:absolute;
  left:124px;
  top:665px;
  width:106px;
  height:13px;
  display:flex;
  font-size:14px;
  color:#8400FF;
  text-align:right;
}
#u2874 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2874_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2875_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:13px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u2875 {
  border-width:0px;
  position:absolute;
  left:172px;
  top:697px;
  width:57px;
  height:13px;
  display:flex;
  font-size:12px;
}
#u2875 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2875_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2876_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:9px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  text-align:right;
}
#u2876 {
  border-width:0px;
  position:absolute;
  left:149px;
  top:683px;
  width:80px;
  height:9px;
  display:flex;
  font-size:12px;
  text-align:right;
}
#u2876 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2876_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2877 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2878_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:19px;
}
#u2878 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:670px;
  width:21px;
  height:19px;
  display:flex;
  font-size:16px;
}
#u2878 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2878_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2879_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:113px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u2879 {
  border-width:0px;
  position:absolute;
  left:53px;
  top:671px;
  width:113px;
  height:14px;
  display:flex;
  font-size:16px;
}
#u2879 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2879_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2880 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2881_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:19px;
}
#u2881 {
  border-width:0px;
  position:absolute;
  left:281px;
  top:670px;
  width:21px;
  height:19px;
  display:flex;
  font-size:16px;
}
#u2881 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2881_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2882_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:113px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u2882 {
  border-width:0px;
  position:absolute;
  left:304px;
  top:672px;
  width:113px;
  height:14px;
  display:flex;
  font-size:16px;
}
#u2882 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2882_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2883_img {
  border-width:0px;
  position:absolute;
  left:-2px;
  top:-2px;
  width:514px;
  height:7px;
}
#u2883 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:755px;
  width:510px;
  height:3px;
  display:flex;
}
#u2883 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2883_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2884 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2885_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:90px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
}
#u2885 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:215px;
  width:90px;
  height:90px;
  display:flex;
  font-size:20px;
}
#u2885 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2885_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2886_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  text-align:center;
}
#u2886 {
  border-width:0px;
  position:absolute;
  left:35px;
  top:285px;
  width:90px;
  height:20px;
  display:flex;
  font-size:18px;
  text-align:center;
}
#u2886 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2886_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2887_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:70px;
}
#u2887 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:215px;
  width:70px;
  height:70px;
  display:flex;
}
#u2887 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2887_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2888 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2889_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:90px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
}
#u2889 {
  border-width:0px;
  position:absolute;
  left:150px;
  top:215px;
  width:90px;
  height:90px;
  display:flex;
  font-size:20px;
}
#u2889 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2889_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2890_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  text-align:center;
}
#u2890 {
  border-width:0px;
  position:absolute;
  left:150px;
  top:285px;
  width:90px;
  height:20px;
  display:flex;
  font-size:18px;
  text-align:center;
}
#u2890 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2890_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2891_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:70px;
}
#u2891 {
  border-width:0px;
  position:absolute;
  left:159px;
  top:215px;
  width:70px;
  height:70px;
  display:flex;
}
#u2891 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2891_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2892 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2893_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:90px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
}
#u2893 {
  border-width:0px;
  position:absolute;
  left:266px;
  top:215px;
  width:90px;
  height:90px;
  display:flex;
  font-size:20px;
}
#u2893 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2893_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2894_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  text-align:center;
}
#u2894 {
  border-width:0px;
  position:absolute;
  left:266px;
  top:285px;
  width:90px;
  height:20px;
  display:flex;
  font-size:18px;
  text-align:center;
}
#u2894 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2894_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2895_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:70px;
}
#u2895 {
  border-width:0px;
  position:absolute;
  left:275px;
  top:215px;
  width:70px;
  height:70px;
  display:flex;
}
#u2895 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2895_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2896 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u2897_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:90px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
}
#u2897 {
  border-width:0px;
  position:absolute;
  left:381px;
  top:215px;
  width:90px;
  height:90px;
  display:flex;
  font-size:20px;
}
#u2897 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2897_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u2898_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:18px;
  text-align:center;
}
#u2898 {
  border-width:0px;
  position:absolute;
  left:381px;
  top:285px;
  width:90px;
  height:20px;
  display:flex;
  font-size:18px;
  text-align:center;
}
#u2898 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u2898_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u2899_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:70px;
}
#u2899 {
  border-width:0px;
  position:absolute;
  left:390px;
  top:215px;
  width:70px;
  height:70px;
  display:flex;
}
#u2899 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u2899_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
