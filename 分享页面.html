﻿<!DOCTYPE html>
<html>
  <head>
    <title>分享页面</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/分享页面/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/分享页面/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u5344" class="ax_default box_1">
        <div id="u5344_div" class=""></div>
        <div id="u5344_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5345" class="ax_default box_1">
        <div id="u5345_div" class=""></div>
        <div id="u5345_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5346" class="ax_default box_1">
        <div id="u5346_div" class=""></div>
        <div id="u5346_text" class="text ">
          <p><span>分享到</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5347" class="ax_default box_1">
        <div id="u5347_div" class=""></div>
        <div id="u5347_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5348" class="ax_default box_1">
        <div id="u5348_div" class=""></div>
        <div id="u5348_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5349" class="ax_default box_1">
        <div id="u5349_div" class=""></div>
        <div id="u5349_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u5350" class="ax_default box_1">
        <div id="u5350_div" class=""></div>
        <div id="u5350_text" class="text ">
          <p><span>最近联系人</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u5351" class="ax_default" data-left="737" data-top="510" data-width="68" data-height="117">

        <!-- Unnamed (矩形) -->
        <div id="u5352" class="ax_default _文本段落">
          <div id="u5352_div" class=""></div>
          <div id="u5352_text" class="text ">
            <p><span>党军强</span></p>
          </div>
        </div>

        <!-- Unnamed (图片 ) -->
        <div id="u5353" class="ax_default _图片">
          <img id="u5353_img" class="img " src="images/分享页面/u5353.svg"/>
          <div id="u5353_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u5354" class="ax_default" data-left="842" data-top="510" data-width="68" data-height="117">

        <!-- Unnamed (矩形) -->
        <div id="u5355" class="ax_default _文本段落">
          <div id="u5355_div" class=""></div>
          <div id="u5355_text" class="text ">
            <p><span>甘伟</span></p>
          </div>
        </div>

        <!-- Unnamed (图片 ) -->
        <div id="u5356" class="ax_default _图片">
          <img id="u5356_img" class="img " src="images/分享页面/u5356.svg"/>
          <div id="u5356_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u5357" class="ax_default" data-left="945" data-top="510" data-width="68" data-height="117">

        <!-- Unnamed (矩形) -->
        <div id="u5358" class="ax_default _文本段落">
          <div id="u5358_div" class=""></div>
          <div id="u5358_text" class="text ">
            <p><span>小龙女</span></p>
          </div>
        </div>

        <!-- Unnamed (图片 ) -->
        <div id="u5359" class="ax_default _图片">
          <img id="u5359_img" class="img " src="images/分享页面/u5359.svg"/>
          <div id="u5359_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u5360" class="ax_default" data-left="737" data-top="638" data-width="68" data-height="116">

        <!-- Unnamed (组合) -->
        <div id="u5361" class="ax_default" data-left="737" data-top="638" data-width="68" data-height="67">

          <!-- Unnamed (圆形) -->
          <div id="u5362" class="ax_default ellipse">
            <img id="u5362_img" class="img " src="images/分享页面/u5362.svg"/>
            <div id="u5362_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (图片 ) -->
          <div id="u5363" class="ax_default _图片_">
            <img id="u5363_img" class="img " src="images/分享页面/u5363.png"/>
            <div id="u5363_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u5364" class="ax_default _文本段落">
          <div id="u5364_div" class=""></div>
          <div id="u5364_text" class="text ">
            <p><span>朋友圈</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u5365" class="ax_default" data-left="842" data-top="638" data-width="68" data-height="116">

        <!-- Unnamed (组合) -->
        <div id="u5366" class="ax_default" data-left="842" data-top="638" data-width="68" data-height="67">

          <!-- Unnamed (圆形) -->
          <div id="u5367" class="ax_default ellipse">
            <img id="u5367_img" class="img " src="images/分享页面/u5362.svg"/>
            <div id="u5367_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (形状) -->
          <div id="u5368" class="ax_default icon">
            <img id="u5368_img" class="img " src="images/分享页面/u5368.svg"/>
            <div id="u5368_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u5369" class="ax_default _文本段落">
          <div id="u5369_div" class=""></div>
          <div id="u5369_text" class="text ">
            <p><span>微信好友</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u5370" class="ax_default" data-left="1049" data-top="510" data-width="68" data-height="117">

        <!-- Unnamed (矩形) -->
        <div id="u5371" class="ax_default _文本段落">
          <div id="u5371_div" class=""></div>
          <div id="u5371_text" class="text ">
            <p><span>梅超风</span></p>
          </div>
        </div>

        <!-- Unnamed (图片 ) -->
        <div id="u5372" class="ax_default _图片">
          <img id="u5372_img" class="img " src="images/分享页面/u5359.svg"/>
          <div id="u5372_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u5373" class="ax_default" data-left="1153" data-top="510" data-width="68" data-height="117">

        <!-- Unnamed (矩形) -->
        <div id="u5374" class="ax_default _文本段落">
          <div id="u5374_div" class=""></div>
          <div id="u5374_text" class="text ">
            <p><span>李莫愁</span></p>
          </div>
        </div>

        <!-- Unnamed (图片 ) -->
        <div id="u5375" class="ax_default _图片">
          <img id="u5375_img" class="img " src="images/分享页面/u5359.svg"/>
          <div id="u5375_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u5376" class="ax_default" data-left="216" data-top="463" data-width="307" data-height="80">

        <!-- Unnamed (矩形) -->
        <div id="u5377" class="ax_default _文本段落">
          <div id="u5377_div" class=""></div>
          <div id="u5377_text" class="text ">
            <p><span>微信</span></p>
          </div>
        </div>

        <!-- Unnamed (图片 ) -->
        <div id="u5378" class="ax_default _图片_">
          <img id="u5378_img" class="img " src="images/分享页面/u5378.png"/>
          <div id="u5378_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (图片 ) -->
        <div id="u5379" class="ax_default _图片_">
          <img id="u5379_img" class="img " src="images/分享页面/u5379.png"/>
          <div id="u5379_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (图片 ) -->
        <div id="u5380" class="ax_default _图片_">
          <img id="u5380_img" class="img " src="images/分享页面/u5380.png"/>
          <div id="u5380_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u5381" class="ax_default _文本段落">
          <div id="u5381_div" class=""></div>
          <div id="u5381_text" class="text ">
            <p><span>QQ</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u5382" class="ax_default _文本段落">
          <div id="u5382_div" class=""></div>
          <div id="u5382_text" class="text ">
            <p><span>复制链接</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u5383" class="ax_default _图片_">
        <img id="u5383_img" class="img " src="images/充值方式/u1461.png"/>
        <div id="u5383_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
