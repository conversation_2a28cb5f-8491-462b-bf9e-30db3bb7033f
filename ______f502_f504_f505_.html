﻿<!DOCTYPE html>
<html>
  <head>
    <title>解约子钱包(F502\F504\F505)</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/______f502_f504_f505_/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/______f502_f504_f505_/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (基础app框架(H5)) -->

      <!-- Unnamed (矩形) -->
      <div id="u510" class="ax_default box_1">
        <div id="u510_div" class=""></div>
        <div id="u510_text" class="text ">
          <p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u511" class="ax_default _二级标题">
        <div id="u511_div" class=""></div>
        <div id="u511_text" class="text ">
          <p><span>16:18</span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u512" class="ax_default icon">
        <img id="u512_img" class="img " src="images/海融宝签约_个人__f501_f502_/u3.svg"/>
        <div id="u512_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u513" class="ax_default icon">
        <img id="u513_img" class="img " src="images/海融宝签约_个人__f501_f502_/u4.svg"/>
        <div id="u513_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u514" class="ax_default _文本段落">
        <div id="u514_div" class=""></div>
        <div id="u514_text" class="text ">
          <p><span>解约子钱包</span></p>
        </div>
      </div>

      <!-- 操作状态 (动态面板) -->
      <div id="u515" class="ax_default ax_default_hidden" data-label="操作状态" style="display:none; visibility: hidden">
        <div id="u515_state0" class="panel_state" data-label="操作成功" style="">
          <div id="u515_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u516" class="ax_default box_3">
              <div id="u516_div" class=""></div>
              <div id="u516_text" class="text ">
                <p><span>操作成功！</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u515_state1" class="panel_state" data-label="操作失败" style="visibility: hidden;">
          <div id="u515_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u517" class="ax_default box_3">
              <div id="u517_div" class=""></div>
              <div id="u517_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u518" class="ax_default _文本段落">
              <div id="u518_div" class=""></div>
              <div id="u518_text" class="text ">
                <p><span>操作失败！</span></p>
              </div>
            </div>

            <!-- Unnamed (图片 ) -->
            <div id="u519" class="ax_default _图片_">
              <img id="u519_img" class="img " src="images/海融宝签约_个人__f501_f502_/u10.png"/>
              <div id="u519_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u520" class="ax_default _图片_">
        <img id="u520_img" class="img " src="images/海融宝签约_个人__f501_f502_/u11.png"/>
        <div id="u520_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u521" class="ax_default icon">
        <img id="u521_img" class="img " src="images/海融宝签约_个人__f501_f502_/u12.svg"/>
        <div id="u521_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u522" class="ax_default _文本段落">
        <div id="u522_div" class=""></div>
        <div id="u522_text" class="text ">
          <p><span>hrypt.com</span></p>
        </div>
      </div>
      <div id="u509" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (矩形) -->
      <div id="u523" class="ax_default primary_button">
        <div id="u523_div" class=""></div>
        <div id="u523_text" class="text ">
          <p><span>提交解约</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u524" class="ax_default _形状">
        <div id="u524_div" class=""></div>
        <div id="u524_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u525" class="ax_default" data-left="22" data-top="294" data-width="460" data-height="177">

        <!-- Unnamed (矩形) -->
        <div id="u526" class="ax_default _形状">
          <div id="u526_div" class=""></div>
          <div id="u526_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u527" class="ax_default _文本段落">
          <div id="u527_div" class=""></div>
          <div id="u527_text" class="text ">
            <p><span>解约原因</span></p>
          </div>
        </div>

        <!-- Unnamed (文本域) -->
        <div id="u528" class="ax_default text_area">
          <div id="u528_div" class=""></div>
          <textarea id="u528_input" class="u528_input">请输入解约原因，不低于10个字。</textarea>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u529" class="ax_default _文本段落">
        <div id="u529_div" class=""></div>
        <div id="u529_text" class="text ">
          <p><span>邮储签约数字⼈⺠币钱包</span></p><p><span>钱包ID：3100 **** **** 8888</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u530" class="ax_default _文本段落">
        <div id="u530_div" class=""></div>
        <div id="u530_text" class="text ">
          <p><span>￥0.00</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u531" class="ax_default _文本段落">
        <div id="u531_div" class=""></div>
        <div id="u531_text" class="text ">
          <p><span>业务冻结资金：0.00</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u532" class="ax_default _文本段落">
        <div id="u532_div" class=""></div>
        <div id="u532_text" class="text ">
          <p><span>合约冻结资金：0.00</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u533" class="ax_default box_1">
        <div id="u533_div" class=""></div>
        <div id="u533_text" class="text ">
          <p><span>提现</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u534" class="ax_default _文本段落">
        <div id="u534_div" class=""></div>
        <div id="u534_text" class="text ">
          <p><span>提示：子钱包的金额全部清零才能申请解约子钱包</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u535" class="ax_default _文本段落">
        <div id="u535_div" class=""></div>
        <div id="u535_text" class="text ">
          <p style="font-size:20px;"><span>F504解约申请</span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span>节点属性&nbsp; &nbsp;&nbsp; 中文描述&nbsp; &nbsp;&nbsp; 数据类型&nbsp; &nbsp;&nbsp; 必输&nbsp; &nbsp;&nbsp; 说明</span></p><p style="font-size:13px;"><span>custId&nbsp; &nbsp;&nbsp; 客户Id&nbsp; &nbsp;&nbsp; Char(32)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 个人签约时生成</span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span>节点属性&nbsp; &nbsp;&nbsp; 中文描述&nbsp; &nbsp;&nbsp; 数据类型&nbsp; &nbsp;&nbsp; 必输&nbsp; &nbsp;&nbsp; 说明</span></p><p style="font-size:13px;"><span>mbno&nbsp; &nbsp;&nbsp; 手机号&nbsp; &nbsp;&nbsp; char(24)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 脱敏展示，接受短信验证码的手机号</span></p><p style="font-size:13px;"><span>sessNo&nbsp; &nbsp;&nbsp; 会话编号&nbsp; &nbsp;&nbsp; char(64)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; 用于会员解约接口。bdlStaCd为PR00时，有值</span></p><p style="font-size:13px;"><span>bdlStaCd&nbsp; &nbsp;&nbsp; 业务处理状态代码&nbsp; &nbsp;&nbsp; char(4)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; PR00-成功；PR01-失败</span></p><p style="font-size:13px;"><span>failReason&nbsp; &nbsp;&nbsp; 失败原因&nbsp; &nbsp;&nbsp; char(500)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; 当业务处理状态代码为PR01-失败时返回</span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span><br></span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u536" class="ax_default _文本段落">
        <div id="u536_div" class=""></div>
        <div id="u536_text" class="text ">
          <p style="font-size:20px;"><span>F505会员解约</span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span>节点属性&nbsp; &nbsp;&nbsp; 中文描述&nbsp; &nbsp;&nbsp; 数据类型&nbsp; &nbsp;&nbsp; 必输&nbsp; &nbsp;&nbsp; 说明</span></p><p style="font-size:13px;"><span>custId&nbsp; &nbsp;&nbsp; 客户ID&nbsp; &nbsp;&nbsp; Char(32)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 个人签约时生成</span></p><p style="font-size:13px;"><span>sessNo&nbsp; &nbsp;&nbsp; 会话编号&nbsp; &nbsp;&nbsp; Char(64)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 会员解约申请获取</span></p><p style="font-size:13px;"><span>verifyCode&nbsp; &nbsp;&nbsp; 短信验证码&nbsp; &nbsp;&nbsp; Char(6)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 会员解约申请获取</span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span>节点属性&nbsp; &nbsp;&nbsp; 中文描述&nbsp; &nbsp;&nbsp; 数据类型&nbsp; &nbsp;&nbsp; 必输&nbsp; &nbsp;&nbsp; 说明</span></p><p style="font-size:13px;"><span>bdlStaCd&nbsp; &nbsp;&nbsp; 业务处理状态代码&nbsp; &nbsp;&nbsp; char(4)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; PR00-成功；PR01-失败；PR02-处理中</span></p><p style="font-size:13px;"><span>failReason&nbsp; &nbsp;&nbsp; 失败原因&nbsp; &nbsp;&nbsp; char(500)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; 当业务处理状态代码为PR01-失败时返回</span></p><p style="font-size:13px;"><span><br></span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u537" class="ax_default" data-left="40" data-top="514" data-width="424" data-height="36">

        <!-- Unnamed (组合) -->
        <div id="u538" class="ax_default" data-left="147" data-top="514" data-width="317" data-height="36">

          <!-- Unnamed (文本框) -->
          <div id="u539" class="ax_default text_field">
            <div id="u539_div" class=""></div>
            <input id="u539_input" type="text" value="" class="u539_input"/>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u540" class="ax_default" data-left="158" data-top="518" data-width="294" data-height="30">

            <!-- 叫号面板按钮 (动态面板) -->
            <div id="u541" class="ax_default" data-label="叫号面板按钮">
              <div id="u541_state0" class="panel_state" data-label="State1" style="">
                <div id="u541_state0_content" class="panel_state_content">

                  <!-- Unnamed (矩形) -->
                  <div id="u542" class="ax_default box_3">
                    <div id="u542_div" class=""></div>
                    <div id="u542_text" class="text ">
                      <p><span>获取验证码</span></p>
                    </div>
                  </div>
                </div>
              </div>
              <div id="u541_state1" class="panel_state" data-label="State2" style="visibility: hidden;">
                <div id="u541_state1_content" class="panel_state_content">

                  <!-- Unnamed (矩形) -->
                  <div id="u543" class="ax_default box_3">
                    <div id="u543_div" class=""></div>
                    <div id="u543_text" class="text ">
                      <p><span>s</span></p>
                    </div>
                  </div>

                  <!-- 叫号倒计时 (文本框) -->
                  <div id="u544" class="ax_default text_field ax_default_hidden" data-label="叫号倒计时" style="display:none; visibility: hidden">
                    <div id="u544_div" class=""></div>
                    <input id="u544_input" type="text" value="15" class="u544_input" readonly/>
                  </div>
                </div>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u545" class="ax_default _文本段落">
              <div id="u545_div" class=""></div>
              <div id="u545_text" class="text ">
                <p><span>输入短信验证码</span></p>
              </div>
            </div>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u546" class="ax_default _文本段落">
          <div id="u546_div" class=""></div>
          <div id="u546_text" class="text ">
            <p><span>解约确认</span></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u547" class="ax_default _文本段落">
        <div id="u547_div" class=""></div>
        <div id="u547_text" class="text ">
          <p style="font-size:20px;"><span>F502 会员签约查询</span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span>节点属性&nbsp; &nbsp;&nbsp; 中文描述&nbsp; &nbsp;&nbsp; 数据类型&nbsp; &nbsp;&nbsp; 必输&nbsp; &nbsp;&nbsp; 说明</span></p><p style="font-size:13px;"><span>platCustId&nbsp; &nbsp;&nbsp; 平台客户ID&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 平台给会员分配的ID，会员注册登陆平台系统的ID</span></p><p style="font-size:13px;"><span>custIdType&nbsp; &nbsp;&nbsp; 证件类型&nbsp; &nbsp;&nbsp; char(4)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 只支持个人&nbsp; &nbsp; 证件类型：IT01:居民身份证；IT02:军官证；IT03:护照；IT04-户口薄；IT05-士兵证；IT06-港澳往来内地通行证；IT07-台湾同胞来往内地通行证；IT08-临时身份证；IT09-外国人居留证</span></p><p style="font-size:13px;"><span>custIdNo&nbsp; &nbsp;&nbsp; 证件号码&nbsp; &nbsp;&nbsp; char(30)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 证件号码</span></p><p style="font-size:13px;"><span>custName&nbsp; &nbsp;&nbsp; 客户名称&nbsp; &nbsp;&nbsp; char(100)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 证件名称</span></p><p style="font-size:13px;"><span>custType&nbsp; &nbsp;&nbsp; 客户类型&nbsp; &nbsp;&nbsp; char(2)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; A-个人</span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span><br></span></p><p style="font-size:13px;"><span>节点属性&nbsp; &nbsp;&nbsp; 中文描述&nbsp; &nbsp;&nbsp; 数据类型&nbsp; &nbsp;&nbsp; 必输&nbsp; &nbsp;&nbsp; 说明</span></p><p style="font-size:13px;"><span>platCustId&nbsp; &nbsp;&nbsp; 平台客户ID&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 平台给会员分配的ID，会员注册登陆平台系统的ID</span></p><p style="font-size:13px;"><span>signContStatus&nbsp; &nbsp;&nbsp; 签约状态&nbsp; &nbsp;&nbsp; char(2)&nbsp; &nbsp;&nbsp; Y&nbsp; &nbsp;&nbsp; 0-已签约；1-未签约；2-签约处理中；</span></p><p style="font-size:13px;"><span>custId&nbsp; &nbsp;&nbsp; 客户id&nbsp; &nbsp;&nbsp; char(32)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; 个人签约时生成的ID</span></p><p style="font-size:13px;"><span>signAgrNo&nbsp; &nbsp;&nbsp; 签约协议号&nbsp; &nbsp;&nbsp; char(64)&nbsp; &nbsp;&nbsp; N&nbsp; &nbsp;&nbsp; 银行生成的签约协议号</span></p><p style="font-size:13px;"><span><br></span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u548" class="ax_default _文本段落">
        <div id="u548_div" class=""></div>
        <div id="u548_text" class="text ">
          <p><span>邮储银行子钱包ID：3100 1234 9876 8888</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
