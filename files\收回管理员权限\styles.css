﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:510px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u3895_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:900px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:50px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3895 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:510px;
  height:900px;
  display:flex;
  opacity:0.49;
}
#u3895 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3895_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3896 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3897 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3898_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u3898 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:834px;
  width:26px;
  height:25px;
  display:flex;
}
#u3898 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3898_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3899_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u3899 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:860px;
  width:26px;
  height:14px;
  display:flex;
  text-align:center;
}
#u3899 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3899_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3900 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3901_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u3901 {
  border-width:0px;
  position:absolute;
  left:130px;
  top:834px;
  width:26px;
  height:25px;
  display:flex;
}
#u3901 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3901_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3902_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u3902 {
  border-width:0px;
  position:absolute;
  left:130px;
  top:860px;
  width:26px;
  height:14px;
  display:flex;
  text-align:center;
}
#u3902 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3902_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3903 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3904_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u3904 {
  border-width:0px;
  position:absolute;
  left:345px;
  top:834px;
  width:26px;
  height:25px;
  display:flex;
}
#u3904 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3904_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3905_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  background-image:url('../../images/平台首页/u2795.png');
  background-repeat:no-repeat;
  background-size:200px 200px;
  background-position: left top;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u3905 {
  border-width:0px;
  position:absolute;
  left:345px;
  top:860px;
  width:26px;
  height:14px;
  display:flex;
  text-align:center;
}
#u3905 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3905_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3906 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3907_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u3907 {
  border-width:0px;
  position:absolute;
  left:452px;
  top:834px;
  width:26px;
  height:25px;
  display:flex;
}
#u3907 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3907_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3908_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u3908 {
  border-width:0px;
  position:absolute;
  left:452px;
  top:860px;
  width:26px;
  height:14px;
  display:flex;
  text-align:center;
}
#u3908 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3908_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3909 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3910_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u3910 {
  border-width:0px;
  position:absolute;
  left:237px;
  top:834px;
  width:26px;
  height:25px;
  display:flex;
}
#u3910 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3910_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3911_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:44px;
  height:14px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:center;
}
#u3911 {
  border-width:0px;
  position:absolute;
  left:228px;
  top:860px;
  width:44px;
  height:14px;
  display:flex;
  text-align:center;
}
#u3911 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3911_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3912_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:15px;
  background:inherit;
  background-color:rgba(255, 0, 0, 1);
  box-sizing:border-box;
  border-width:2px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:75px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u3912 {
  border-width:0px;
  position:absolute;
  left:363px;
  top:834px;
  width:21px;
  height:15px;
  display:flex;
  color:#FFFFFF;
}
#u3912 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3912_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3913_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:40px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u3913 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:20px;
  width:51px;
  height:40px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u3913 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3913_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3914_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:18px;
}
#u3914 {
  border-width:0px;
  position:absolute;
  left:425px;
  top:19px;
  width:23px;
  height:18px;
  display:flex;
}
#u3914 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3914_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3915_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:16px;
}
#u3915 {
  border-width:0px;
  position:absolute;
  left:462px;
  top:21px;
  width:26px;
  height:16px;
  display:flex;
}
#u3915 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3915_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3916_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:24px;
  height:25px;
}
#u3916 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:50px;
  width:24px;
  height:25px;
  display:flex;
}
#u3916 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3916_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3917_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:20px;
  text-align:center;
}
#u3917 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:49px;
  width:300px;
  height:25px;
  display:flex;
  font-size:20px;
  text-align:center;
}
#u3917 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3917_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3918 {
  position:fixed;
  left:50%;
  margin-left:-75px;
  top:50%;
  margin-top:-25px;
  visibility:hidden;
}
#u3918_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3918_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u3919_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(0, 0, 0, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u3919 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u3919 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u3919_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3918_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u3918_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u3920_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.498039215686275);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u3920 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:50px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u3920 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 10px;
  box-sizing:border-box;
  width:100%;
}
#u3920_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3921_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u3921 {
  border-width:0px;
  position:absolute;
  left:60px;
  top:16px;
  width:80px;
  height:18px;
  display:flex;
  font-size:16px;
  color:#A30014;
  text-align:center;
}
#u3921 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3921_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3922_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:30px;
}
#u3922 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:10px;
  width:30px;
  height:30px;
  display:flex;
}
#u3922 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3922_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3923_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:228px;
  height:11px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  text-align:center;
}
#u3923 {
  border-width:0px;
  position:absolute;
  left:136px;
  top:71px;
  width:228px;
  height:11px;
  display:flex;
  font-size:10px;
  text-align:center;
}
#u3923 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3923_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3924 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:44px;
  width:39px;
  height:41px;
  overflow:hidden;
  background-image:url('../../resources/images/transparent.gif');
}
#u3925 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u3926 {
  border-width:0px;
  position:absolute;
  left:11px;
  top:129px;
  width:478px;
  height:190px;
}
#u3927_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:30px;
}
#u3927 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u3927 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3927_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3928_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:30px;
}
#u3928 {
  border-width:0px;
  position:absolute;
  left:98px;
  top:0px;
  width:78px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u3928 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3928_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3929_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:216px;
  height:30px;
}
#u3929 {
  border-width:0px;
  position:absolute;
  left:176px;
  top:0px;
  width:216px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u3929 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3929_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3930_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:30px;
}
#u3930 {
  border-width:0px;
  position:absolute;
  left:392px;
  top:0px;
  width:86px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u3930 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3930_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3931_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
}
#u3931 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:30px;
  width:98px;
  height:40px;
  display:flex;
  font-size:16px;
}
#u3931 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3931_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3932_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:40px;
}
#u3932 {
  border-width:0px;
  position:absolute;
  left:98px;
  top:30px;
  width:78px;
  height:40px;
  display:flex;
  font-size:16px;
}
#u3932 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3932_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3933_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:216px;
  height:40px;
}
#u3933 {
  border-width:0px;
  position:absolute;
  left:176px;
  top:30px;
  width:216px;
  height:40px;
  display:flex;
  font-size:16px;
  text-align:left;
}
#u3933 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3933_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3934_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:40px;
}
#u3934 {
  border-width:0px;
  position:absolute;
  left:392px;
  top:30px;
  width:86px;
  height:40px;
  display:flex;
  font-size:16px;
  color:#8400FF;
}
#u3934 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3934_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3935_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
}
#u3935 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:70px;
  width:98px;
  height:40px;
  display:flex;
  font-size:16px;
}
#u3935 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3935_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3936_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:40px;
}
#u3936 {
  border-width:0px;
  position:absolute;
  left:98px;
  top:70px;
  width:78px;
  height:40px;
  display:flex;
  font-size:16px;
  color:#000000;
  text-align:left;
}
#u3936 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3936_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3937_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:216px;
  height:40px;
}
#u3937 {
  border-width:0px;
  position:absolute;
  left:176px;
  top:70px;
  width:216px;
  height:40px;
  display:flex;
  font-size:16px;
  text-align:left;
}
#u3937 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3937_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3938_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:40px;
}
#u3938 {
  border-width:0px;
  position:absolute;
  left:392px;
  top:70px;
  width:86px;
  height:40px;
  display:flex;
  font-size:16px;
  color:#8400FF;
}
#u3938 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3938_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3939_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
}
#u3939 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:110px;
  width:98px;
  height:40px;
  display:flex;
  font-size:16px;
}
#u3939 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3939_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3940_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:40px;
}
#u3940 {
  border-width:0px;
  position:absolute;
  left:98px;
  top:110px;
  width:78px;
  height:40px;
  display:flex;
  font-size:16px;
  color:#000000;
  text-align:left;
}
#u3940 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3940_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3941_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:216px;
  height:40px;
}
#u3941 {
  border-width:0px;
  position:absolute;
  left:176px;
  top:110px;
  width:216px;
  height:40px;
  display:flex;
  font-size:16px;
  text-align:left;
}
#u3941 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3941_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3942_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:40px;
}
#u3942 {
  border-width:0px;
  position:absolute;
  left:392px;
  top:110px;
  width:86px;
  height:40px;
  display:flex;
  font-size:16px;
  color:#8400FF;
}
#u3942 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3942_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3943_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:40px;
}
#u3943 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:150px;
  width:98px;
  height:40px;
  display:flex;
  font-size:16px;
}
#u3943 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3943_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3944_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:40px;
}
#u3944 {
  border-width:0px;
  position:absolute;
  left:98px;
  top:150px;
  width:78px;
  height:40px;
  display:flex;
  font-size:16px;
  color:#000000;
  text-align:left;
}
#u3944 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3944_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3945_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:216px;
  height:40px;
}
#u3945 {
  border-width:0px;
  position:absolute;
  left:176px;
  top:150px;
  width:216px;
  height:40px;
  display:flex;
  font-size:16px;
  text-align:left;
}
#u3945 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3945_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3946_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:40px;
}
#u3946 {
  border-width:0px;
  position:absolute;
  left:392px;
  top:150px;
  width:86px;
  height:40px;
  display:flex;
  font-size:16px;
  color:#8400FF;
}
#u3946 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3946_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3947_input {
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:29px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3947_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:29px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3947_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:29px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u3947 {
  border-width:0px;
  position:absolute;
  left:112px;
  top:165px;
  width:74px;
  height:29px;
  display:flex;
  font-size:16px;
}
#u3947 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3947_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:29px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u3947.disabled {
}
.u3947_input_option {
  font-size:16px;
}
#u3948_input {
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:29px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3948_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:29px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3948_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:29px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u3948 {
  border-width:0px;
  position:absolute;
  left:112px;
  top:205px;
  width:74px;
  height:29px;
  display:flex;
  font-size:16px;
}
#u3948 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3948_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:29px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u3948.disabled {
}
.u3948_input_option {
  font-size:16px;
}
#u3949_input {
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:29px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3949_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:29px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3949_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:29px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u3949 {
  border-width:0px;
  position:absolute;
  left:112px;
  top:245px;
  width:74px;
  height:29px;
  display:flex;
  font-size:16px;
}
#u3949 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3949_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:29px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u3949.disabled {
}
.u3949_input_option {
  font-size:16px;
}
#u3950_input {
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:29px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3950_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:29px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u3950_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:29px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u3950 {
  border-width:0px;
  position:absolute;
  left:112px;
  top:284px;
  width:74px;
  height:29px;
  display:flex;
  font-size:16px;
}
#u3950 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3950_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:29px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u3950.disabled {
}
.u3950_input_option {
  font-size:16px;
}
#u3951 {
  border-width:0px;
  position:absolute;
  left:403px;
  top:160px;
  width:86px;
  height:159px;
  overflow:hidden;
  background-image:url('../../resources/images/transparent.gif');
}
#u3952_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:218px;
  height:25px;
  background:inherit;
  background-color:rgba(22, 155, 213, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3952 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:357px;
  width:218px;
  height:25px;
  display:flex;
}
#u3952 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3952_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
