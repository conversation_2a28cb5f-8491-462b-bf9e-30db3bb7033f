﻿<!DOCTYPE html>
<html>
  <head>
    <title>变更绑定手机</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/变更绑定手机/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/变更绑定手机/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (基础APP框架) -->

      <!-- Unnamed (矩形) -->
      <div id="u4593" class="ax_default box_1">
        <div id="u4593_div" class=""></div>
        <div id="u4593_text" class="text ">
          <p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p><p></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u4594" class="ax_default" data-left="22" data-top="834" data-width="456" data-height="41">

        <!-- Unnamed (组合) -->
        <div id="u4595" class="ax_default" data-left="22" data-top="834" data-width="26" data-height="41">

          <!-- Unnamed (图片 ) -->
          <div id="u4596" class="ax_default _图片">
            <img id="u4596_img" class="img " src="images/平台首页/u2789.png"/>
            <div id="u4596_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u4597" class="ax_default _文本段落">
            <div id="u4597_div" class=""></div>
            <div id="u4597_text" class="text ">
              <p><span>首页</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u4598" class="ax_default" data-left="130" data-top="834" data-width="26" data-height="41">

          <!-- Unnamed (图片 ) -->
          <div id="u4599" class="ax_default _图片">
            <img id="u4599_img" class="img " src="images/平台首页/u2792.png"/>
            <div id="u4599_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u4600" class="ax_default _文本段落">
            <div id="u4600_div" class=""></div>
            <div id="u4600_text" class="text ">
              <p><span>融资</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u4601" class="ax_default" data-left="345" data-top="834" data-width="26" data-height="41">

          <!-- Unnamed (图片 ) -->
          <div id="u4602" class="ax_default _图片">
            <img id="u4602_img" class="img " src="images/平台首页/u2795.png"/>
            <div id="u4602_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u4603" class="ax_default _文本段落">
            <div id="u4603_div" class=""></div>
            <div id="u4603_text" class="text ">
              <p><span>消息</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u4604" class="ax_default" data-left="452" data-top="834" data-width="26" data-height="41">

          <!-- Unnamed (图片 ) -->
          <div id="u4605" class="ax_default _图片">
            <img id="u4605_img" class="img " src="images/平台首页/u2798.png"/>
            <div id="u4605_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u4606" class="ax_default _文本段落">
            <div id="u4606_div" class=""></div>
            <div id="u4606_text" class="text ">
              <p><span>我的</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u4607" class="ax_default" data-left="228" data-top="834" data-width="44" data-height="41">

          <!-- Unnamed (图片 ) -->
          <div id="u4608" class="ax_default _图片">
            <img id="u4608_img" class="img " src="images/平台首页/u2801.png"/>
            <div id="u4608_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u4609" class="ax_default _文本段落">
            <div id="u4609_div" class=""></div>
            <div id="u4609_text" class="text ">
              <p><span>发现</span></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4610" class="ax_default box_1">
        <div id="u4610_div" class=""></div>
        <div id="u4610_text" class="text ">
          <p><span>3</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4611" class="ax_default _二级标题">
        <div id="u4611_div" class=""></div>
        <div id="u4611_text" class="text ">
          <p><span>16:18</span></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u4612" class="ax_default icon">
        <img id="u4612_img" class="img " src="images/海融宝签约_个人__f501_f502_/u3.svg"/>
        <div id="u4612_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u4613" class="ax_default icon">
        <img id="u4613_img" class="img " src="images/海融宝签约_个人__f501_f502_/u4.svg"/>
        <div id="u4613_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u4614" class="ax_default _图片">
        <img id="u4614_img" class="img " src="images/个人开结算账户（申请）/u2269.png"/>
        <div id="u4614_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4615" class="ax_default _文本段落">
        <div id="u4615_div" class=""></div>
        <div id="u4615_text" class="text ">
          <p><span>变更绑定手机</span></p>
        </div>
      </div>

      <!-- 操作状态 (动态面板) -->
      <div id="u4616" class="ax_default ax_default_hidden" data-label="操作状态" style="display:none; visibility: hidden">
        <div id="u4616_state0" class="panel_state" data-label="操作成功" style="">
          <div id="u4616_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u4617" class="ax_default box_3">
              <div id="u4617_div" class=""></div>
              <div id="u4617_text" class="text ">
                <p><span>操作成功！</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u4616_state1" class="panel_state" data-label="操作失败" style="visibility: hidden;">
          <div id="u4616_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u4618" class="ax_default box_3">
              <div id="u4618_div" class=""></div>
              <div id="u4618_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u4619" class="ax_default _文本段落">
              <div id="u4619_div" class=""></div>
              <div id="u4619_text" class="text ">
                <p><span>操作失败！</span></p>
              </div>
            </div>

            <!-- Unnamed (图片 ) -->
            <div id="u4620" class="ax_default _图片_">
              <img id="u4620_img" class="img " src="images/海融宝签约_个人__f501_f502_/u10.png"/>
              <div id="u4620_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4621" class="ax_default _文本段落">
        <div id="u4621_div" class=""></div>
        <div id="u4621_text" class="text ">
          <p><span>hrypt.com</span></p>
        </div>
      </div>
      <div id="u4592" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (手机和验证码输入) -->

      <!-- Unnamed (矩形) -->
      <div id="u4623" class="ax_default _形状">
        <div id="u4623_div" class=""></div>
        <div id="u4623_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4624" class="ax_default _文本段落">
        <div id="u4624_div" class=""></div>
        <div id="u4624_text" class="text ">
          <p><span>原手机</span></p>
        </div>
      </div>

      <!-- 叫号面板按钮 (动态面板) -->
      <div id="u4625" class="ax_default" data-label="叫号面板按钮">
        <div id="u4625_state0" class="panel_state" data-label="State1" style="">
          <div id="u4625_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u4626" class="ax_default box_3">
              <div id="u4626_div" class=""></div>
              <div id="u4626_text" class="text ">
                <p><span>获取验证码</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u4625_state1" class="panel_state" data-label="State2" style="visibility: hidden;">
          <div id="u4625_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u4627" class="ax_default box_3">
              <div id="u4627_div" class=""></div>
              <div id="u4627_text" class="text ">
                <p><span>s</span></p>
              </div>
            </div>

            <!-- 叫号倒计时 (文本框) -->
            <div id="u4628" class="ax_default text_field ax_default_hidden" data-label="叫号倒计时" style="display:none; visibility: hidden">
              <div id="u4628_div" class=""></div>
              <input id="u4628_input" type="text" value="15" class="u4628_input" readonly/>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u4629" class="ax_default text_field">
        <div id="u4629_div" class=""></div>
        <input id="u4629_input" type="text" value="" class="u4629_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4630" class="ax_default _文本段落">
        <div id="u4630_div" class=""></div>
        <div id="u4630_text" class="text ">
          <p><span>134 8250 8234</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4631" class="ax_default _文本段落">
        <div id="u4631_div" class=""></div>
        <div id="u4631_text" class="text ">
          <p><span>验证码</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u4632" class="ax_default text_field">
        <div id="u4632_div" class=""></div>
        <input id="u4632_input" type="text" value="" class="u4632_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4633" class="ax_default _文本段落">
        <div id="u4633_div" class=""></div>
        <div id="u4633_text" class="text ">
          <p><span>输入短信验证码</span></p>
        </div>
      </div>
      <div id="u4622" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (手机和验证码输入) -->

      <!-- Unnamed (矩形) -->
      <div id="u4635" class="ax_default _形状">
        <div id="u4635_div" class=""></div>
        <div id="u4635_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4636" class="ax_default _文本段落">
        <div id="u4636_div" class=""></div>
        <div id="u4636_text" class="text ">
          <p><span>新手机</span></p>
        </div>
      </div>

      <!-- 叫号面板按钮 (动态面板) -->
      <div id="u4637" class="ax_default" data-label="叫号面板按钮">
        <div id="u4637_state0" class="panel_state" data-label="State1" style="">
          <div id="u4637_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u4638" class="ax_default box_3">
              <div id="u4638_div" class=""></div>
              <div id="u4638_text" class="text ">
                <p><span>获取验证码</span></p>
              </div>
            </div>
          </div>
        </div>
        <div id="u4637_state1" class="panel_state" data-label="State2" style="visibility: hidden;">
          <div id="u4637_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u4639" class="ax_default box_3">
              <div id="u4639_div" class=""></div>
              <div id="u4639_text" class="text ">
                <p><span>s</span></p>
              </div>
            </div>

            <!-- 叫号倒计时 (文本框) -->
            <div id="u4640" class="ax_default text_field ax_default_hidden" data-label="叫号倒计时" style="display:none; visibility: hidden">
              <div id="u4640_div" class=""></div>
              <input id="u4640_input" type="text" value="15" class="u4640_input" readonly/>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u4641" class="ax_default text_field">
        <div id="u4641_div" class=""></div>
        <input id="u4641_input" type="text" value="" class="u4641_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4642" class="ax_default _文本段落">
        <div id="u4642_div" class=""></div>
        <div id="u4642_text" class="text ">
          <p><span>输入新手机号</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4643" class="ax_default _文本段落">
        <div id="u4643_div" class=""></div>
        <div id="u4643_text" class="text ">
          <p><span>验证码</span></p>
        </div>
      </div>

      <!-- Unnamed (文本框) -->
      <div id="u4644" class="ax_default text_field">
        <div id="u4644_div" class=""></div>
        <input id="u4644_input" type="text" value="" class="u4644_input"/>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4645" class="ax_default _文本段落">
        <div id="u4645_div" class=""></div>
        <div id="u4645_text" class="text ">
          <p><span>输入短信验证码</span></p>
        </div>
      </div>
      <div id="u4634" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (矩形) -->
      <div id="u4646" class="ax_default primary_button">
        <div id="u4646_div" class=""></div>
        <div id="u4646_text" class="text ">
          <p><span>确认变更</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u4647" class="ax_default _文本段落">
        <div id="u4647_div" class=""></div>
        <div id="u4647_text" class="text ">
          <p><span>&nbsp;&nbsp; &nbsp;&nbsp; 说明：变更绑定手机号，将影响本平台所有会员关联的手机号信息，同时所有业务短信验证也将发送到新手机号，原手机号的登陆信息也将自动解绑。</span></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
